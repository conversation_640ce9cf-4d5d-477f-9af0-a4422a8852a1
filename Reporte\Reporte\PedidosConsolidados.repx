﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="AuditoriaDescuentos" Margins="100, 100, 212, 100" PageWidth="850" PageHeight="1100" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" HeightF="212">
      <Controls>
        <Item1 Ref="2" ControlType="XRLabel" Name="labelEmpresa" Multiline="true" Text="labelEmpresa" TextAlignment="MiddleCenter" WordWrap="false" SizeF="650,50.0000038" LocationFloat="0,48.9583321" Font="Arial, 14.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="3" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="4" ControlType="XRLabel" Name="labelUsuario" Multiline="true" Text="labelUsuario&#xD;&#xA;" TextAlignment="MiddleRight" WordWrap="false" SizeF="151.041656,21.8749962" LocationFloat="498.9584,10.0000067" Padding="2,2,0,0,100">
          <StylePriority Ref="5" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="6" ControlType="XRLabel" Name="labelFecha" Multiline="true" Text="labelFecha" TextAlignment="MiddleLeft" WordWrap="false" SizeF="259.375,21.8749981" LocationFloat="0,10.0000067" Padding="2,2,0,0,100">
          <StylePriority Ref="7" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="8" ControlType="XRLabel" Name="label2" Multiline="true" Text="Pedido Consolidado de Alimentos" TextAlignment="MiddleCenter" WordWrap="false" SizeF="650,30.2083359" LocationFloat="0,98.9583359" Font="Arial, 14.25pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="9" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="10" ControlType="XRLabel" Name="pedidoConsolidado" KeepTogether="true" CanGrow="false" TextTrimming="None" Text="Pedidos Consolidados:" WordWrap="false" SizeF="650,31.3749542" LocationFloat="0,180.625031" Padding="2,2,0,0,100" />
      </Controls>
    </Item1>
    <Item2 Ref="11" ControlType="BottomMarginBand" Name="BottomMargin" />
    <Item3 Ref="12" ControlType="DetailBand" Name="Detail" HeightF="27.083334">
      <Controls>
        <Item1 Ref="13" ControlType="XRTable" Name="table1" SizeF="650,27.083334" LocationFloat="0,0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="14" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="15" ControlType="XRTableCell" Name="tableCell1" Weight="0.36057684516967325" Multiline="true" WordWrap="false">
                  <ExpressionBindings>
                    <Item1 Ref="16" EventName="BeforePrint" PropertyName="Text" Expression="ToInt([Producto])" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="17" ControlType="XRTableCell" Name="tableCell3" Weight="0.83653859137188258" Multiline="true" WordWrap="false">
                  <ExpressionBindings>
                    <Item1 Ref="18" EventName="BeforePrint" PropertyName="Text" Expression="[Nombre]" />
                  </ExpressionBindings>
                </Item2>
                <Item3 Ref="19" ControlType="XRTableCell" Name="tableCell6" Weight="0.39423046740077072" TextFormatString="{0:C2}" Multiline="true" TextAlignment="TopCenter" WordWrap="false">
                  <ExpressionBindings>
                    <Item1 Ref="20" EventName="BeforePrint" PropertyName="Text" Expression="[Descripcion]" />
                  </ExpressionBindings>
                  <StylePriority Ref="21" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="22" ControlType="XRTableCell" Name="tableCell7" Weight="0.2355774915629873" Multiline="true" WordWrap="false">
                  <ExpressionBindings>
                    <Item1 Ref="23" EventName="BeforePrint" PropertyName="Text" Expression="[Total]" />
                  </ExpressionBindings>
                </Item4>
                <Item5 Ref="24" ControlType="XRTableCell" Name="tableCell16" Weight="0.47596163894632687" TextFormatString="{0:C2}" Multiline="true" TextAlignment="TopRight" WordWrap="false">
                  <ExpressionBindings>
                    <Item1 Ref="25" EventName="BeforePrint" PropertyName="Text" Expression="[CostoUnitario]" />
                  </ExpressionBindings>
                  <StylePriority Ref="26" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="27" ControlType="XRTableCell" Name="tableCell18" Weight="0.69711542196987986" TextFormatString="{0:C2}" Multiline="true" TextAlignment="TopRight" WordWrap="false">
                  <ExpressionBindings>
                    <Item1 Ref="28" EventName="BeforePrint" PropertyName="Text" Expression="[TotalConsolidado]" />
                  </ExpressionBindings>
                  <StylePriority Ref="29" UseTextAlignment="false" />
                </Item6>
              </Cells>
            </Item1>
          </Rows>
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="30" ControlType="PageHeaderBand" Name="PageHeader" HeightF="0">
      <SubBands>
        <Item1 Ref="31" ControlType="SubBand" Name="SubBand1" HeightF="38.5416679">
          <Controls>
            <Item1 Ref="32" ControlType="XRTable" Name="table2" TextAlignment="MiddleLeft" SizeF="650,38.5416679" LocationFloat="0,0" Padding="2,2,0,0,96" Borders="Top, Bottom">
              <Rows>
                <Item1 Ref="33" ControlType="XRTableRow" Name="tableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="34" ControlType="XRTableCell" Name="tableCell8" Weight="0.36057684516967325" Multiline="true" Text="Producto" TextAlignment="MiddleLeft" WordWrap="false" Font="Arial, 9.75pt, style=Bold, charSet=0" ForeColor="DarkBlue" BackColor="SkyBlue">
                      <StylePriority Ref="35" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="36" ControlType="XRTableCell" Name="tableCell9" Weight="0.83653864421652757" Multiline="true" Text="Nombre" TextAlignment="MiddleLeft" WordWrap="false" Font="Arial, 9.75pt, style=Bold, charSet=0" ForeColor="DarkBlue" BackColor="SkyBlue">
                      <StylePriority Ref="37" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item2>
                    <Item3 Ref="38" ControlType="XRTableCell" Name="tableCell10" Weight="0.39423053406157471" Multiline="true" Text="Descripción" TextAlignment="MiddleLeft" WordWrap="false" Font="Arial, 9.75pt, style=Bold, charSet=0" ForeColor="DarkBlue" BackColor="SkyBlue">
                      <StylePriority Ref="39" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item3>
                    <Item4 Ref="40" ControlType="XRTableCell" Name="tableCell11" Weight="0.23557734148872711" Multiline="true" Text="Total&#xD;&#xA;" TextAlignment="MiddleLeft" WordWrap="false" Font="Arial, 9.75pt, style=Bold, charSet=0" ForeColor="DarkBlue" BackColor="SkyBlue">
                      <StylePriority Ref="41" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item4>
                    <Item5 Ref="42" ControlType="XRTableCell" Name="tableCell12" Weight="0.47596166164851061" Multiline="true" Text="Costo Unitario" TextAlignment="MiddleRight" WordWrap="false" Font="Arial, 9.75pt, style=Bold, charSet=0" ForeColor="DarkBlue" BackColor="SkyBlue">
                      <StylePriority Ref="43" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item5>
                    <Item6 Ref="44" ControlType="XRTableCell" Name="tableCell13" Weight="0.69711560354130464" Multiline="true" Text="Total Consolidado" TextAlignment="MiddleRight" WordWrap="false" Font="Arial, 9.75pt, style=Bold, charSet=0" ForeColor="DarkBlue" BackColor="SkyBlue">
                      <StylePriority Ref="45" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item6>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="46" UseBorders="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </SubBands>
    </Item4>
  </Bands>
</XtraReportsLayoutSerializer>