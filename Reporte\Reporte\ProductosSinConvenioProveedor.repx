﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="ProductosSinConvenioProveedor" Landscape="true" Margins="100, 100, 130, 14" PageWidth="1100" PageHeight="850" Version="20.1">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="topMarginBand1" HeightF="130.375">
      <Controls>
        <Item1 Ref="2" ControlType="XRTable" Name="table2" TextAlignment="BottomLeft" SizeF="747.9165,32.2916679" LocationFloat="0,98.0833359" Font="Arial, 8.25pt, style=Bold, charSet=0" BackColor="SkyBlue" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="3" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="4" ControlType="XRTableCell" Name="tableCell13" Weight="0.52835049671951584" Multiline="true" Text="Codigo" />
                <Item2 Ref="5" ControlType="XRTableCell" Name="tableCell14" Weight="3.5747442725314205" Multiline="true" Text="Nombre Producto" />
                <Item3 Ref="6" ControlType="XRTableCell" Name="tableCell1" Weight="0.72164727870418965" Multiline="true" Text="Precio Convenio" />
                <Item4 Ref="7" ControlType="XRTableCell" Name="tableCell15" Weight="0.7268032583792754" Multiline="true" Text="Costo Ultimo" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="8" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="9" ControlType="XRLabel" Name="labelProveedor" Multiline="true" TextAlignment="BottomLeft" SizeF="552.777954,27.08333" LocationFloat="0,54.1666679" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="10" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="11" ControlType="XRLabel" Name="label1" Multiline="true" Text="Servicios Medicos y Hospitalarios Centroamericanos" TextAlignment="BottomLeft" SizeF="552.777954,27.0833321" LocationFloat="0,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="12" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="13" ControlType="XRLabel" Name="label2" Multiline="true" Text="Productos sin Convenios de Consignación" TextAlignment="BottomLeft" SizeF="552.777954,27.08333" LocationFloat="0,27.083334" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="14" UseFont="false" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item1>
    <Item2 Ref="15" ControlType="DetailBand" Name="detailBand1" HeightF="32.2916679">
      <Controls>
        <Item1 Ref="16" ControlType="XRTable" Name="table1" TextAlignment="BottomLeft" SizeF="747.9165,32.2916679" LocationFloat="0,0" Font="Arial, 8.25pt, charSet=0" BackColor="Transparent" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="17" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="18" ControlType="XRTableCell" Name="tableCell5" Weight="0.52835073032057811" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="19" EventName="BeforePrint" PropertyName="Text" Expression="[Codigo]" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="20" ControlType="XRTableCell" Name="tableCell6" Weight="3.5747444439962428" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="21" EventName="BeforePrint" PropertyName="Text" Expression="[Nombre]" />
                  </ExpressionBindings>
                </Item2>
                <Item3 Ref="22" ControlType="XRTableCell" Name="tableCell7" Weight="0.72164680573405993" TextFormatString="{0:C2}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="23" EventName="BeforePrint" PropertyName="Text" Expression="[PrecioConvenio]" />
                  </ExpressionBindings>
                </Item3>
                <Item4 Ref="24" ControlType="XRTableCell" Name="tableCell8" Weight="0.72680351452765812" TextFormatString="{0:C2}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="25" EventName="BeforePrint" PropertyName="Text" Expression="[CostoUltimo]" />
                  </ExpressionBindings>
                </Item4>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="26" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item2>
    <Item3 Ref="27" ControlType="BottomMarginBand" Name="bottomMarginBand1" HeightF="14.4166632" />
  </Bands>
</XtraReportsLayoutSerializer>