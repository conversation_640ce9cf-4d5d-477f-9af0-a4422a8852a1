﻿
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using Radiologia;
using Microsoft.AspNetCore.Authorization;

namespace Reportes.Controllers
{
    public class ReporteController : Controller
    {
      
        [AllowAnonymous]
        [HttpGet("status")]
        public string status()
        {
            return "Servicio v3-reporte-inventario " + DateTime.Now.ToString() + " version - startup";
        }
        [Authorize]
        [HttpPost("Reporte")]
        public IActionResult GetReporte([FromBody] Models.Reporte parametros)
        {

            try
            {
                if (!ModelState.IsValid) return Content(JsonConvert.SerializeObject(new { codigo = -1, descripcion = "Parámetros no válidos." }), "application/json");
                
                Models.Reporte Modelo = new Models.Reporte();
                byte[] result = Modelo.ReporteGenerar(parametros).ToArray();
                return File(result, parametros.opciones.tiporeporte);
            }
            catch (Exception ex)
            {
                return BadRequest(JsonConvert.SerializeObject(ex));
            }

        }

    }
}
