﻿using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;
using Modelo.Conexion;
using Radiologia;
using Reporte.Estructura;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Reporte.Models
{
    public class ReporteKardexBodegaProducto  
    {
        public class EncabezadoReporte
        {
            public int CodigoBodega { get; set; }
            public string NombreBodega { get; set; }
            public string CodigoProducto { get; set; }
            public string NombreProducto { get; set; }
            public int InventarioInicial { get; set; }
          
        }


        private IConexion conexion;

        public ReporteKardexBodegaProducto()
        {
            
            String Usuario = Startup.Conexion["Tag_Usuario"].ToString();
            String Password = Startup.Conexion["Tag_Password"].ToString();

            String Instancia = Startup.Conexion["Instancia"].ToString();
            DBManager db = new DBManager(Usuario, Password, Instancia);
            conexion = db.ObtenerConexion();
        }

        public MemoryStream GeneraReporteKardexBodegaProducto(EstructuraSesion cierreExistencias)
        {

            ReporteKardexProductoBodega reporteKardexProductoBodega = new ReporteKardexProductoBodega();
            DataTable resultados = new DataTable();
            //DataTable admisionesList = new DataTable();
            DataSet datosResumen = new DataSet();
            List<SqlParameter> parametrosEntrada = new List<SqlParameter>();
            int defaultValue = 0;


            try
            {
                parametrosEntrada.Add(conexion.crearParametro("@Producto", SqlDbType.VarChar, cierreExistencias.opciones.ListaProductos));
                parametrosEntrada.Add(conexion.crearParametro("@Bodega", SqlDbType.Int, cierreExistencias.opciones.Bodega));

                resultados = conexion.getTableBySP("INVENTARIO", "spINVReporteKardex", parametrosEntrada);



                var headerReporte = (from dataKardex in resultados.AsEnumerable()
                                           select new EncabezadoReporte()
                                           {
                                                 CodigoBodega         = Convert.ToInt32(dataKardex["CodigoBodega"].ToString())
                                               , NombreBodega         = dataKardex["NombreBodega"].ToString() 
                                               , CodigoProducto       = dataKardex["Producto"].ToString()
                                               , NombreProducto       = dataKardex["NombreProducto"].ToString()
                                               , InventarioInicial    = Convert.ToInt32(dataKardex["InventarioFisico"].ToString()) 
                                           })
                                           .FirstOrDefault();
                                           

                var codigoAgrupacion = resultados.AsEnumerable()
                                       .Where(k => k.Field<Int32?>("codigoAgrupacion") != null)
                                       .GroupBy(k => k.Field<Int32?>("codigoAgrupacion")).Distinct()
                                       .Select(g => g.Key)
                                        .FirstOrDefault() ?? defaultValue;
                                       

               // var codigoAgrupacion = agrupacion.Key;

                var SumaIngresos = resultados.AsEnumerable().Sum(row => Convert.ToDecimal(row.Field<Int32?>("Entrada")));
                var SumaEgresos = resultados.AsEnumerable().Sum(row => Convert.ToDecimal(row.Field<Int32?>("Salida")));                
                var InventarioFinal = (from s in resultados.AsEnumerable()
                                       select Convert.ToInt32(s["TotalCalculado"])
                                       ).LastOrDefault();
                
                ((XRTableCell)reporteKardexProductoBodega.FindControl("FechaAtencion", true)).Text = (codigoAgrupacion == 1 ) ? "FechaDespacho" : "FechaAceptado";

                
                reporteKardexProductoBodega.FindControl("NombreProducto", true).Text = headerReporte.NombreProducto.ToString();
                reporteKardexProductoBodega.FindControl("CodigoBodega", true).Text = headerReporte.CodigoBodega.ToString();
                reporteKardexProductoBodega.FindControl("NombreBodega", true).Text = headerReporte.NombreBodega.ToString();
                ((XRTableCell)reporteKardexProductoBodega.FindControl("CodigoProducto", true)).Text = headerReporte.CodigoProducto.ToString();
                ((XRTableCell)reporteKardexProductoBodega.FindControl("SumaIngresos", true)).Text = SumaIngresos.ToString();
                ((XRTableCell)reporteKardexProductoBodega.FindControl("SumaEgresos", true)).Text = SumaEgresos.ToString();
                ((XRTableCell)reporteKardexProductoBodega.FindControl("ExistenciaBodega", true)).Text = InventarioFinal.ToString();

                DataTable kardexProductoBodega = resultados;
                datosResumen.Tables.Add(kardexProductoBodega);
                reporteKardexProductoBodega.DataSource = datosResumen;



 
                using (MemoryStream ms2 = new MemoryStream())
                {
                    PdfExportOptions pdfOptions = reporteKardexProductoBodega.ExportOptions.Pdf;
                    pdfOptions.ConvertImagesToJpeg = false;
                    pdfOptions.ImageQuality = PdfJpegImageQuality.High;
                    pdfOptions.PdfACompatibility = PdfACompatibility.PdfA3b;
                    pdfOptions.DocumentOptions.Author = "Sighos";
                    pdfOptions.DocumentOptions.Keywords = "Sighos, Reporte, PDF";
                    //pdfOptions.DocumentOptions.Producer = Environment.UserName.ToString();
                    pdfOptions.DocumentOptions.Subject = "Reporte";
                    pdfOptions.DocumentOptions.Title = "Reporte";

                    if (cierreExistencias.opciones.tiporeporte == "text/csv") reporteKardexProductoBodega.ExportToCsv(ms2);
                    if (cierreExistencias.opciones.tiporeporte == "application/pdf") reporteKardexProductoBodega.ExportToPdf(ms2, pdfOptions);
                    if (cierreExistencias.opciones.tiporeporte == "application/vnd.ms-excel") reporteKardexProductoBodega.ExportToXlsx(ms2);

                    ms2.Seek(0, System.IO.SeekOrigin.Begin);

                    //admisionesList = null;
                    conexion.closeConexion();
                    conexion = null;
                    reporteKardexProductoBodega.DataSource = null;
                    reporteKardexProductoBodega.Dispose();
                    reporteKardexProductoBodega = null;
                    System.GC.Collect(2);
                    System.GC.WaitForFullGCComplete();

                    return ms2;
                }

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex);
            }

        }

    }
}
