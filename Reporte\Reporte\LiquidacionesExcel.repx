﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="LiquidacionesExcel" Landscape="true" Margins="99, 100, 127, 100" PaperKind="Custom" PageWidth="2000" PageHeight="1269" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" HeightF="127.083328">
      <Controls>
        <Item1 Ref="2" ControlType="XRTable" Name="table2" TextAlignment="BottomLeft" SizeF="1800,32.2916679" LocationFloat="0,94.7916641" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="SkyBlue" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="3" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="4" ControlType="XRTableCell" Name="tableCell13" Weight="0.62113400135248886" Multiline="true" Text="Correlativo" />
                <Item2 Ref="5" ControlType="XRTableCell" Name="tableCell14" Weight="0.69845357788877027" Multiline="true" Text="Fecha" />
                <Item3 Ref="6" ControlType="XRTableCell" Name="tableCell15" Weight="0.690721750485437" Multiline="true" Text="Proveedor" />
                <Item4 Ref="7" ControlType="XRTableCell" Name="tableCell16" Weight="2.9793824575420325" Multiline="true" Text="Nombre" />
                <Item5 Ref="8" ControlType="XRTableCell" Name="tableCell17" Weight="0.63037973930461555" Multiline="true" Text="Nit" />
                <Item6 Ref="9" ControlType="XRTableCell" Name="tableCell18" Weight="0.7850187818142228" Multiline="true" Text="Orden de Compra" />
                <Item7 Ref="10" ControlType="XRTableCell" Name="tableCell19" Weight="0.7850187818142228" Multiline="true" Text="Factura" />
                <Item8 Ref="11" ControlType="XRTableCell" Name="tableCell20" Weight="0.7850187818142228" Multiline="true" Text="Status" />
                <Item9 Ref="12" ControlType="XRTableCell" Name="tableCell21" Weight="0.50666914274576247" Multiline="true" Text="Usuario" />
                <Item10 Ref="13" ControlType="XRTableCell" Name="tableCell22" Weight="0.78501786676222618" Multiline="true" Text="Fecha Inicial" />
                <Item11 Ref="14" ControlType="XRTableCell" Name="tableCell23" Weight="0.78501786676222618" Multiline="true" Text="Fecha Final" />
                <Item12 Ref="15" ControlType="XRTableCell" Name="tableCell24" Weight="3.308991967810512" Multiline="true" Text="Comentario" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="16" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="17" ControlType="XRLabel" Name="labelFechas" Multiline="true" TextAlignment="BottomLeft" SizeF="757.148438,27.0833321" LocationFloat="0,54.1666679" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="18" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="19" ControlType="XRLabel" Name="label2" Multiline="true" Text="Reporte de Liquidaciones" TextAlignment="BottomLeft" SizeF="757.148438,27.08333" LocationFloat="0,27.083334" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="20" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="21" ControlType="XRLabel" Name="label1" Multiline="true" Text="SERVICIOS MEDICOS Y HOSPITALARIOS CENTROAMERICANOS, S.A." TextAlignment="BottomLeft" SizeF="757.148438,27.0833321" LocationFloat="0,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="22" UseFont="false" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item1>
    <Item2 Ref="23" ControlType="BottomMarginBand" Name="BottomMargin" />
    <Item3 Ref="24" ControlType="DetailBand" Name="Detail" HeightF="32.2916679">
      <Controls>
        <Item1 Ref="25" ControlType="XRTable" Name="table1" TextAlignment="BottomLeft" SizeF="1800,32.2916679" LocationFloat="0,0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="26" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="27" ControlType="XRTableCell" Name="tableCell1" Weight="0.62113400135248886" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="28" EventName="BeforePrint" PropertyName="Text" Expression="[Correlativo]" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="29" ControlType="XRTableCell" Name="tableCell2" Weight="0.69845357788877027" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="30" EventName="BeforePrint" PropertyName="Text" Expression="[Fecha]" />
                  </ExpressionBindings>
                </Item2>
                <Item3 Ref="31" ControlType="XRTableCell" Name="tableCell3" Weight="0.690721750485437" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="32" EventName="BeforePrint" PropertyName="Text" Expression="[Proveedor]" />
                  </ExpressionBindings>
                </Item3>
                <Item4 Ref="33" ControlType="XRTableCell" Name="tableCell4" Weight="2.9793824575420325" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="34" EventName="BeforePrint" PropertyName="Text" Expression="[Nombre]" />
                  </ExpressionBindings>
                </Item4>
                <Item5 Ref="35" ControlType="XRTableCell" Name="tableCell5" Weight="0.63037973930461555" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="36" EventName="BeforePrint" PropertyName="Text" Expression="[Nit]" />
                  </ExpressionBindings>
                </Item5>
                <Item6 Ref="37" ControlType="XRTableCell" Name="tableCell6" Weight="0.7850187818142228" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="38" EventName="BeforePrint" PropertyName="Text" Expression="[OrdenCompra]" />
                  </ExpressionBindings>
                </Item6>
                <Item7 Ref="39" ControlType="XRTableCell" Name="tableCell7" Weight="0.7850187818142228" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="40" EventName="BeforePrint" PropertyName="Text" Expression="[Factura]" />
                  </ExpressionBindings>
                </Item7>
                <Item8 Ref="41" ControlType="XRTableCell" Name="tableCell8" Weight="0.7850187818142228" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="42" EventName="BeforePrint" PropertyName="Text" Expression="[Status]" />
                  </ExpressionBindings>
                </Item8>
                <Item9 Ref="43" ControlType="XRTableCell" Name="tableCell9" Weight="0.50666914274576247" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="44" EventName="BeforePrint" PropertyName="Text" Expression="[Usuario]" />
                  </ExpressionBindings>
                </Item9>
                <Item10 Ref="45" ControlType="XRTableCell" Name="tableCell10" Weight="0.78501786676222618" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="46" EventName="BeforePrint" PropertyName="Text" Expression="[FechaInicial]" />
                  </ExpressionBindings>
                </Item10>
                <Item11 Ref="47" ControlType="XRTableCell" Name="tableCell11" Weight="0.78501786676222618" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="48" EventName="BeforePrint" PropertyName="Text" Expression="[FechaFinal]" />
                  </ExpressionBindings>
                </Item11>
                <Item12 Ref="49" ControlType="XRTableCell" Name="tableCell12" Weight="3.308991967810512" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="50" EventName="BeforePrint" PropertyName="Text" Expression="[Comentario]" />
                  </ExpressionBindings>
                </Item12>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="51" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item3>
  </Bands>
</XtraReportsLayoutSerializer>