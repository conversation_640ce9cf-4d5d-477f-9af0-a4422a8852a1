﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="PendienteAceptarExcel" Landscape="true" Margins="41, 35, 100, 100" PageWidth="1100" PageHeight="850" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" />
    <Item2 Ref="2" ControlType="BottomMarginBand" Name="BottomMargin" />
    <Item3 Ref="3" ControlType="DetailBand" Name="Detail" HeightF="27.9166546">
      <Controls>
        <Item1 Ref="4" ControlType="XRTable" Name="table1" TextAlignment="MiddleLeft" SizeF="1022.99994,27.9166546" LocationFloat="0,0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="5" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="6" ControlType="XRTableCell" Name="tableCell1" Weight="0.52452834617379107" Multiline="true" Font="Arial, 6.75pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="7" EventName="BeforePrint" PropertyName="Text" Expression="[BodegaFuente]" />
                  </ExpressionBindings>
                  <StylePriority Ref="8" UseFont="false" UseBorders="false" />
                </Item1>
                <Item2 Ref="9" ControlType="XRTableCell" Name="tableCell2" Weight="0.50943368113372123" Multiline="true" Font="Arial, 6.75pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="10" EventName="BeforePrint" PropertyName="Text" Expression="[BodegaDestino]" />
                  </ExpressionBindings>
                  <StylePriority Ref="11" UseFont="false" UseBorders="false" />
                </Item2>
                <Item3 Ref="12" ControlType="XRTableCell" Name="tableCell3" Weight="0.75471722227802318" Multiline="true" Font="Arial, 6.75pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="13" EventName="BeforePrint" PropertyName="Text" Expression="[Movimiento]" />
                  </ExpressionBindings>
                  <StylePriority Ref="14" UseFont="false" UseBorders="false" />
                </Item3>
                <Item4 Ref="15" ControlType="XRTableCell" Name="tableCell4" Weight="0.6981131003939578" TextFormatString="{0:d/MM/yyyy}" Multiline="true" Font="Arial, 6.75pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="16" EventName="BeforePrint" PropertyName="Text" Expression="[Fecha]" />
                  </ExpressionBindings>
                  <StylePriority Ref="17" UseFont="false" UseBorders="false" />
                </Item4>
                <Item5 Ref="18" ControlType="XRTableCell" Name="tableCell5" Weight="1.1320755703999486" Multiline="true" Font="Arial, 6.75pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="19" EventName="BeforePrint" PropertyName="Text" Expression="[FechaDespacho]" />
                  </ExpressionBindings>
                  <StylePriority Ref="20" UseFont="false" UseBorders="false" />
                </Item5>
                <Item6 Ref="21" ControlType="XRTableCell" Name="tableCell6" Weight="0.73584912987985573" Multiline="true" Font="Arial, 6.75pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="22" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="23" UseFont="false" UseBorders="false" />
                </Item6>
                <Item7 Ref="24" ControlType="XRTableCell" Name="tableCell7" Weight="3.3150947672239606" Multiline="true" Font="Arial, 6.75pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="25" EventName="BeforePrint" PropertyName="Text" Expression="[Nombre]" />
                  </ExpressionBindings>
                  <StylePriority Ref="26" UseFont="false" UseBorders="false" />
                </Item7>
                <Item8 Ref="27" ControlType="XRTableCell" Name="tableCell8" Weight="0.69245271135994324" Multiline="true" Font="Arial, 6.75pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="28" EventName="BeforePrint" PropertyName="Text" Expression="[Existencia]" />
                  </ExpressionBindings>
                  <StylePriority Ref="29" UseFont="false" UseBorders="false" />
                </Item8>
                <Item9 Ref="30" ControlType="XRTableCell" Name="tableCell9" Weight="0.90264122091638044" TextFormatString="{0:C2}" Multiline="true" Font="Arial, 6.75pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="31" EventName="BeforePrint" PropertyName="Text" Expression="[Valor]" />
                  </ExpressionBindings>
                  <StylePriority Ref="32" UseFont="false" UseBorders="false" />
                </Item9>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="33" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="34" ControlType="PageHeaderBand" Name="PageHeader" HeightF="72.9166641">
      <SubBands>
        <Item1 Ref="35" ControlType="SubBand" Name="SubBand1" HeightF="47.9166679">
          <Controls>
            <Item1 Ref="36" ControlType="XRTable" Name="table2" SizeF="1022.99994,47.9166679" LocationFloat="0,0" Padding="2,2,0,0,96">
              <Rows>
                <Item1 Ref="37" ControlType="XRTableRow" Name="tableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="38" ControlType="XRTableCell" Name="tableCell10" Weight="0.52452834617379107" Multiline="true" Text="Bodega Fuente" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightBlue" Borders="All">
                      <StylePriority Ref="39" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="40" ControlType="XRTableCell" Name="tableCell11" Weight="0.50943368113372123" Multiline="true" Text="Bodega Destino" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightBlue" Borders="All">
                      <StylePriority Ref="41" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item2>
                    <Item3 Ref="42" ControlType="XRTableCell" Name="tableCell12" Weight="0.75471722227802318" Multiline="true" Text="Movimiento" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightBlue" Borders="All">
                      <StylePriority Ref="43" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item3>
                    <Item4 Ref="44" ControlType="XRTableCell" Name="tableCell13" Weight="0.6981131003939578" Multiline="true" Text="Fecha" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightBlue" Borders="All">
                      <StylePriority Ref="45" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item4>
                    <Item5 Ref="46" ControlType="XRTableCell" Name="tableCell14" Weight="1.1320755703999486" Multiline="true" Text="Fecha Despacho" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightBlue" Borders="All">
                      <StylePriority Ref="47" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item5>
                    <Item6 Ref="48" ControlType="XRTableCell" Name="tableCell15" Weight="0.73584912987985573" Multiline="true" Text="Producto" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightBlue" Borders="All">
                      <StylePriority Ref="49" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item6>
                    <Item7 Ref="50" ControlType="XRTableCell" Name="tableCell16" Weight="3.3150947672239606" Multiline="true" Text="Nombre Producto" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightBlue" Borders="All">
                      <StylePriority Ref="51" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item7>
                    <Item8 Ref="52" ControlType="XRTableCell" Name="tableCell17" Weight="0.69245271135994324" Multiline="true" Text="Cantidad" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightBlue" Borders="All">
                      <StylePriority Ref="53" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item8>
                    <Item9 Ref="54" ControlType="XRTableCell" Name="tableCell18" Weight="0.90264122091638044" Multiline="true" Text="Valor" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightBlue" Borders="All">
                      <StylePriority Ref="55" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item9>
                  </Cells>
                </Item1>
              </Rows>
            </Item1>
          </Controls>
        </Item1>
      </SubBands>
      <Controls>
        <Item1 Ref="56" ControlType="XRLabel" Name="label2" Multiline="true" Text="Consulta de Productos Despachados por Bodega" SizeF="399.5833,31.25" LocationFloat="0,31.25" Padding="2,2,0,0,100" />
        <Item2 Ref="57" ControlType="XRLabel" Name="label1" Multiline="true" Text="Grupo Sermesa" SizeF="114.166626,31.25" LocationFloat="0,0" Padding="2,2,0,0,96" />
      </Controls>
    </Item4>
  </Bands>
</XtraReportsLayoutSerializer>