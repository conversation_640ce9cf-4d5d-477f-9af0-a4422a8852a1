﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="*********" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="CargosProductosConsignacionExcel" Margins="100, 100, 170, 1" PaperKind="Custom" PageWidth="2000" PageHeight="1269" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" HeightF="169.791672">
      <Controls>
        <Item1 Ref="2" ControlType="XRLabel" Name="labelTipo" Multiline="true" TextAlignment="BottomLeft" SizeF="390.782959,27.08332" LocationFloat="853.564453,108.333336" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="3" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="4" ControlType="XRLabel" Name="labelProveedor" Multiline="true" TextAlignment="BottomLeft" SizeF="757.148438,27.0833282" LocationFloat="0,108.333336" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="5" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="6" ControlType="XRTable" Name="table2" TextAlignment="BottomLeft" SizeF="1800,34.375" LocationFloat="0,135.416672" Font="Arial, 8.25pt, charSet=0" BackColor="SkyBlue" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="7" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="8" ControlType="XRTableCell" Name="tableCell17" Weight="0.70785098355371068" Multiline="true" Text="Codigo" />
                <Item2 Ref="9" ControlType="XRTableCell" Name="tableCell18" Weight="5.3785014591767224" Multiline="true" Text="Producto" />
                <Item3 Ref="10" ControlType="XRTableCell" Name="tableCell19" Weight="1.0669676958693373" Multiline="true" Text="Hospital" />
                <Item4 Ref="11" ControlType="XRTableCell" Name="tableCell20" Weight="0.63638066653083225" Multiline="true" Text="Serie" />
                <Item5 Ref="12" ControlType="XRTableCell" Name="tableCell21" Weight="0.99194899414107507" Multiline="true" Text="Admision" />
                <Item6 Ref="13" ControlType="XRTableCell" Name="tableCell22" Weight="0.96314570808969346" Multiline="true" Text="Cantidad" />
                <Item7 Ref="14" ControlType="XRTableCell" Name="tableCell23" Weight="1.0619394699871618" Multiline="true" Text="Tipo Cargo" />
                <Item8 Ref="15" ControlType="XRTableCell" Name="tableCell24" Weight="0.99672941914893443" Multiline="true" Text="Cargo" />
                <Item9 Ref="16" ControlType="XRTableCell" Name="tableCell25" Weight="0.99864195484982909" Multiline="true" Text="Monto" />
                <Item10 Ref="17" ControlType="XRTableCell" Name="tableCell26" Weight="1.0415103026407317" Multiline="true" Text="Fecha" />
                <Item11 Ref="18" ControlType="XRTableCell" Name="tableCell27" Weight="1.0618345863936725" Multiline="true" Text="Correlativo" />
                <Item12 Ref="19" ControlType="XRTableCell" Name="tableCell28" Weight="0.77814137697779939" Multiline="true" Text="Bodega" />
                <Item13 Ref="20" ControlType="XRTableCell" Name="tableCell29" Weight="1.0922281678576455" Multiline="true" Text="Consignacion Liquidada" />
                <Item14 Ref="21" ControlType="XRTableCell" Name="tableCell30" Weight="0.65957435492905292" Multiline="true" Text="Id Envio" />
                <Item15 Ref="22" ControlType="XRTableCell" Name="tableCell31" Weight="1.0833815093995693" Multiline="true" Text="Costo Convenio" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="23" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="24" ControlType="XRLabel" Name="label1" Multiline="true" Text="SERVICIOS MEDICOS Y HOSPITALARIOS CENTROAMERICANOS, S.A." TextAlignment="BottomLeft" SizeF="757.148438,27.0833321" LocationFloat="0,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="25" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="26" ControlType="XRLabel" Name="label2" Multiline="true" Text="Reporte de detallado de consumos de productos en Consignación" TextAlignment="BottomLeft" SizeF="757.148438,27.08333" LocationFloat="0,27.083334" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="27" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="28" ControlType="XRLabel" Name="labelFechas" Multiline="true" TextAlignment="BottomLeft" SizeF="757.148438,27.0833321" LocationFloat="0,54.1666679" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="29" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="30" ControlType="XRLabel" Name="labelBodega" Multiline="true" TextAlignment="BottomLeft" SizeF="757.148438,27.0833282" LocationFloat="0,81.25" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="31" UseFont="false" UseTextAlignment="false" />
        </Item7>
      </Controls>
    </Item1>
    <Item2 Ref="32" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="1.04166663" />
    <Item3 Ref="33" ControlType="DetailBand" Name="Detail" HeightF="34.375">
      <Controls>
        <Item1 Ref="34" ControlType="XRTable" Name="table1" TextAlignment="BottomLeft" SizeF="1800,34.375" LocationFloat="0,0" Font="Arial, 8.25pt, charSet=0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="35" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="36" ControlType="XRTableCell" Name="tableCell1" Weight="0.70785035561125154" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="37" EventName="BeforePrint" PropertyName="Text" Expression="[Codigo]" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="38" ControlType="XRTableCell" Name="tableCell2" Weight="5.37850271506164" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="39" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
                  </ExpressionBindings>
                </Item2>
                <Item3 Ref="40" ControlType="XRTableCell" Name="tableCell3" Weight="1.0669663614916112" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="41" EventName="BeforePrint" PropertyName="Text" Expression="[Hospital]" />
                  </ExpressionBindings>
                </Item3>
                <Item4 Ref="42" ControlType="XRTableCell" Name="tableCell4" Weight="0.63638137296609931" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="43" EventName="BeforePrint" PropertyName="Text" Expression="[SerieAdmision]" />
                  </ExpressionBindings>
                </Item4>
                <Item5 Ref="44" ControlType="XRTableCell" Name="tableCell5" Weight="0.99194836619861615" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="45" EventName="BeforePrint" PropertyName="Text" Expression="[Admision]" />
                  </ExpressionBindings>
                </Item5>
                <Item6 Ref="46" ControlType="XRTableCell" Name="tableCell6" Weight="0.96314633603215238" TextFormatString="{0:N0}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="47" EventName="BeforePrint" PropertyName="Text" Expression="[Cantidad]" />
                  </ExpressionBindings>
                </Item6>
                <Item7 Ref="48" ControlType="XRTableCell" Name="tableCell7" Weight="1.0619394699871618" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="49" EventName="BeforePrint" PropertyName="Text" Expression="[Tipo_Cargo]" />
                  </ExpressionBindings>
                </Item7>
                <Item8 Ref="50" ControlType="XRTableCell" Name="tableCell8" Weight="0.99672941914893443" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="51" EventName="BeforePrint" PropertyName="Text" Expression="[Cargo]" />
                  </ExpressionBindings>
                </Item8>
                <Item9 Ref="52" ControlType="XRTableCell" Name="tableCell9" Weight="0.99864321073474693" TextFormatString="{0:C2}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="53" EventName="BeforePrint" PropertyName="Text" Expression="[Monto]" />
                  </ExpressionBindings>
                </Item9>
                <Item10 Ref="54" ControlType="XRTableCell" Name="tableCell10" Weight="1.0415090467558144" TextFormatString="{0:d/MM/yyyy}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="55" EventName="BeforePrint" PropertyName="Text" Expression="[Fecha]" />
                  </ExpressionBindings>
                </Item10>
                <Item11 Ref="56" ControlType="XRTableCell" Name="tableCell11" Weight="1.0618345863936725" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="57" EventName="BeforePrint" PropertyName="Text" Expression="[Liquidacion]" />
                  </ExpressionBindings>
                </Item11>
                <Item12 Ref="58" ControlType="XRTableCell" Name="tableCell12" Weight="0.77813886520796194" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="59" EventName="BeforePrint" PropertyName="Text" Expression="[Bodega]" />
                  </ExpressionBindings>
                </Item12>
                <Item13 Ref="60" ControlType="XRTableCell" Name="tableCell13" Weight="1.2208332952493266" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="61" EventName="BeforePrint" PropertyName="Text" Expression="[ConsignacionLiquidada]" />
                  </ExpressionBindings>
                </Item13>
                <Item14 Ref="62" ControlType="XRTableCell" Name="tableCell14" Weight="0.5309704834222897" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="63" EventName="BeforePrint" PropertyName="Text" Expression="[IdEnvio]" />
                  </ExpressionBindings>
                </Item14>
                <Item15 Ref="64" ControlType="XRTableCell" Name="tableCell15" Weight="1.0833827652844876" TextFormatString="{0:C2}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="65" EventName="BeforePrint" PropertyName="Text" Expression="[CostoConvenio]" />
                  </ExpressionBindings>
                </Item15>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="66" UseFont="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item3>
  </Bands>
</XtraReportsLayoutSerializer>