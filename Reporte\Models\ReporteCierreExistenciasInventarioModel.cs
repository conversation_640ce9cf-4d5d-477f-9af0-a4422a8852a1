﻿using DevExpress.XtraPrinting;
using Modelo.Conexion;
using Radiologia;
using Reporte.Estructura;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Reporte.Models
{ 
    public class ReporteCierreExistenciasInventarioModel
    {
        private IConexion conexion;

        public ReporteCierreExistenciasInventarioModel()
        {
            String Usuario = Startup.Conexion["Tag_Usuario"].ToString();
            String Password = Startup.Conexion["Tag_Password"].ToString();

            String Instancia = Startup.Conexion["Instancia"].ToString();
            DBManager db = new DBManager(Usuario, Password, Instancia);
            conexion = db.ObtenerConexion();
        }


        public MemoryStream GeneraCierreExistencias(EstructuraSesion cierreExistencias)
        {

            Reporte.ReporteCierreExistenciasInventario reporteCierreExistencias = new Reporte.ReporteCierreExistenciasInventario();
            DataTable resultados = new DataTable();
            DataTable admisionesList = new DataTable();
            DataSet datosResumen = new DataSet();
            List<SqlParameter> parametrosEntrada = new List<SqlParameter>();


            try
            {
                parametrosEntrada.Add(conexion.crearParametro("@empresabodega", SqlDbType.Char, cierreExistencias.session_empresa_bodega));
                parametrosEntrada.Add(conexion.crearParametro("@empresasucursal", SqlDbType.Char, cierreExistencias.session_sucursal));                
                parametrosEntrada.Add(conexion.crearParametro("@empresaunificadora", SqlDbType.Char, cierreExistencias.session_empresa_unificadora));
                parametrosEntrada.Add(conexion.crearParametro("@periodo", SqlDbType.Char, cierreExistencias.opciones.Periodo));
                parametrosEntrada.Add(conexion.crearParametro("@CodigoBodega", SqlDbType.Int, cierreExistencias.opciones.Bodega));
                parametrosEntrada.Add(conexion.crearParametro("@TipoExistencia", SqlDbType.Int, cierreExistencias.opciones.TipoBusqueda));
                resultados = conexion.getTableBySP("HOSPITAL", "spINVCierreMensualExistencias", parametrosEntrada);
                DataTable inventarioExistencias = resultados;

                var suma = inventarioExistencias.AsEnumerable().Sum(row => Convert.ToDecimal(row.Field<float>("CostoTotal")));

                admisionesList.TableName = "resumen";

                datosResumen.Tables.Add(inventarioExistencias);
                reporteCierreExistencias.DataSource = datosResumen;

                reporteCierreExistencias.FindControl("SumaTotal", true).Text = suma.ToString();

                using (MemoryStream ms2 = new MemoryStream())
                {
                    PdfExportOptions pdfOptions = reporteCierreExistencias.ExportOptions.Pdf;
                    pdfOptions.ConvertImagesToJpeg = false;
                    pdfOptions.ImageQuality = PdfJpegImageQuality.High;
                    pdfOptions.PdfACompatibility = PdfACompatibility.PdfA3b;
                    pdfOptions.DocumentOptions.Author = "Sighos";
                    pdfOptions.DocumentOptions.Keywords = "Sighos, Reporte, PDF";
                    //pdfOptions.DocumentOptions.Producer = Environment.UserName.ToString();
                    pdfOptions.DocumentOptions.Subject = "Reporte";
                    pdfOptions.DocumentOptions.Title = "Reporte";

                    if (cierreExistencias.opciones.tiporeporte == "text/csv") reporteCierreExistencias.ExportToCsv(ms2);
                    if (cierreExistencias.opciones.tiporeporte == "application/pdf") reporteCierreExistencias.ExportToPdf(ms2, pdfOptions);
                    if (cierreExistencias.opciones.tiporeporte == "application/vnd.ms-excel") reporteCierreExistencias.ExportToXlsx(ms2);

                    ms2.Seek(0, System.IO.SeekOrigin.Begin);

                    admisionesList = null;
                    conexion.closeConexion();
                    conexion = null;
                    reporteCierreExistencias.DataSource = null;
                    reporteCierreExistencias.Dispose();
                    reporteCierreExistencias = null;
                    System.GC.Collect(2);
                    System.GC.WaitForFullGCComplete();

                    return ms2;
                }

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex);
            }

        }
    }
}

