//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Reporte.Reporte {
    
    public partial class ContrasenaInventario : DevExpress.XtraReports.UI.XtraReport {
        private void InitializeComponent() {
            DevExpress.XtraReports.ReportInitializer reportInitializer = new DevExpress.XtraReports.ReportInitializer(this, "Reporte.Reporte.ContrasenaInventario.repx");

            // Controls
            this.TopMargin = reportInitializer.GetControl<DevExpress.XtraReports.UI.TopMarginBand>("TopMargin");
            this.BottomMargin = reportInitializer.GetControl<DevExpress.XtraReports.UI.BottomMarginBand>("BottomMargin");
            this.Detail = reportInitializer.GetControl<DevExpress.XtraReports.UI.DetailBand>("Detail");
            this.SubBand1 = reportInitializer.GetControl<DevExpress.XtraReports.UI.SubBand>("SubBand1");
            this.SubBand2 = reportInitializer.GetControl<DevExpress.XtraReports.UI.SubBand>("SubBand2");
            this.barCode1 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRBarCode>("barCode1");
            this.labelRegistro = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelRegistro");
            this.line3 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLine>("line3");
            this.line2 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLine>("line2");
            this.line1 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLine>("line1");
            this.label24 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label24");
            this.labelFechaActual1 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelFechaActual1");
            this.label22 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label22");
            this.labelSaldo1 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelSaldo1");
            this.label20 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label20");
            this.label18 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label18");
            this.labelFechaFactura1 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelFechaFactura1");
            this.label16 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label16");
            this.labelVencimiento1 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelVencimiento1");
            this.label14 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label14");
            this.labelDocumento1 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelDocumento1");
            this.label12 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label12");
            this.labelOrdenCompra1 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelOrdenCompra1");
            this.label10 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label10");
            this.labelNombreProveedor1 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelNombreProveedor1");
            this.label8 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label8");
            this.labelValidacion = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelValidacion");
            this.label6 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label6");
            this.labelContrasena1 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelContrasena1");
            this.label4 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label4");
            this.label3 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label3");
            this.label1 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label1");
            this.barCode2 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRBarCode>("barCode2");
            this.line4 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLine>("line4");
            this.label50 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label50");
            this.label49 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label49");
            this.label42 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label42");
            this.label41 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label41");
            this.label26 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label26");
            this.label27 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label27");
            this.labelContrasena2 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelContrasena2");
            this.label29 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label29");
            this.label30 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label30");
            this.label31 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label31");
            this.labelNombreProveedor2 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelNombreProveedor2");
            this.label33 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label33");
            this.labelOrdenCompra2 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelOrdenCompra2");
            this.label35 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label35");
            this.labelDocumento2 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelDocumento2");
            this.label37 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label37");
            this.labelVencimiento2 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelVencimiento2");
            this.label39 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label39");
            this.labelFechaFactura2 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelFechaFactura2");
            this.label43 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label43");
            this.labelSaldo2 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelSaldo2");
            this.label45 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label45");
            this.labelFechaActual2 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelFechaActual2");
            this.label47 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label47");
            this.label2 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label2");
            this.label5 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label5");
            this.label7 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label7");
        }
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.SubBand SubBand1;
        private DevExpress.XtraReports.UI.SubBand SubBand2;
        private DevExpress.XtraReports.UI.XRBarCode barCode1;
        private DevExpress.XtraReports.UI.XRLabel labelRegistro;
        private DevExpress.XtraReports.UI.XRLine line3;
        private DevExpress.XtraReports.UI.XRLine line2;
        private DevExpress.XtraReports.UI.XRLine line1;
        private DevExpress.XtraReports.UI.XRLabel label24;
        private DevExpress.XtraReports.UI.XRLabel labelFechaActual1;
        private DevExpress.XtraReports.UI.XRLabel label22;
        private DevExpress.XtraReports.UI.XRLabel labelSaldo1;
        private DevExpress.XtraReports.UI.XRLabel label20;
        private DevExpress.XtraReports.UI.XRLabel label18;
        private DevExpress.XtraReports.UI.XRLabel labelFechaFactura1;
        private DevExpress.XtraReports.UI.XRLabel label16;
        private DevExpress.XtraReports.UI.XRLabel labelVencimiento1;
        private DevExpress.XtraReports.UI.XRLabel label14;
        private DevExpress.XtraReports.UI.XRLabel labelDocumento1;
        private DevExpress.XtraReports.UI.XRLabel label12;
        private DevExpress.XtraReports.UI.XRLabel labelOrdenCompra1;
        private DevExpress.XtraReports.UI.XRLabel label10;
        private DevExpress.XtraReports.UI.XRLabel labelNombreProveedor1;
        private DevExpress.XtraReports.UI.XRLabel label8;
        private DevExpress.XtraReports.UI.XRLabel labelValidacion;
        private DevExpress.XtraReports.UI.XRLabel label6;
        private DevExpress.XtraReports.UI.XRLabel labelContrasena1;
        private DevExpress.XtraReports.UI.XRLabel label4;
        private DevExpress.XtraReports.UI.XRLabel label3;
        private DevExpress.XtraReports.UI.XRLabel label1;
        private DevExpress.XtraReports.UI.XRBarCode barCode2;
        private DevExpress.XtraReports.UI.XRLine line4;
        private DevExpress.XtraReports.UI.XRLabel label50;
        private DevExpress.XtraReports.UI.XRLabel label49;
        private DevExpress.XtraReports.UI.XRLabel label42;
        private DevExpress.XtraReports.UI.XRLabel label41;
        private DevExpress.XtraReports.UI.XRLabel label26;
        private DevExpress.XtraReports.UI.XRLabel label27;
        private DevExpress.XtraReports.UI.XRLabel labelContrasena2;
        private DevExpress.XtraReports.UI.XRLabel label29;
        private DevExpress.XtraReports.UI.XRLabel label30;
        private DevExpress.XtraReports.UI.XRLabel label31;
        private DevExpress.XtraReports.UI.XRLabel labelNombreProveedor2;
        private DevExpress.XtraReports.UI.XRLabel label33;
        private DevExpress.XtraReports.UI.XRLabel labelOrdenCompra2;
        private DevExpress.XtraReports.UI.XRLabel label35;
        private DevExpress.XtraReports.UI.XRLabel labelDocumento2;
        private DevExpress.XtraReports.UI.XRLabel label37;
        private DevExpress.XtraReports.UI.XRLabel labelVencimiento2;
        private DevExpress.XtraReports.UI.XRLabel label39;
        private DevExpress.XtraReports.UI.XRLabel labelFechaFactura2;
        private DevExpress.XtraReports.UI.XRLabel label43;
        private DevExpress.XtraReports.UI.XRLabel labelSaldo2;
        private DevExpress.XtraReports.UI.XRLabel label45;
        private DevExpress.XtraReports.UI.XRLabel labelFechaActual2;
        private DevExpress.XtraReports.UI.XRLabel label47;
        private DevExpress.XtraReports.UI.XRLabel label2;
        private DevExpress.XtraReports.UI.XRLabel label5;
        private DevExpress.XtraReports.UI.XRLabel label7;
    }
}
