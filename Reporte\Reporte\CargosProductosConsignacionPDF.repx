﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="CargosProductosConsignacionPDF" Margins="54, 59, 200, 100" PageWidth="850" PageHeight="1100" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" HeightF="200">
      <Controls>
        <Item1 Ref="2" ControlType="XRLabel" Name="labelEstado" Multiline="true" TextAlignment="MiddleCenter" SizeF="118.06736,30.2083435" LocationFloat="0,158.958344" Font="Times New Roman, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="3" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="4" ControlType="XRLabel" Name="labelTipo" Multiline="true" TextAlignment="MiddleCenter" SizeF="452.083466,30.2083435" LocationFloat="118.06736,158.958344" Font="Times New Roman, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="5" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="6" ControlType="XRLabel" Name="labelPeriodo" Multiline="true" TextAlignment="MiddleCenter" SizeF="452.083466,30.2083435" LocationFloat="118.06736,128.75" Font="Times New Roman, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="7" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="8" ControlType="XRLabel" Name="labelProveedor" Multiline="true" TextAlignment="MiddleCenter" SizeF="452.083466,30.2083282" LocationFloat="118.06736,98.54167" Font="Times New Roman, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="9" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="10" ControlType="XRLabel" Name="labelEmpresa" Multiline="true" Text="Reporte Detallado de Consumos de Productos en Consignación" TextAlignment="MiddleCenter" SizeF="452.083466,34.375" LocationFloat="118.06736,64.16667" Font="Times New Roman, 11.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="11" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="12" ControlType="XRLabel" Name="labelNombeEmpresa" Multiline="true" TextAlignment="MiddleCenter" SizeF="452.083466,30.2083359" LocationFloat="118.06736,10.0000067" Font="Times New Roman, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="13" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="14" ControlType="XRLabel" Name="labelNitEmpresa" Multiline="true" TextAlignment="MiddleCenter" SizeF="245.833527,23.9583321" LocationFloat="222.234024,40.20834" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="15" UseFont="false" UseTextAlignment="false" />
        </Item7>
      </Controls>
    </Item1>
    <Item2 Ref="16" ControlType="BottomMarginBand" Name="BottomMargin" />
    <Item3 Ref="17" ControlType="DetailBand" Name="Detail" HeightF="23.3333263">
      <Controls>
        <Item1 Ref="18" ControlType="XRTable" Name="table2" TextAlignment="BottomLeft" SizeF="736.999939,23.3333263" LocationFloat="0,0" Font="Arial, 8.25pt, charSet=0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="19" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="20" ControlType="XRTableCell" Name="tableCell11" Weight="0.43816265011258748" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="21" EventName="BeforePrint" PropertyName="Text" Expression="[Codigo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="22" UseFont="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="23" ControlType="XRTableCell" Name="tableCell12" Weight="2.6431096635658515" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="24" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="25" UseFont="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="26" ControlType="XRTableCell" Name="tableCell13" Weight="0.44181372787118" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="27" EventName="BeforePrint" PropertyName="Text" Expression="[Hospital]" />
                  </ExpressionBindings>
                  <StylePriority Ref="28" UseFont="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="29" ControlType="XRTableCell" Name="tableCell14" Weight="0.67844792541320187" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="30" EventName="BeforePrint" PropertyName="Text" Expression="Concat([SerieAdmision], ' - ', [Admision])" />
                  </ExpressionBindings>
                  <StylePriority Ref="31" UseFont="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="32" ControlType="XRTableCell" Name="tableCell15" Weight="0.5300332770282995" TextFormatString="{0:N0}" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="33" EventName="BeforePrint" PropertyName="Text" Expression="[Cantidad]" />
                  </ExpressionBindings>
                  <StylePriority Ref="34" UseFont="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="35" ControlType="XRTableCell" Name="tableCell16" Weight="0.6784491724021855" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="36" EventName="BeforePrint" PropertyName="Text" Expression="Concat([Tipo_Cargo], [Cargo])" />
                  </ExpressionBindings>
                  <StylePriority Ref="37" UseFont="false" UseTextAlignment="false" />
                </Item6>
                <Item7 Ref="38" ControlType="XRTableCell" Name="tableCell17" Weight="0.51943546626523074" TextFormatString="{0:d/MM/yyyy}" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="39" EventName="BeforePrint" PropertyName="Text" Expression="[Fecha]" />
                  </ExpressionBindings>
                  <StylePriority Ref="40" UseFont="false" UseTextAlignment="false" />
                </Item7>
                <Item8 Ref="41" ControlType="XRTableCell" Name="tableCell18" Weight="0.4373363444355729" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="42" EventName="BeforePrint" PropertyName="Text" Expression="[Bodega]" />
                  </ExpressionBindings>
                  <StylePriority Ref="43" UseFont="false" UseTextAlignment="false" />
                </Item8>
                <Item9 Ref="44" ControlType="XRTableCell" Name="tableCell19" Weight="0.42132223779069133" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="45" EventName="BeforePrint" PropertyName="Text" Expression="[Liquidacion]" />
                  </ExpressionBindings>
                  <StylePriority Ref="46" UseFont="false" UseTextAlignment="false" />
                </Item9>
                <Item10 Ref="47" ControlType="XRTableCell" Name="tableCell20" Weight="0.71210091653225327" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="48" EventName="BeforePrint" PropertyName="Text" Expression="[CostoConvenio]" />
                  </ExpressionBindings>
                  <StylePriority Ref="49" UseFont="false" UseTextAlignment="false" />
                </Item10>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="50" UseFont="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="51" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="33.3333321">
      <Controls>
        <Item1 Ref="52" ControlType="XRTable" Name="table1" TextAlignment="BottomLeft" SizeF="736.999939,33.3333321" LocationFloat="0,0" Font="Arial, 8.25pt, charSet=0" Padding="2,2,0,0,96" Borders="Bottom">
          <Rows>
            <Item1 Ref="53" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="54" ControlType="XRTableCell" Name="tableCell1" Weight="0.43816265011258748" Multiline="true" Text="Codigo" Font="Times New Roman, 8.25pt, charSet=0">
                  <StylePriority Ref="55" UseFont="false" />
                </Item1>
                <Item2 Ref="56" ControlType="XRTableCell" Name="tableCell2" Weight="2.6431097412077462" Multiline="true" Text="Producto" Font="Times New Roman, 8.25pt, charSet=0">
                  <StylePriority Ref="57" UseFont="false" />
                </Item2>
                <Item3 Ref="58" ControlType="XRTableCell" Name="tableCell3" Weight="0.44181372787118" Multiline="true" Text="Hospital" Font="Times New Roman, 8.25pt, charSet=0">
                  <StylePriority Ref="59" UseFont="false" />
                </Item3>
                <Item4 Ref="60" ControlType="XRTableCell" Name="tableCell4" Weight="0.67844761484562277" Multiline="true" Text="No. Admisión" Font="Times New Roman, 8.25pt, charSet=0">
                  <StylePriority Ref="61" UseFont="false" />
                </Item4>
                <Item5 Ref="62" ControlType="XRTableCell" Name="tableCell5" Weight="0.53003451929861556" Multiline="true" Text="Cantidad" Font="Times New Roman, 8.25pt, charSet=0">
                  <StylePriority Ref="63" UseFont="false" />
                </Item5>
                <Item6 Ref="64" ControlType="XRTableCell" Name="tableCell6" Weight="0.67844793013186944" Multiline="true" Text="Tipo Cargo" Font="Times New Roman, 8.25pt, charSet=0">
                  <StylePriority Ref="65" UseFont="false" />
                </Item6>
                <Item7 Ref="66" ControlType="XRTableCell" Name="tableCell7" Weight="0.51943546626523074" Multiline="true" Text="Fecha" Font="Times New Roman, 8.25pt, charSet=0">
                  <StylePriority Ref="67" UseFont="false" />
                </Item7>
                <Item8 Ref="68" ControlType="XRTableCell" Name="tableCell8" Weight="0.4373363056146255" Multiline="true" Text="Bodega" Font="Times New Roman, 8.25pt, charSet=0">
                  <StylePriority Ref="69" UseFont="false" />
                </Item8>
                <Item9 Ref="70" ControlType="XRTableCell" Name="tableCell9" Weight="0.421322820104902" Multiline="true" Text="Liq" Font="Times New Roman, 8.25pt, charSet=0">
                  <StylePriority Ref="71" UseFont="false" />
                </Item9>
                <Item10 Ref="72" ControlType="XRTableCell" Name="tableCell10" Weight="0.71210060596467439" Multiline="true" Text="Costo C" Font="Times New Roman, 8.25pt, charSet=0">
                  <StylePriority Ref="73" UseFont="false" />
                </Item10>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="74" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item4>
    <Item5 Ref="75" ControlType="GroupFooterBand" Name="GroupFooter1" HeightF="27.70834">
      <Controls>
        <Item1 Ref="76" ControlType="XRLine" Name="line3" SizeF="731.609741,5.541642" LocationFloat="0,0" />
        <Item2 Ref="77" ControlType="XRLabel" Name="label5" TextFormatString="{0:N0}" Multiline="true" TextAlignment="TopLeft" SizeF="129.166962,21.9583511" LocationFloat="402.4423,5.54164267" Font="Times New Roman, 8.25pt, charSet=0" Padding="2,2,0,0,100">
          <Summary Ref="78" Running="Group" />
          <ExpressionBindings>
            <Item1 Ref="79" EventName="BeforePrint" PropertyName="Text" Expression="sumSum(Cantidad)" />
          </ExpressionBindings>
          <StylePriority Ref="80" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="81" ControlType="XRLabel" Name="label2" Multiline="true" Text="Total = " TextAlignment="TopLeft" SizeF="56.2501526,21.9583511" LocationFloat="346.192169,5.54164267" Font="Times New Roman, 8.25pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="82" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="83" ControlType="XRLabel" Name="label1" Multiline="true" TextAlignment="TopLeft" SizeF="320.902771,21.9583511" LocationFloat="10.0000067,5.54164267" Font="Times New Roman, 8.25pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="84" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
          </ExpressionBindings>
          <StylePriority Ref="85" UseFont="false" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item5>
    <Item6 Ref="86" ControlType="PageFooterBand" Name="PageFooter" HeightF="0" />
    <Item7 Ref="87" ControlType="GroupHeaderBand" Name="GroupHeader1" HeightF="0">
      <GroupFields>
        <Item1 Ref="88" FieldName="Codigo" />
      </GroupFields>
    </Item7>
    <Item8 Ref="89" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="48.9583321">
      <Controls>
        <Item1 Ref="90" ControlType="XRLabel" Name="label3" Multiline="true" Text="Autorizado por:" TextAlignment="BottomLeft" SizeF="100.011795,32.37502" LocationFloat="0,0" Font="Times New Roman, 8.25pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="91" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="92" ControlType="XRLine" Name="line1" SizeF="230.890976,2.083334" LocationFloat="100.011795,30.2916851" />
        <Item3 Ref="93" ControlType="XRLabel" Name="label4" Multiline="true" Text="Enterado:" TextAlignment="BottomLeft" SizeF="66.66687,32.37502" LocationFloat="346.192169,0" Font="Times New Roman, 8.25pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="94" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="95" ControlType="XRLine" Name="line2" SizeF="302.430542,2.083334" LocationFloat="412.8591,30.2916851" />
      </Controls>
    </Item8>
  </Bands>
</XtraReportsLayoutSerializer>