﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="LiquidacionDetalle" Margins="51, 53, 260, 100" PageWidth="850" PageHeight="1100" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" HeightF="259.75">
      <Controls>
        <Item1 Ref="2" ControlType="XRBarCode" Name="barCode1" Alignment="BottomCenter" TextAlignment="BottomCenter" SizeF="220.833313,116.666656" LocationFloat="481.609,12.08334" Padding="10,10,0,0,100">
          <Symbology Ref="3" Name="Code128" />
          <ExpressionBindings>
            <Item1 Ref="4" EventName="BeforePrint" PropertyName="Text" Expression="[Liquidacion]" />
          </ExpressionBindings>
          <StylePriority Ref="5" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="6" ControlType="XRLabel" Name="labelFechaLiquidacion" Multiline="true" TextAlignment="BottomLeft" SizeF="191.666748,30.2083435" LocationFloat="484.7339,201.250015" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="7" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="8" ControlType="XRLabel" Name="labelNitProveedor" Multiline="true" TextAlignment="BottomLeft" SizeF="191.666748,30.2083435" LocationFloat="484.7339,171.041687" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="9" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="10" ControlType="XRLabel" Name="label6" Multiline="true" Text="Fecha:" TextAlignment="BottomLeft" SizeF="82.291626,30.2083435" LocationFloat="402.4423,201.250015" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="11" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="12" ControlType="XRLabel" Name="label5" Multiline="true" Text="Nit:" TextAlignment="BottomLeft" SizeF="82.291626,30.2083435" LocationFloat="402.4423,171.041687" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="13" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="14" ControlType="XRLabel" Name="labelEstado" Multiline="true" TextAlignment="BottomLeft" SizeF="103.125092,30.2083435" LocationFloat="264.942322,201.250015" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="15" UseFont="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="16" ControlType="XRLabel" Name="label4" Multiline="true" Text="Estado:" TextAlignment="BottomLeft" SizeF="55.2083435,30.2083435" LocationFloat="209.733963,201.250015" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="17" UseFont="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="18" ControlType="XRLabel" Name="labelFactura" Multiline="true" TextAlignment="BottomLeft" SizeF="94.10899,30.2083435" LocationFloat="115.624969,201.250015" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="19" UseFont="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="20" ControlType="XRLabel" Name="labelOrdenCompra" Multiline="true" TextAlignment="BottomLeft" SizeF="86.4583,30.2083435" LocationFloat="115.625,171.041687" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="21" UseFont="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="22" ControlType="XRLabel" Name="labelProveedor" Multiline="true" TextAlignment="BottomLeft" SizeF="252.442352,30.2083435" LocationFloat="115.625,140.833344" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="23" UseFont="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="24" ControlType="XRLabel" Name="label3" Multiline="true" Text="Factura:" TextAlignment="BottomLeft" SizeF="115.625,30.2083435" LocationFloat="0,201.250015" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="25" UseFont="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="26" ControlType="XRLabel" Name="label2" Multiline="true" Text="Orden De Compra:" TextAlignment="BottomLeft" SizeF="115.625,30.2083435" LocationFloat="0,171.041687" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="27" UseFont="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="28" ControlType="XRLabel" Name="label1" Multiline="true" Text="Proveedor:" TextAlignment="BottomLeft" SizeF="115.625,30.2083435" LocationFloat="0,140.833344" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="29" UseFont="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="30" ControlType="XRLabel" Name="labelEmpresa" Multiline="true" Text="Reporte Detallado de Consumos de Productos en Consignación" TextAlignment="MiddleCenter" SizeF="445.833374,34.3749924" LocationFloat="24.3173752,94.37501" Font="Times New Roman, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="31" UseFont="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="32" ControlType="XRLabel" Name="labelNoLiquidacion" Multiline="true" TextAlignment="MiddleCenter" SizeF="445.8333,30.2083359" LocationFloat="24.3173752,64.16667" Font="Times New Roman, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="33" UseFont="false" UseTextAlignment="false" />
        </Item15>
        <Item16 Ref="34" ControlType="XRLabel" Name="labelNombeEmpresa" Multiline="true" TextAlignment="MiddleCenter" SizeF="445.833374,30.2083359" LocationFloat="24.3173447,10.0000067" Font="Times New Roman, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="35" UseFont="false" UseTextAlignment="false" />
        </Item16>
        <Item17 Ref="36" ControlType="XRLabel" Name="labelNitEmpresa" Multiline="true" TextAlignment="MiddleCenter" SizeF="239.583374,23.9583321" LocationFloat="128.484024,40.20834" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="37" UseFont="false" UseTextAlignment="false" />
        </Item17>
      </Controls>
    </Item1>
    <Item2 Ref="38" ControlType="BottomMarginBand" Name="BottomMargin" />
    <Item3 Ref="39" ControlType="DetailBand" Name="Detail" HeightF="13.5833426">
      <Controls>
        <Item1 Ref="40" ControlType="XRTable" Name="table2" TextAlignment="BottomLeft" SizeF="746.000061,13.3333206" LocationFloat="0,0" Font="Arial, 8.25pt, charSet=0" Padding="2,2,0,0,96" Borders="None">
          <Rows>
            <Item1 Ref="41" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="42" ControlType="XRTableCell" Name="tableCell9" Weight="0.41696124554735292" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="43" EventName="BeforePrint" PropertyName="Text" Expression="[Codigo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="44" UseFont="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="45" ControlType="XRTableCell" Name="tableCell11" Weight="3.0989399892508729" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="46" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="47" UseFont="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="48" ControlType="XRTableCell" Name="tableCell12" Weight="0.51872640969691886" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="49" EventName="BeforePrint" PropertyName="Text" Expression="[Hospital]" />
                  </ExpressionBindings>
                  <StylePriority Ref="50" UseFont="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="51" ControlType="XRTableCell" Name="tableCell13" Weight="0.57243975097595567" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="52" EventName="BeforePrint" PropertyName="Text" Expression="Concat([SerieAdmision], ' - ', [Admision])" />
                  </ExpressionBindings>
                  <StylePriority Ref="53" UseFont="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="54" ControlType="XRTableCell" Name="tableCell14" Weight="0.442523753206212" TextFormatString="{0:N0}" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="55" EventName="BeforePrint" PropertyName="Text" Expression="[Cantidad]" />
                  </ExpressionBindings>
                  <StylePriority Ref="56" UseFont="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="57" ControlType="XRTableCell" Name="tableCell15" Weight="0.61484248512779494" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="58" EventName="BeforePrint" PropertyName="Text" Expression="Concat([Tipo_Cargo], ' ', [Cargo])" />
                  </ExpressionBindings>
                  <StylePriority Ref="59" UseFont="false" UseTextAlignment="false" />
                </Item6>
                <Item7 Ref="60" ControlType="XRTableCell" Name="tableCell16" Weight="0.572440692193205" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="61" EventName="BeforePrint" PropertyName="Text" Expression="[Fecha]" />
                  </ExpressionBindings>
                  <StylePriority Ref="62" UseFont="false" UseTextAlignment="false" />
                </Item7>
                <Item8 Ref="63" ControlType="XRTableCell" Name="tableCell17" Weight="0.532740837283451" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="64" EventName="BeforePrint" PropertyName="Text" Expression="[Bodega]" />
                  </ExpressionBindings>
                  <StylePriority Ref="65" UseFont="false" UseTextAlignment="false" />
                </Item8>
                <Item9 Ref="66" ControlType="XRTableCell" Name="tableCell18" Weight="0.82218781159960164" TextFormatString="{0:C2}" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="67" EventName="BeforePrint" PropertyName="Text" Expression="[CostoConvenio]" />
                  </ExpressionBindings>
                  <StylePriority Ref="68" UseFont="false" UseTextAlignment="false" />
                </Item9>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="69" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="70" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="33.3333321">
      <Controls>
        <Item1 Ref="71" ControlType="XRTable" Name="table1" TextAlignment="BottomLeft" SizeF="746,33.3333321" LocationFloat="0,0" Font="Arial, 8.25pt, charSet=0" Padding="2,2,0,0,96" Borders="Top, Bottom">
          <Rows>
            <Item1 Ref="72" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="73" ControlType="XRTableCell" Name="tableCell1" Weight="0.41696124554735292" Multiline="true" Text="Codigo" Font="Times New Roman, 6.75pt, charSet=0">
                  <StylePriority Ref="74" UseFont="false" />
                </Item1>
                <Item2 Ref="75" ControlType="XRTableCell" Name="tableCell2" Weight="3.0989399892508724" Multiline="true" Text="Producto" Font="Times New Roman, 6.75pt, charSet=0">
                  <StylePriority Ref="76" UseFont="false" />
                </Item2>
                <Item3 Ref="77" ControlType="XRTableCell" Name="tableCell3" Weight="0.518726409696919" Multiline="true" Text="Hospital" Font="Times New Roman, 6.75pt, charSet=0">
                  <StylePriority Ref="78" UseFont="false" />
                </Item3>
                <Item4 Ref="79" ControlType="XRTableCell" Name="tableCell4" Weight="0.57244037211113386" Multiline="true" Text="No. Admisión" Font="Times New Roman, 6.75pt, charSet=0">
                  <StylePriority Ref="80" UseFont="false" />
                </Item4>
                <Item5 Ref="81" ControlType="XRTableCell" Name="tableCell5" Weight="0.44252188980067758" Multiline="true" Text="Cantidad" Font="Times New Roman, 6.75pt, charSet=0">
                  <StylePriority Ref="82" UseFont="false" />
                </Item5>
                <Item6 Ref="83" ControlType="XRTableCell" Name="tableCell6" Weight="0.6148437273981513" Multiline="true" Text="Cargo" Font="Times New Roman, 6.75pt, charSet=0">
                  <StylePriority Ref="84" UseFont="false" />
                </Item6>
                <Item7 Ref="85" ControlType="XRTableCell" Name="tableCell7" Weight="0.57244069219320481" Multiline="true" Text="Fecha" Font="Times New Roman, 6.75pt, charSet=0">
                  <StylePriority Ref="86" UseFont="false" />
                </Item7>
                <Item8 Ref="87" ControlType="XRTableCell" Name="tableCell8" Weight="0.532740837283451" Multiline="true" Text="Bodega" Font="Times New Roman, 6.75pt, charSet=0">
                  <StylePriority Ref="88" UseFont="false" />
                </Item8>
                <Item9 Ref="89" ControlType="XRTableCell" Name="tableCell10" Weight="0.82218719046442357" Multiline="true" Text="Costo C" Font="Times New Roman, 6.75pt, charSet=0">
                  <StylePriority Ref="90" UseFont="false" />
                </Item9>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="91" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item4>
    <Item5 Ref="92" ControlType="GroupHeaderBand" Name="GroupHeader1" HeightF="0">
      <GroupFields>
        <Item1 Ref="93" FieldName="Codigo" />
      </GroupFields>
    </Item5>
    <Item6 Ref="94" ControlType="GroupFooterBand" Name="GroupFooter1" HeightF="61.4583321">
      <Controls>
        <Item1 Ref="95" ControlType="XRLabel" Name="label7" Multiline="true" TextAlignment="TopLeft" SizeF="296.88678,32.3750153" LocationFloat="40.9722252,22.8333149" Font="Times New Roman, 8.25pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="96" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
          </ExpressionBindings>
          <StylePriority Ref="97" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="98" ControlType="XRLabel" Name="label8" Multiline="true" Text="Total = " TextAlignment="TopLeft" SizeF="56.2501526,32.37502" LocationFloat="396.4582,22.8333149" Font="Times New Roman, 8.25pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="99" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="100" ControlType="XRLabel" Name="label9" TextFormatString="{0:N0}" Multiline="true" TextAlignment="TopLeft" SizeF="185.417084,32.3750229" LocationFloat="452.708282,22.8333149" Font="Times New Roman, 8.25pt, charSet=0" Padding="2,2,0,0,100">
          <Summary Ref="101" Running="Group" />
          <ExpressionBindings>
            <Item1 Ref="102" EventName="BeforePrint" PropertyName="Text" Expression="sumSum(Cantidad)" />
          </ExpressionBindings>
          <StylePriority Ref="103" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="104" ControlType="XRLine" Name="line3" SizeF="689.942261,2.083334" LocationFloat="0,9.999974" />
      </Controls>
    </Item6>
    <Item7 Ref="105" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="48.54164">
      <Controls>
        <Item1 Ref="106" ControlType="XRLine" Name="line2" SizeF="245.833862,2.083334" LocationFloat="470.150757,30.2916851" />
        <Item2 Ref="107" ControlType="XRLabel" Name="label11" Multiline="true" Text="Proveedor:" TextAlignment="BottomLeft" SizeF="67.4425659,32.37502" LocationFloat="402.4423,0" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="108" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="109" ControlType="XRLine" Name="line1" SizeF="268.055542,2.083334" LocationFloat="69.80346,30.2916851" />
        <Item4 Ref="110" ControlType="XRLabel" Name="label10" Multiline="true" Text="Sermesa:" TextAlignment="BottomLeft" SizeF="69.80346,32.37502" LocationFloat="0,0" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="111" UseFont="false" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item7>
  </Bands>
</XtraReportsLayoutSerializer>