﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="ConveniosInactivos" Landscape="true" Margins="100, 100, 134, 100" PaperKind="Custom" PageWidth="2200" PageHeight="850" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" HeightF="134.375">
      <Controls>
        <Item1 Ref="2" ControlType="XRTable" Name="table2" TextAlignment="BottomLeft" SizeF="2000,32.2916679" LocationFloat="4.76837158E-05,102.083336" Font="Arial, 8.25pt, style=Bold, charSet=0" BackColor="SkyBlue" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="3" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="4" ControlType="XRTableCell" Name="tableCell13" Weight="0.61340200687218915" Multiline="true" Text="IdMovimiento" />
                <Item2 Ref="5" ControlType="XRTableCell" Name="tableCell14" Weight="0.34278326080714128" Multiline="true" Text="Linea" />
                <Item3 Ref="6" ControlType="XRTableCell" Name="tableCell1" Weight="0.53608289400434761" Multiline="true" Text="Proveedor" />
                <Item4 Ref="7" ControlType="XRTableCell" Name="tableCell15" Weight="0.54381386416674449" Multiline="true" Text="Producto" />
                <Item5 Ref="8" ControlType="XRTableCell" Name="tableCell16" Weight="0.62886758118452613" Multiline="true" Text="Fecha" />
                <Item6 Ref="9" ControlType="XRTableCell" Name="tableCell17" Weight="0.5917200943944414" Multiline="true" Text="Usuario" />
                <Item7 Ref="10" ControlType="XRTableCell" Name="tableCell18" Weight="0.84642420228069093" Multiline="true" Text="Convenio" />
                <Item8 Ref="11" ControlType="XRTableCell" Name="tableCell20" Weight="0.58443810024695586" Multiline="true" Text="IdConvenio" />
                <Item9 Ref="12" ControlType="XRTableCell" Name="tableCell21" Weight="4.7051216333804549" Multiline="true" Text="Informacion" />
                <Item10 Ref="13" ControlType="XRTableCell" Name="tableCell22" Weight="3.9551208116869176" Multiline="true" Text="Motivo" />
                <Item11 Ref="14" ControlType="XRTableCell" Name="tableCell23" Weight="0.76182244102304075" Multiline="true" Text="CostoC" />
                <Item12 Ref="15" ControlType="XRTableCell" Name="tableCell24" Weight="0.7357641624505078" Multiline="true" Text="PrecioC" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="16" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="17" ControlType="XRLabel" Name="label1" Multiline="true" Text="Servicios Medicos y Hospitalarios Centroamericanos" TextAlignment="BottomLeft" SizeF="552.777954,27.0833321" LocationFloat="0,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="18" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="19" ControlType="XRLabel" Name="label2" Multiline="true" Text="Productos sin Convenios de Consignación" TextAlignment="BottomLeft" SizeF="552.777954,27.08333" LocationFloat="0,27.083334" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="20" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="21" ControlType="XRLabel" Name="labelProveedor" Multiline="true" TextAlignment="BottomLeft" SizeF="552.777954,27.08333" LocationFloat="0,54.1666679" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="22" UseFont="false" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item1>
    <Item2 Ref="23" ControlType="BottomMarginBand" Name="BottomMargin" />
    <Item3 Ref="24" ControlType="DetailBand" Name="Detail">
      <Controls>
        <Item1 Ref="25" ControlType="XRTable" Name="table1" TextAlignment="BottomLeft" SizeF="2000,32.2916679" LocationFloat="0,0" Font="Arial, 8.25pt, charSet=0" BackColor="Transparent" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="26" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="27" ControlType="XRTableCell" Name="tableCell5" Weight="0.61340245829561146" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="28" EventName="BeforePrint" PropertyName="Text" Expression="[IdConvenioElimina]" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="29" ControlType="XRTableCell" Name="tableCell6" Weight="0.34278309202460316" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="30" EventName="BeforePrint" PropertyName="Text" Expression="[Linea]" />
                  </ExpressionBindings>
                </Item2>
                <Item3 Ref="31" ControlType="XRTableCell" Name="tableCell7" Weight="0.53608306300018083" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="32" EventName="BeforePrint" PropertyName="Text" Expression="[Proveedor]" />
                  </ExpressionBindings>
                </Item3>
                <Item4 Ref="33" ControlType="XRTableCell" Name="tableCell8" Weight="0.54381386420940436" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="34" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
                  </ExpressionBindings>
                </Item4>
                <Item5 Ref="35" ControlType="XRTableCell" Name="tableCell9" Weight="0.62886770374531387" TextFormatString="{0:d/MM/yyyy HH:mm:ss}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="36" EventName="BeforePrint" PropertyName="Text" Expression="[Fecha]" />
                  </ExpressionBindings>
                </Item5>
                <Item6 Ref="37" ControlType="XRTableCell" Name="tableCell10" Weight="0.59171998015216509" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="38" EventName="BeforePrint" PropertyName="Text" Expression="[Usuario]" />
                  </ExpressionBindings>
                </Item6>
                <Item7 Ref="39" ControlType="XRTableCell" Name="tableCell11" Weight="0.8464240884674894" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="40" EventName="BeforePrint" PropertyName="Text" Expression="[Convenio]" />
                  </ExpressionBindings>
                </Item7>
                <Item8 Ref="41" ControlType="XRTableCell" Name="tableCell25" Weight="0.58443832766254844" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="42" EventName="BeforePrint" PropertyName="Text" Expression="[IdConvenio]" />
                  </ExpressionBindings>
                </Item8>
                <Item9 Ref="43" ControlType="XRTableCell" Name="tableCell26" Weight="4.7051212863024094" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="44" EventName="BeforePrint" PropertyName="Text" Expression="[Informacion]" />
                  </ExpressionBindings>
                </Item9>
                <Item10 Ref="45" ControlType="XRTableCell" Name="tableCell27" Weight="3.9551205851646842" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="46" EventName="BeforePrint" PropertyName="Text" Expression="[Motivo]" />
                  </ExpressionBindings>
                </Item10>
                <Item11 Ref="47" ControlType="XRTableCell" Name="tableCell28" Weight="0.76182244102303986" TextFormatString="{0:C2}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="48" EventName="BeforePrint" PropertyName="Text" Expression="[CostoC]" />
                  </ExpressionBindings>
                </Item11>
                <Item12 Ref="49" ControlType="XRTableCell" Name="tableCell29" Weight="0.73576416245050869" TextFormatString="{0:C2}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="50" EventName="BeforePrint" PropertyName="Text" Expression="[PrecioC]" />
                  </ExpressionBindings>
                </Item12>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="51" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item3>
  </Bands>
</XtraReportsLayoutSerializer>