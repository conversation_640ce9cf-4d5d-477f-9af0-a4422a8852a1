﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="MovimientoNotaCredito" Landscape="true" Margins="100, 100, 127, 100" PageWidth="1100" PageHeight="850" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" HeightF="127.083336">
      <Controls>
        <Item1 Ref="2" ControlType="XRBarCode" Name="barCode1" Alignment="BottomCenter" TextAlignment="BottomCenter" SizeF="177.083313,84.375" LocationFloat="715.625,10.0000067" Padding="10,10,0,0,96">
          <Symbology Ref="3" Name="Code128" />
          <ExpressionBindings>
            <Item1 Ref="4" EventName="BeforePrint" PropertyName="Text" Expression="[NotaCredito]" />
          </ExpressionBindings>
          <StylePriority Ref="5" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="6" ControlType="XRLabel" Name="labelNitEmpresa" Multiline="true" TextAlignment="MiddleCenter" SizeF="505.2083,25.0000019" LocationFloat="198.541672,31.8750057" Font="Arial, 8.25pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="7" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="8" ControlType="XRLabel" Name="labelEmprsaSucursal" Multiline="true" TextAlignment="MiddleCenter" SizeF="505.2083,21.875" LocationFloat="198.5417,10.0000067" Font="Arial, 8.25pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="9" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="10" ControlType="XRLabel" Name="labelNombeMovimiento" Multiline="true" Text="MOVIMIENTOS" TextAlignment="MiddleCenter" SizeF="505.2083,30.2083359" LocationFloat="198.95871,79.79167" Font="Arial, 14.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="11" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="12" ControlType="XRLabel" Name="labelEmpresa" Multiline="true" TextAlignment="MiddleCenter" SizeF="505.208344,22.9166641" LocationFloat="198.958649,56.8750076" Font="Arial, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="13" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="14" ControlType="XRLabel" Name="labelFecha" Multiline="true" Text="labelFecha" TextAlignment="MiddleLeft" SizeF="154.166656,21.8749962" LocationFloat="0,10.0000067" Padding="2,2,0,0,100">
          <StylePriority Ref="15" UseTextAlignment="false" />
        </Item6>
      </Controls>
    </Item1>
    <Item2 Ref="16" ControlType="BottomMarginBand" Name="BottomMargin" />
    <Item3 Ref="17" ControlType="DetailBand" Name="Detail" HeightF="23.958334">
      <Controls>
        <Item1 Ref="18" ControlType="XRTable" Name="table1" TextAlignment="BottomLeft" SizeF="898.958252,23.958334" LocationFloat="1.04173028,0" Font="Arial, 8.25pt, charSet=0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="19" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="20" ControlType="XRTableCell" Name="tableCell1" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="21" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="22" ControlType="XRTableCell" Name="tableCell2" Weight="3.6249997120328734" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="23" EventName="BeforePrint" PropertyName="Text" Expression="[NombreProd]" />
                  </ExpressionBindings>
                </Item2>
                <Item3 Ref="24" ControlType="XRTableCell" Name="tableCell3" Weight="0.58035546469828425" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="25" EventName="BeforePrint" PropertyName="Text" Expression="[Cantidad]" />
                  </ExpressionBindings>
                </Item3>
                <Item4 Ref="26" ControlType="XRTableCell" Name="tableCell4" Weight="0.81785840851909941" TextFormatString="{0:C2}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="27" EventName="BeforePrint" PropertyName="Text" Expression="[Costo]" />
                  </ExpressionBindings>
                </Item4>
                <Item5 Ref="28" ControlType="XRTableCell" Name="tableCell5" Weight="0.81785840851909941" TextFormatString="{0:C2}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="29" EventName="BeforePrint" PropertyName="Text" Expression="[Devolucion]" />
                  </ExpressionBindings>
                </Item5>
                <Item6 Ref="30" ControlType="XRTableCell" Name="tableCell6" Weight="0.86428296007994965" TextFormatString="{0:C2}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="31" EventName="BeforePrint" PropertyName="Text" Expression="[Precio]" />
                  </ExpressionBindings>
                </Item6>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="32" UseFont="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="33" ControlType="PageHeaderBand" Name="PageHeader" HeightF="148.958328">
      <SubBands>
        <Item1 Ref="34" ControlType="SubBand" Name="SubBand1" HeightF="23.958334">
          <Controls>
            <Item1 Ref="35" ControlType="XRTable" Name="table2" TextAlignment="BottomLeft" SizeF="898.958252,23.958334" LocationFloat="1.04173028,0" Font="Arial, 8.25pt, charSet=0" Padding="2,2,0,0,96" Borders="Bottom">
              <Rows>
                <Item1 Ref="36" ControlType="XRTableRow" Name="tableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="37" ControlType="XRTableCell" Name="tableCell7" Weight="1" Multiline="true" Text="Código" />
                    <Item2 Ref="38" ControlType="XRTableCell" Name="tableCell8" Weight="3.6249997120328734" Multiline="true" Text="Descripción" />
                    <Item3 Ref="39" ControlType="XRTableCell" Name="tableCell9" Weight="0.58035546469828425" Multiline="true" Text="Cantidad" />
                    <Item4 Ref="40" ControlType="XRTableCell" Name="tableCell10" Weight="0.81785840851909941" Multiline="true" Text="Costo U." />
                    <Item5 Ref="41" ControlType="XRTableCell" Name="tableCell11" Weight="0.81785840851909941" Multiline="true" Text="Costo Total" />
                    <Item6 Ref="42" ControlType="XRTableCell" Name="tableCell12" Weight="0.86428296007994976" Multiline="true" Text="Valor" />
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="43" UseFont="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </SubBands>
      <Controls>
        <Item1 Ref="44" ControlType="XRLabel" Name="labelValidacion" Multiline="true" TextAlignment="BottomLeft" SizeF="165.625,28.125" LocationFloat="704.167,112.5" Padding="2,2,0,0,100">
          <StylePriority Ref="45" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="46" ControlType="XRLabel" Name="label17" Multiline="true" Text="Validación:" TextAlignment="BottomLeft" SizeF="153.125,28.125" LocationFloat="551.042,112.5" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="47" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="48" ControlType="XRLabel" Name="labelMotivo" Multiline="true" TextAlignment="BottomLeft" SizeF="396.874939,28.125" LocationFloat="154.166733,112.5" Padding="2,2,0,0,100">
          <StylePriority Ref="49" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="50" ControlType="XRLabel" Name="label15" Multiline="true" Text="Motivo:" TextAlignment="BottomLeft" SizeF="153.125,28.125" LocationFloat="1.04173028,112.5" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="51" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="52" ControlType="XRLabel" Name="labelExento" Multiline="true" TextAlignment="BottomLeft" SizeF="46.8751221,28.125" LocationFloat="822.916748,84.375" Padding="2,2,0,0,100">
          <StylePriority Ref="53" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="54" ControlType="XRLabel" Name="labelTextoExento" Multiline="true" Text="Exento de IVA (N/S):" TextAlignment="BottomLeft" SizeF="167.708374,28.125" LocationFloat="655.2084,84.375" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="55" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="56" ControlType="XRLabel" Name="labelRebaja" Multiline="true" TextAlignment="BottomLeft" SizeF="46.8751221,28.125" LocationFloat="608.333252,84.375" Padding="2,2,0,0,100">
          <StylePriority Ref="57" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="58" ControlType="XRLabel" Name="label13" Multiline="true" Text="Rebaja Existencias (N/S):" TextAlignment="BottomLeft" SizeF="204.1666,28.125" LocationFloat="404.166656,84.375" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="59" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="60" ControlType="XRLabel" Name="labelValorNota" TextFormatString="{0:C2}" Multiline="true" TextAlignment="BottomLeft" SizeF="165.625,28.125" LocationFloat="704.1669,56.25" Padding="2,2,0,0,100">
          <StylePriority Ref="61" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="62" ControlType="XRLabel" Name="label11" Multiline="true" Text="Valor:" TextAlignment="BottomLeft" SizeF="153.125,28.125" LocationFloat="551.041931,56.25" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="63" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="64" ControlType="XRLabel" Name="labelFactura" Multiline="true" TextAlignment="BottomLeft" SizeF="249.999924,28.125" LocationFloat="154.166733,84.375" Padding="2,2,0,0,100">
          <StylePriority Ref="65" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="66" ControlType="XRLabel" Name="labelProveedor" Multiline="true" TextAlignment="BottomLeft" SizeF="396.875,28.125" LocationFloat="154.1667,56.25" Padding="2,2,0,0,100">
          <StylePriority Ref="67" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="68" ControlType="XRLabel" Name="label5" Multiline="true" Text="Factura No.:" TextAlignment="BottomLeft" SizeF="153.125,28.125" LocationFloat="1.04169846,84.375" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="69" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="70" ControlType="XRLabel" Name="label4" Multiline="true" Text="Proveedor:" TextAlignment="BottomLeft" SizeF="153.125,28.125" LocationFloat="1.04169846,56.25" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="71" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="72" ControlType="XRLabel" Name="labelBodega" Multiline="true" TextAlignment="BottomLeft" SizeF="165.625122,28.125" LocationFloat="704.1669,28.125" Padding="2,2,0,0,100">
          <StylePriority Ref="73" UseTextAlignment="false" />
        </Item15>
        <Item16 Ref="74" ControlType="XRLabel" Name="labelFechaIngreso" Multiline="true" TextAlignment="BottomLeft" SizeF="165.625061,28.125" LocationFloat="704.1668,0" Padding="2,2,0,0,100">
          <StylePriority Ref="75" UseTextAlignment="false" />
        </Item16>
        <Item17 Ref="76" ControlType="XRLabel" Name="label8" Multiline="true" Text="Bodega:" TextAlignment="BottomLeft" SizeF="153.125,28.125" LocationFloat="551.0418,28.125" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="77" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item17>
        <Item18 Ref="78" ControlType="XRLabel" Name="labelTextFechaIngreso" Multiline="true" Text="Fecha Ingreso:" TextAlignment="BottomLeft" SizeF="153.125,28.125" LocationFloat="551.0418,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="79" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item18>
        <Item19 Ref="80" ControlType="XRLabel" Name="labelNFace" Multiline="true" TextAlignment="BottomLeft" SizeF="146.875031,28.125" LocationFloat="404.166656,28.125" Padding="2,2,0,0,100">
          <StylePriority Ref="81" UseTextAlignment="false" />
        </Item19>
        <Item20 Ref="82" ControlType="XRLabel" Name="labelFechaRegistro" Multiline="true" TextAlignment="BottomLeft" SizeF="146.875092,28.125" LocationFloat="404.166656,0" Padding="2,2,0,0,100">
          <StylePriority Ref="83" UseTextAlignment="false" />
        </Item20>
        <Item21 Ref="84" ControlType="XRLabel" Name="labelTextNface" Multiline="true" Text="NFACE:" TextAlignment="BottomLeft" SizeF="153.125,28.125" LocationFloat="251.0417,28.125" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="85" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item21>
        <Item22 Ref="86" ControlType="XRLabel" Name="label2" Multiline="true" Text="Fecha:" TextAlignment="BottomLeft" SizeF="153.125,28.125" LocationFloat="251.041672,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="87" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item22>
        <Item23 Ref="88" ControlType="XRLabel" Name="labelNumeroNota" Multiline="true" TextAlignment="BottomLeft" SizeF="96.875,28.125" LocationFloat="154.166672,28.125" Padding="2,2,0,0,100">
          <StylePriority Ref="89" UseTextAlignment="false" />
        </Item23>
        <Item24 Ref="90" ControlType="XRLabel" Name="labelTextNota" Multiline="true" Text="No. NC:" TextAlignment="BottomLeft" SizeF="153.125,28.125" LocationFloat="1.0416826,28.125" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="91" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item24>
        <Item25 Ref="92" ControlType="XRLabel" Name="labelCorrelativo" Multiline="true" TextAlignment="BottomLeft" SizeF="96.875,28.125" LocationFloat="154.166672,0" Padding="2,2,0,0,100">
          <StylePriority Ref="93" UseTextAlignment="false" />
        </Item25>
        <Item26 Ref="94" ControlType="XRLabel" Name="label7" Multiline="true" Text="Correlativo:" TextAlignment="BottomLeft" SizeF="153.125,28.125" LocationFloat="1.04166663,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="95" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item26>
      </Controls>
    </Item4>
    <Item5 Ref="96" ControlType="GroupFooterBand" Name="GroupFooter1">
      <Controls>
        <Item1 Ref="97" ControlType="XRLabel" Name="labelNombreEmpleado" Multiline="true" TextAlignment="BottomLeft" SizeF="249.999924,28.125" LocationFloat="154.166763,0" Padding="2,2,0,0,100">
          <StylePriority Ref="98" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="99" ControlType="XRLabel" Name="label12" Multiline="true" Text="Ingresado Por:" TextAlignment="BottomLeft" SizeF="153.124939,28.125" LocationFloat="1.04173028,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="100" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="101" ControlType="XRLabel" Name="label10" TextFormatString="{0:C2}" Multiline="true" TextAlignment="BottomLeft" SizeF="106.875,28.125" LocationFloat="793.125,0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="102" EventName="BeforePrint" PropertyName="Text" Expression="Sum([Precio])" />
          </ExpressionBindings>
          <StylePriority Ref="103" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="104" ControlType="XRLabel" Name="label9" Multiline="true" Text="Total:" TextAlignment="BottomLeft" SizeF="95.83356,28.125" LocationFloat="608.333252,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="105" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item5>
  </Bands>
</XtraReportsLayoutSerializer>