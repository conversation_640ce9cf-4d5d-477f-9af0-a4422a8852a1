﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="ReporteKardexProductoBodega" Landscape="true" Margins="77, 76, 151, 89" PaperKind="Custom" PageWidth="1478" PageHeight="850" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" HeightF="151.011368">
      <Controls>
        <Item1 Ref="2" ControlType="XRTable" Name="table3" SizeF="146.561859,14.4999542" LocationFloat="5.77984356E-05,83.1603" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="3" ControlType="XRTableRow" Name="tableRow3" Weight="1">
              <Cells>
                <Item1 Ref="4" ControlType="XRTableCell" Name="CodigoProducto" Weight="1" Multiline="true" TextAlignment="TopRight" Font="Arial, 8.25pt, style=Bold, charSet=0">
                  <StylePriority Ref="5" UseFont="false" UseTextAlignment="false" />
                </Item1>
              </Cells>
            </Item1>
          </Rows>
        </Item1>
        <Item2 Ref="6" ControlType="XRLabel" Name="label4" Multiline="true" CanGrow="false" Text="Bodega&#xD;&#xA;a:" TextAlignment="TopRight" SizeF="146.56192,14.4999771" LocationFloat="0,97.6602554" Font="Arial, 8.25pt, style=Bold, charSet=0" Padding="2,2,1,0,100" BorderWidth="0">
          <StylePriority Ref="7" UseFont="false" UsePadding="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="8" ControlType="XRLabel" Name="CodigoBodega" Multiline="true" TextAlignment="TopRight" SizeF="88.45324,14.4999695" LocationFloat="146.561935,97.66027" Font="Arial, 8.25pt, style=Bold, charSet=0" ForeColor="255,64,64,64" Padding="2,2,0,0,100" BorderWidth="0">
          <ExpressionBindings>
            <Item1 Ref="9" EventName="BeforePrint" PropertyName="Text" Expression="[CodigoBodega]" />
          </ExpressionBindings>
          <StylePriority Ref="10" UseFont="false" UseForeColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="11" ControlType="XRLabel" Name="NombreBodega" Multiline="true" TextAlignment="TopLeft" SizeF="314.1673,14.4999619" LocationFloat="235.0152,97.66028" Font="Arial, 8.25pt, style=Bold, charSet=0" ForeColor="255,64,64,64" Padding="2,2,0,0,100" BorderWidth="0">
          <ExpressionBindings>
            <Item1 Ref="12" EventName="BeforePrint" PropertyName="Text" Expression="[NombreBodega]" />
          </ExpressionBindings>
          <StylePriority Ref="13" UseFont="false" UseForeColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="14" ControlType="XRLabel" Name="NombreProducto" Multiline="true" TextAlignment="TopLeft" SizeF="402.620575,14.4999619" LocationFloat="146.56192,83.1603" Font="Arial, 8.25pt, style=Bold, charSet=0" ForeColor="255,64,64,64" Padding="2,2,0,0,100" BorderWidth="0">
          <ExpressionBindings>
            <Item1 Ref="15" EventName="BeforePrint" PropertyName="Text" Expression="[NombreProducto]" />
          </ExpressionBindings>
          <StylePriority Ref="16" UseFont="false" UseForeColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="17" ControlType="XRLabel" Name="label2" Multiline="true" CanShrink="true" Text="KARDEX MOVIMIENTOS Y CARGOS POR PRODUCTO " TextAlignment="MiddleCenter" SizeF="1265,14.6666746" LocationFloat="0,39.3333435" Font="Arial, 10pt, style=Bold" ForeColor="255,0,96,151" Padding="2,2,0,0,100">
          <StylePriority Ref="18" UseFont="false" UseForeColor="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="19" ControlType="XRLabel" Name="label1" Multiline="true" CanShrink="true" Text="INVENTARIOS" TextAlignment="MiddleCenter" SizeF="1265,14.6666746" LocationFloat="0,24.6666756" Font="Arial, 10pt, style=Bold" ForeColor="255,0,96,151" Padding="2,2,0,0,100">
          <StylePriority Ref="20" UseFont="false" UseForeColor="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="21" ControlType="XRLabel" Name="uiTitulo" Multiline="true" CanShrink="true" Text="SUMINISTROS DE BODEGA SERMESA" TextAlignment="MiddleCenter" SizeF="1265,14.6666746" LocationFloat="0,10" Font="Arial, 10pt, style=Bold" ForeColor="255,0,96,151" Padding="2,2,0,0,100">
          <StylePriority Ref="22" UseFont="false" UseForeColor="false" UseTextAlignment="false" />
        </Item8>
      </Controls>
    </Item1>
    <Item2 Ref="23" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="88.69835" />
    <Item3 Ref="24" ControlType="DetailBand" Name="Detail" HeightF="18.21" Font="Arial, 8pt">
      <Controls>
        <Item1 Ref="25" ControlType="XRTable" Name="table1" TextAlignment="TopLeft" SizeF="1325,18.21" LocationFloat="5.77984356E-05,0" Font="Arial, 8.25pt, charSet=0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="26" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="27" ControlType="XRTableCell" Name="Periodo" Weight="0.18799167152291574" Multiline="true" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="28" EventName="BeforePrint" PropertyName="Text" Expression="[Numero]" />
                  </ExpressionBindings>
                  <StylePriority Ref="29" UseFont="false" />
                </Item1>
                <Item2 Ref="30" ControlType="XRTableCell" Name="Producto" Weight="0.35858221387879152" Multiline="true" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="31" EventName="BeforePrint" PropertyName="Text" Expression="[Fecha]" />
                  </ExpressionBindings>
                  <StylePriority Ref="32" UseFont="false" />
                </Item2>
                <Item3 Ref="33" ControlType="XRTableCell" Name="NumeroSerie" Weight="0.58373410038613927" Multiline="true" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="34" EventName="BeforePrint" PropertyName="Text" Expression="[NombreMovimiento]" />
                  </ExpressionBindings>
                  <StylePriority Ref="35" UseFont="false" />
                </Item3>
                <Item4 Ref="36" ControlType="XRTableCell" Name="Hospital" Weight="0.24265638650495155" Multiline="true" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="37" EventName="BeforePrint" PropertyName="Text" Expression="[BodegaFuente]" />
                  </ExpressionBindings>
                  <StylePriority Ref="38" UseFont="false" />
                </Item4>
                <Item5 Ref="39" ControlType="XRTableCell" Name="tableCell15" Weight="0.21255579189500781" Multiline="true" Text="tableCell15" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="40" EventName="BeforePrint" PropertyName="Text" Expression="[BodegaDestino]" />
                  </ExpressionBindings>
                  <StylePriority Ref="41" UseFont="false" />
                </Item5>
                <Item6 Ref="42" ControlType="XRTableCell" Name="tableCell16" Weight="0.2187354972760302" Multiline="true" Text="tableCell16" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="43" EventName="BeforePrint" PropertyName="Text" Expression="[Entrada]" />
                  </ExpressionBindings>
                  <StylePriority Ref="44" UseFont="false" />
                </Item6>
                <Item7 Ref="45" ControlType="XRTableCell" Name="tableCell17" Weight="0.2488050997315707" Multiline="true" Text="tableCell17" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="46" EventName="BeforePrint" PropertyName="Text" Expression="[Salida]" />
                  </ExpressionBindings>
                  <StylePriority Ref="47" UseFont="false" />
                </Item7>
                <Item8 Ref="48" ControlType="XRTableCell" Name="tableCell18" Weight="0.486673507902194" Multiline="true" Text="tableCell18" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="49" EventName="BeforePrint" PropertyName="Text" Expression="[Proveedor]" />
                  </ExpressionBindings>
                  <StylePriority Ref="50" UseFont="false" />
                </Item8>
                <Item9 Ref="51" ControlType="XRTableCell" Name="tableCell19" Weight="0.24922020022192071" Multiline="true" Text="tableCell19" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="52" EventName="BeforePrint" PropertyName="Text" Expression="[NotaCredito]" />
                  </ExpressionBindings>
                  <StylePriority Ref="53" UseFont="false" />
                </Item9>
                <Item10 Ref="54" ControlType="XRTableCell" Name="tableCell20" Weight="0.33419818998041706" Multiline="true" Text="tableCell20" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="55" EventName="BeforePrint" PropertyName="Text" Expression="[CostoPromedio]" />
                  </ExpressionBindings>
                  <StylePriority Ref="56" UseFont="false" />
                </Item10>
                <Item11 Ref="57" ControlType="XRTableCell" Name="tableCell21" Weight="0.28582508483960206" Multiline="true" Text="tableCell21" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="58" EventName="BeforePrint" PropertyName="Text" Expression="[CostoUltimo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="59" UseFont="false" />
                </Item11>
                <Item12 Ref="60" ControlType="XRTableCell" Name="tableCell22" Weight="0.2543329829701747" Multiline="true" Text="tableCell22" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="61" EventName="BeforePrint" PropertyName="Text" Expression="[TotalCalculado]" />
                  </ExpressionBindings>
                  <StylePriority Ref="62" UseFont="false" />
                </Item12>
                <Item13 Ref="63" ControlType="XRTableCell" Name="tableCell23" Weight="0.31666340060370551" Multiline="true" Text="tableCell23" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="64" EventName="BeforePrint" PropertyName="Text" Expression="[CostoTotal]" />
                  </ExpressionBindings>
                  <StylePriority Ref="65" UseFont="false" />
                </Item13>
                <Item14 Ref="66" ControlType="XRTableCell" Name="tableCell24" Weight="0.23897589169478045" Multiline="true" Text="tableCell24" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="67" EventName="BeforePrint" PropertyName="Text" Expression="[CantidadAceptada]" />
                  </ExpressionBindings>
                  <StylePriority Ref="68" UseFont="false" />
                </Item14>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="69" UseFont="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
      <StylePriority Ref="70" UseFont="false" />
    </Item3>
    <Item4 Ref="71" ControlType="GroupHeaderBand" Name="GroupHeader1" HeightF="19.3340759">
      <Controls>
        <Item1 Ref="72" ControlType="XRTable" Name="table2" KeepTogether="true" TextAlignment="TopLeft" SizeF="1325,19.3340759" LocationFloat="5.77984356E-05,0" Padding="0,0,2,2,100" BorderColor="255,0,96,151">
          <Rows>
            <Item1 Ref="73" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="74" ControlType="XRTableCell" Name="tableCell8" Weight="0.35669454310238868" Multiline="true" Text="Número" Font="Arial, 8pt, style=Bold" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" Borders="None">
                  <StylePriority Ref="75" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorders="false" />
                </Item1>
                <Item2 Ref="76" ControlType="XRTableCell" Name="FechaAtencion" Weight="0.680371283625306" Multiline="true" Text="FechaAtención" Font="Arial, 8pt, style=Bold" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="77" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item2>
                <Item3 Ref="78" ControlType="XRTableCell" Name="tableCell7" Weight="1.1075729277271587" Multiline="true" Text="TipoMovimiento" Font="Arial, 8pt, style=Bold" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="79" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item3>
                <Item4 Ref="80" ControlType="XRTableCell" Name="tableCell1" Weight="0.46041479107084149" Multiline="true" Text="Origen" Font="Arial, 8pt, style=Bold" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="81" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item4>
                <Item5 Ref="82" ControlType="XRTableCell" Name="tableCell2" Weight="0.40330138768416995" Multiline="true" Text="Destino" Font="Arial, 8pt, style=Bold" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="83" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item5>
                <Item6 Ref="84" ControlType="XRTableCell" Name="tableCell3" Weight="0.41502766097645827" Multiline="true" Text="Ingreso" Font="Arial, 8pt, style=Bold" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="85" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item6>
                <Item7 Ref="86" ControlType="XRTableCell" Name="tableCell4" Weight="0.47208155049699618" Multiline="true" Text="Egreso" Font="Arial, 8pt, style=Bold" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="87" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item7>
                <Item8 Ref="88" ControlType="XRTableCell" Name="tableCell5" Weight="0.9234114829355794" Multiline="true" Text="Proveedor/Factura" Font="Arial, 8pt, style=Bold" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="89" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item8>
                <Item9 Ref="90" ControlType="XRTableCell" Name="tableCell9" Weight="0.*****************" Multiline="true" Text="Nota Credito" Font="Arial, 8pt, style=Bold" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="91" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item9>
                <Item10 Ref="92" ControlType="XRTableCell" Name="tableCell10" Weight="0.63410452278601181" Multiline="true" Text="Costo Promedio" Font="Arial, 8pt, style=Bold" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="93" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item10>
                <Item11 Ref="94" ControlType="XRTableCell" Name="tableCell11" Weight="0.542320792062756" Multiline="true" Text="Costo Ultimo" Font="Arial, 8pt, style=Bold" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="95" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item11>
                <Item12 Ref="96" ControlType="XRTableCell" Name="tableCell12" Weight="0.48257193337914439" Multiline="true" Text="Saldo" Font="Arial, 8pt, style=Bold" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="97" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item12>
                <Item13 Ref="98" ControlType="XRTableCell" Name="tableCell13" Weight="0.60083504766168594" Multiline="true" Text="Costo Total" Font="Arial, 8pt, style=Bold" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="99" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item13>
                <Item14 Ref="100" ControlType="XRTableCell" Name="tableCell14" Weight="0.45343148824259383" Multiline="true" Text="Cantidad" Font="Arial, 8pt, style=Bold" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="101" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item14>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="102" UsePadding="false" UseBorderColor="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item4>
    <Item5 Ref="103" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="50">
      <Controls>
        <Item1 Ref="104" ControlType="XRTable" Name="table5" SizeF="78.13983,24.9999981" LocationFloat="566.643,24.99999" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="105" ControlType="XRTableRow" Name="tableRow5" Weight="1">
              <Cells>
                <Item1 Ref="106" ControlType="XRTableCell" Name="ExistenciaBodega" Weight="1" Multiline="true" Font="Arial, 9pt, style=Bold, charSet=0">
                  <StylePriority Ref="107" UseFont="false" />
                </Item1>
              </Cells>
            </Item1>
          </Rows>
        </Item1>
        <Item2 Ref="108" ControlType="XRLabel" Name="label5" Multiline="true" Text="Existencia en Bodega :" SizeF="211.659363,24.99999" LocationFloat="354.983643,24.99999" Font="Arial, 9pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="109" UseFont="false" />
        </Item2>
        <Item3 Ref="110" ControlType="XRTable" Name="table4" SizeF="146.835541,25" LocationFloat="497.947266,0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="111" ControlType="XRTableRow" Name="tableRow4" Weight="1">
              <Cells>
                <Item1 Ref="112" ControlType="XRTableCell" Name="SumaIngresos" Weight="0.68695831298828125" Multiline="true" Font="Arial, 9pt, style=Bold, charSet=0">
                  <StylePriority Ref="113" UseFont="false" />
                </Item1>
                <Item2 Ref="114" ControlType="XRTableCell" Name="SumaEgresos" Weight="0.78139709472656249" Multiline="true" Font="Arial, 9pt, style=Bold, charSet=0">
                  <StylePriority Ref="115" UseFont="false" />
                </Item2>
              </Cells>
            </Item1>
          </Rows>
        </Item3>
        <Item4 Ref="116" ControlType="XRLabel" Name="label3" Multiline="true" Text="Total por movimiento" SizeF="142.963531,24.9999924" LocationFloat="354.983643,0" Font="Arial, 9pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="117" UseFont="false" />
        </Item4>
      </Controls>
    </Item5>
  </Bands>
</XtraReportsLayoutSerializer>