﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="PsicotropicoUnProductoIntegrado" Landscape="true" PaperKind="Legal" PageWidth="1400" PageHeight="850" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" />
    <Item2 Ref="2" ControlType="BottomMarginBand" Name="BottomMargin">
      <Controls>
        <Item1 Ref="3" ControlType="XRLabel" Name="label4" Multiline="true" Text="Revisado por:____________________________________________" TextAlignment="BottomLeft" SizeF="444.038452,37.5" LocationFloat="755.9616,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="4" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="5" ControlType="XRLabel" Name="label3" Multiline="true" Text="Generado o Elaborado por:___________________________________" TextAlignment="BottomLeft" SizeF="444.038452,37.5" LocationFloat="0,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="6" UseFont="false" UseTextAlignment="false" />
        </Item2>
      </Controls>
    </Item2>
    <Item3 Ref="7" ControlType="DetailBand" Name="Detail" HeightF="39.58664">
      <Controls>
        <Item1 Ref="8" ControlType="XRTable" Name="table1" SizeF="1042.01306,39.58" LocationFloat="0,0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="9" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="10" ControlType="XRTableCell" Name="tableCell1" Weight="0.58808286173711055" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="11" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="12" ControlType="XRTableCell" Name="tableCell2" Weight="1.9715026588053886" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="13" EventName="BeforePrint" PropertyName="Text" Expression="[NombreProducto]" />
                  </ExpressionBindings>
                </Item2>
                <Item3 Ref="14" ControlType="XRTableCell" Name="tableCell3" Weight="0.37823843701340504" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="15" EventName="BeforePrint" PropertyName="Text" Expression="[Inicial]" />
                  </ExpressionBindings>
                </Item3>
                <Item4 Ref="16" ControlType="XRTableCell" Name="tableCell4" Weight="0.79015503733807124" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="17" EventName="BeforePrint" PropertyName="Text" Expression="[TransaccionEntrada]" />
                  </ExpressionBindings>
                </Item4>
                <Item5 Ref="18" ControlType="XRTableCell" Name="tableCell5" Weight="0.51036207134354439" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="19" EventName="BeforePrint" PropertyName="Text" Expression="[Compras]" />
                  </ExpressionBindings>
                </Item5>
                <Item6 Ref="20" ControlType="XRTableCell" Name="tableCell6" Weight="0.51036207134354439" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="21" EventName="BeforePrint" PropertyName="Text" Expression="[TrasladosE]" />
                  </ExpressionBindings>
                </Item6>
                <Item7 Ref="22" ControlType="XRTableCell" Name="tableCell7" Weight="0.51036207134354439" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="23" EventName="BeforePrint" PropertyName="Text" Expression="[TotalIngresos]" />
                  </ExpressionBindings>
                </Item7>
                <Item8 Ref="24" ControlType="XRTableCell" Name="tableCell8" Weight="0.79792697793862688" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="25" EventName="BeforePrint" PropertyName="Text" Expression="[TransaccionSalida]" />
                  </ExpressionBindings>
                </Item8>
                <Item9 Ref="26" ControlType="XRTableCell" Name="tableCell9" Weight="0.57253807091572129" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="27" EventName="BeforePrint" PropertyName="Text" Expression="[Cargos]" />
                  </ExpressionBindings>
                </Item9>
                <Item10 Ref="28" ControlType="XRTableCell" Name="tableCell10" Weight="0.57253807091572129" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="29" EventName="BeforePrint" PropertyName="Text" Expression="[TrasladosS]" />
                  </ExpressionBindings>
                </Item10>
                <Item11 Ref="30" ControlType="XRTableCell" Name="tableCell11" Weight="0.57253807091572129" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="31" EventName="BeforePrint" PropertyName="Text" Expression="[TotalSalidas]" />
                  </ExpressionBindings>
                </Item11>
              </Cells>
            </Item1>
          </Rows>
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="32" ControlType="PageHeaderBand" Name="PageHeader" HeightF="197.916672">
      <SubBands>
        <Item1 Ref="33" ControlType="SubBand" Name="SubBand1" HeightF="39.58">
          <Controls>
            <Item1 Ref="34" ControlType="XRTable" Name="table2" TextAlignment="BottomLeft" SizeF="1042.01306,39.58" LocationFloat="0,0" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="SkyBlue" Padding="2,2,0,0,96">
              <Rows>
                <Item1 Ref="35" ControlType="XRTableRow" Name="tableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="36" ControlType="XRTableCell" Name="tableCell12" Weight="0.58808286173711055" Multiline="true" Text="Producto" />
                    <Item2 Ref="37" ControlType="XRTableCell" Name="tableCell13" Weight="1.9715026588053886" Multiline="true" Text="Nombre Producto" />
                    <Item3 Ref="38" ControlType="XRTableCell" Name="tableCell14" Weight="0.37823843701340504" Multiline="true" Text="Inicial" />
                    <Item4 Ref="39" ControlType="XRTableCell" Name="tableCell15" Weight="0.79015503733807124" Multiline="true" Text="Transaccion Entrada" />
                    <Item5 Ref="40" ControlType="XRTableCell" Name="tableCell16" Weight="0.51036207134354439" Multiline="true" Text="Compras" />
                    <Item6 Ref="41" ControlType="XRTableCell" Name="tableCell17" Weight="0.51036207134354439" Multiline="true" Text="Traslados" />
                    <Item7 Ref="42" ControlType="XRTableCell" Name="tableCell18" Weight="0.51036207134354439" Multiline="true" Text="Total Ingresos" />
                    <Item8 Ref="43" ControlType="XRTableCell" Name="tableCell19" Weight="0.79792697793862688" Multiline="true" Text="Transaccion Salida" />
                    <Item9 Ref="44" ControlType="XRTableCell" Name="tableCell20" Weight="0.57253807091572129" Multiline="true" Text="Cargos" />
                    <Item10 Ref="45" ControlType="XRTableCell" Name="tableCell21" Weight="0.57253807091572129" Multiline="true" Text="Traslados" />
                    <Item11 Ref="46" ControlType="XRTableCell" Name="tableCell22" Weight="0.57253807091572129" Multiline="true" Text="Total Salidas" />
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="47" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </SubBands>
      <Controls>
        <Item1 Ref="48" ControlType="XRLabel" Name="labelProducto" Multiline="true" Text="Producto:" TextAlignment="BottomLeft" SizeF="499.652679,39.5833321" LocationFloat="0,158.333328" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="49" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="50" ControlType="XRLabel" Name="labelSucursalFamilia" Multiline="true" Text="Sucursal: Familia:" TextAlignment="BottomLeft" SizeF="499.652679,39.5833321" LocationFloat="0,118.75" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="51" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="52" ControlType="XRLabel" Name="labelFechas" Multiline="true" Text="Del: Al." TextAlignment="BottomLeft" SizeF="499.652679,39.5833321" LocationFloat="0,79.1666641" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="53" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="54" ControlType="XRLabel" Name="label2" Multiline="true" Text="Reporte de Psicotropicos / Estupefacientes" TextAlignment="BottomLeft" SizeF="499.652679,39.5833321" LocationFloat="0,39.5833321" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="55" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="56" ControlType="XRLabel" Name="label1" Multiline="true" Text="RESUMEN DE MOVIMIENTOS DE INVENTARIO POR PRODUCTO" TextAlignment="BottomLeft" SizeF="499.652679,39.5833321" LocationFloat="0,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="57" UseFont="false" UseTextAlignment="false" />
        </Item5>
      </Controls>
    </Item4>
  </Bands>
</XtraReportsLayoutSerializer>