﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.13.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.13.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="BitacoraMontosLimite" Margins="100, 100, 184, 100" PageWidth="850" PageHeight="1100" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" HeightF="184">
      <Controls>
        <Item1 Ref="2" ControlType="XRLabel" Name="label4" Multiline="true" Text="Bitacora Montos Limite" TextAlignment="TopCenter" SizeF="278.571472,23" LocationFloat="185.714279,114.166672" Font="Arial, 14pt, style=Bold" Padding="2,2,0,0,100">
          <StylePriority Ref="3" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="4" ControlType="XRLabel" Name="labelEmpresa" Multiline="true" TextAlignment="TopCenter" SizeF="278.571472,23" LocationFloat="185.714279,68.75" Padding="2,2,0,0,100">
          <StylePriority Ref="5" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="6" ControlType="XRLabel" Name="labelUsuario" Multiline="true" TextAlignment="TopRight" SizeF="185.714279,23.0000038" LocationFloat="464.285736,28.125" Padding="2,2,0,0,100">
          <StylePriority Ref="7" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="8" ControlType="XRLabel" Name="labelFecha" Multiline="true" SizeF="185.714279,23.0000038" LocationFloat="0,28.125" Padding="2,2,0,0,100" />
      </Controls>
    </Item1>
    <Item2 Ref="9" ControlType="BottomMarginBand" Name="BottomMargin">
      <Controls>
        <Item1 Ref="10" ControlType="XRPageInfo" Name="pageInfo1" TextFormatString="Página {0} de {1}" TextAlignment="TopRight" SizeF="142.708344,23.0000038" LocationFloat="497.2917,28.125" Padding="2,2,0,0,100">
          <StylePriority Ref="11" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item2>
    <Item3 Ref="12" ControlType="DetailBand" Name="Detail" HeightF="25">
      <Controls>
        <Item1 Ref="13" ControlType="XRTable" Name="tableDetalle" SizeF="650,25" LocationFloat="0,0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="14" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="15" ControlType="XRTableCell" Name="tableCell2" Weight="0.528846153846154" Multiline="true" Text="[corporativo]" Font="Arial, 8.25pt, charSet=0">
                  <StylePriority Ref="16" UseFont="false" />
                </Item1>
                <Item2 Ref="17" ControlType="XRTableCell" Name="tableCell3" Weight="1.471153846153846" CanGrow="false" TextFitMode="ShrinkOnly" Text="[nombreCorporativo]" WordWrap="false" Font="Arial, 8.25pt, charSet=0">
                  <StylePriority Ref="18" UseFont="false" />
                </Item2>
                <Item3 Ref="19" ControlType="XRTableCell" Name="tableCell8" Weight="0.9999998767559346" CanGrow="false" TextFitMode="ShrinkOnly" Text="[fecha]" TextAlignment="TopLeft" WordWrap="false" Font="Arial, 8.25pt, charSet=0">
                  <StylePriority Ref="20" UseFont="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="21" ControlType="XRTableCell" Name="tableCell9" Weight="0.43910282428448005" Multiline="true" Text="[bodega]" Font="Arial, 8.25pt, charSet=0">
                  <StylePriority Ref="22" UseFont="false" />
                </Item4>
                <Item5 Ref="23" ControlType="XRTableCell" Name="tableCell10" Weight="1.5608972989595853" CanGrow="false" TextFitMode="ShrinkOnly" Text="[nombreBodega]" WordWrap="false" Font="Arial, 8.25pt, charSet=0">
                  <StylePriority Ref="24" UseFont="false" />
                </Item5>
                <Item6 Ref="25" ControlType="XRTableCell" Name="valorAnterior" Weight="1" CanShrink="true" TextFormatString="{0:C2}" TextAlignment="TopRight" Font="Arial, 8.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="26" EventName="BeforePrint" PropertyName="Text" Expression="ToFloat([valorAnterior])" />
                  </ExpressionBindings>
                  <StylePriority Ref="27" UseFont="false" UseTextAlignment="false" />
                </Item6>
                <Item7 Ref="28" ControlType="XRTableCell" Name="valorActual" Weight="1" CanShrink="true" TextFormatString="{0:C2}" TextAlignment="TopRight" Font="Arial, 8.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="29" EventName="BeforePrint" PropertyName="Text" Expression="ToFloat([valorActual])" />
                  </ExpressionBindings>
                  <StylePriority Ref="30" UseFont="false" UseTextAlignment="false" />
                </Item7>
              </Cells>
            </Item1>
          </Rows>
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="31" ControlType="PageHeaderBand" Name="PageHeader" HeightF="0">
      <SubBands>
        <Item1 Ref="32" ControlType="SubBand" Name="SubBand1" HeightF="32.2916679">
          <Controls>
            <Item1 Ref="33" ControlType="XRTable" Name="tableCabecera" SizeF="650,29.166666" LocationFloat="0,0" BackColor="Lavender" Padding="2,2,0,0,96">
              <Rows>
                <Item1 Ref="34" ControlType="XRTableRow" Name="tableRow1" Weight="1">
                  <Cells>
                    <Item1 Ref="35" ControlType="XRTableCell" Name="tableCell7" Weight="0.528846153846154" Multiline="true" Text="Corp." Font="Arial, 9.75pt, style=Bold">
                      <StylePriority Ref="36" UseFont="false" />
                    </Item1>
                    <Item2 Ref="37" ControlType="XRTableCell" Name="tableCell6" Weight="1.471153846153846" Multiline="true" Text="Nombre" Font="Arial, 9.75pt, style=Bold">
                      <StylePriority Ref="38" UseFont="false" />
                    </Item2>
                    <Item3 Ref="39" ControlType="XRTableCell" Name="tableCell5" Weight="1.000000534057617" Multiline="true" Text="Fecha" Font="Arial, 9.75pt, style=Bold">
                      <StylePriority Ref="40" UseFont="false" />
                    </Item3>
                    <Item4 Ref="41" ControlType="XRTableCell" Name="tableCell4" Weight="0.43910216698279758" Multiline="true" Text="Cod." Font="Arial, 9.75pt, style=Bold">
                      <StylePriority Ref="42" UseFont="false" />
                    </Item4>
                    <Item5 Ref="43" ControlType="XRTableCell" Name="tableCell1" Weight="1.5608972989595853" Multiline="true" Text="Bodega" Font="Arial, 9.75pt, style=Bold">
                      <StylePriority Ref="44" UseFont="false" />
                    </Item5>
                    <Item6 Ref="45" ControlType="XRTableCell" Name="valorAnteriorTitulo" Weight="1" Multiline="true" Text="valorAnteriorTitulo" TextAlignment="TopCenter" Font="Arial, 9.75pt, style=Bold">
                      <StylePriority Ref="46" UseFont="false" UseTextAlignment="false" />
                    </Item6>
                    <Item7 Ref="47" ControlType="XRTableCell" Name="valorActualTitulo" Weight="1" Multiline="true" Text="valorActualTitulo" TextAlignment="TopCenter" Font="Arial, 9.75pt, style=Bold">
                      <StylePriority Ref="48" UseFont="false" UseTextAlignment="false" />
                    </Item7>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="49" UseBackColor="false" />
            </Item1>
          </Controls>
        </Item1>
      </SubBands>
    </Item4>
  </Bands>
</XtraReportsLayoutSerializer>