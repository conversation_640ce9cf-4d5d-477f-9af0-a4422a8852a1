﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="MovimientoNuevo" Landscape="true" Margins="100, 100, 100, 72" PageWidth="1100" PageHeight="850" Version="20.1">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="topMarginBand1">
      <Controls>
        <Item1 Ref="2" ControlType="XRLabel" Name="labelFecha" Multiline="true" Text="labelFecha" TextAlignment="MiddleLeft" SizeF="154.166656,21.8749962" LocationFloat="0,10.0000067" Padding="2,2,0,0,100">
          <StylePriority Ref="3" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="4" ControlType="XRLabel" Name="labelEmpresa" Multiline="true" Text="labelEmpresa" TextAlignment="MiddleCenter" SizeF="300,34.3750038" LocationFloat="298.958344,10.0000067" Font="Arial, 14.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="5" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="6" ControlType="XRLabel" Name="labelUsuario" Multiline="true" Text="labelUsuario&#xD;&#xA;" TextAlignment="MiddleRight" SizeF="152.083252,21.8749962" LocationFloat="746.875061,10.0000067" Padding="2,2,0,0,100">
          <StylePriority Ref="7" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="8" ControlType="XRLabel" Name="labelNombeMovimiento" Multiline="true" Text="MOVIMIENTOS" TextAlignment="MiddleCenter" SizeF="301.0417,30.2083359" LocationFloat="297.9167,59.79166" Font="Arial, 14.25pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="9" UseFont="false" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item1>
    <Item2 Ref="10" ControlType="DetailBand" Name="detailBand1" HeightF="47.9166679">
      <Controls>
        <Item1 Ref="11" ControlType="XRTable" Name="table3" SizeF="897.9164,45.8333359" LocationFloat="1.04166663,0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="12" ControlType="XRTableRow" Name="tableRow3" Weight="1">
              <Cells>
                <Item1 Ref="13" ControlType="XRTableCell" Name="tableCell11" Weight="0.50167203640769664" Multiline="true" TextAlignment="MiddleLeft" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="14" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="15" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="16" ControlType="XRTableCell" Name="tableCell12" Weight="2.4734410547349843" Multiline="true" TextAlignment="MiddleLeft" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="17" EventName="BeforePrint" PropertyName="Text" Expression="[NombreProducto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="18" UseBorders="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="19" ControlType="XRTableCell" Name="tableCell13" Weight="0.62073075975083358" Multiline="true" TextAlignment="MiddleLeft" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="20" EventName="BeforePrint" PropertyName="Text" Expression="[Descripcion]" />
                  </ExpressionBindings>
                  <StylePriority Ref="21" UseBorders="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="22" ControlType="XRTableCell" Name="tableCell14" Weight="0.4960738219049069" Multiline="true" TextAlignment="MiddleRight" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="23" EventName="BeforePrint" PropertyName="Text" Expression="[Cantidad]" />
                  </ExpressionBindings>
                  <StylePriority Ref="24" UseBorders="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="25" ControlType="XRTableCell" Name="tableCell15" Weight="0.49213798714598478" Multiline="true" TextAlignment="MiddleRight" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="26" EventName="BeforePrint" PropertyName="Text" Expression="[Cantidad]" />
                  </ExpressionBindings>
                  <StylePriority Ref="27" UseBorders="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="28" ControlType="XRTableCell" Name="tableCell3" Weight="0.67920543814548917" TextFormatString="{0:C2}" Multiline="true" TextAlignment="MiddleRight" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="29" EventName="BeforePrint" PropertyName="Text" Expression="[CostoUnitario]" />
                  </ExpressionBindings>
                  <StylePriority Ref="30" UseBorders="false" UseTextAlignment="false" />
                </Item6>
                <Item7 Ref="31" ControlType="XRTableCell" Name="tableCell4" Weight="0.70902401040690211" TextFormatString="{0:C2}" Multiline="true" TextAlignment="MiddleRight" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="32" EventName="BeforePrint" PropertyName="Text" Expression="[Cantidad] * [CostoUnitario]" />
                  </ExpressionBindings>
                  <StylePriority Ref="33" UseBorders="false" UseTextAlignment="false" />
                </Item7>
              </Cells>
            </Item1>
          </Rows>
        </Item1>
      </Controls>
    </Item2>
    <Item3 Ref="34" ControlType="BottomMarginBand" Name="bottomMarginBand1" HeightF="71.875">
      <Controls>
        <Item1 Ref="35" ControlType="XRLine" Name="line2" SizeF="193.402878,4.166666" LocationFloat="690.241638,23.958334" />
        <Item2 Ref="36" ControlType="XRLabel" Name="label2" Multiline="true" Text="REVISADO INGRESO:" TextAlignment="BottomRight" SizeF="191.666687,28.125" LocationFloat="498.574951,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="37" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="38" ControlType="XRLabel" Name="labelRevisadoOperado" Multiline="true" TextAlignment="BottomRight" SizeF="292.708344,28.125" LocationFloat="1.04166663,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="39" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="40" ControlType="XRLine" Name="line1" SizeF="193.402878,4.166666" LocationFloat="293.75,23.958334" />
      </Controls>
    </Item3>
    <Item4 Ref="41" ControlType="PageHeaderBand" Name="PageHeader" HeightF="236.458328">
      <SubBands>
        <Item1 Ref="42" ControlType="SubBand" Name="SubBand1" HeightF="44.7916679">
          <Controls>
            <Item1 Ref="43" ControlType="XRTable" Name="table2" SizeF="897.9164,44.7916679" LocationFloat="1.04166663,0" Padding="2,2,0,0,96">
              <Rows>
                <Item1 Ref="44" ControlType="XRTableRow" Name="tableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="45" ControlType="XRTableCell" Name="tableCell6" Weight="0.58202045534590741" Multiline="true" Text="Producto" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightSkyBlue" Borders="All">
                      <StylePriority Ref="46" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="47" ControlType="XRTableCell" Name="tableCell7" Weight="2.8695899578583104" Multiline="true" Text="Descripcion" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightSkyBlue" Borders="All">
                      <StylePriority Ref="48" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item2>
                    <Item3 Ref="49" ControlType="XRTableCell" Name="tableCell8" Weight="0.72014816550783967" Multiline="true" Text="Presentacion" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightSkyBlue" Borders="All">
                      <StylePriority Ref="50" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item3>
                    <Item4 Ref="51" ControlType="XRTableCell" Name="tableCell9" Weight="0.57552460304712061" Multiline="true" Text="Cantidad Despachada" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightSkyBlue" Borders="All">
                      <StylePriority Ref="52" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item4>
                    <Item5 Ref="53" ControlType="XRTableCell" Name="tableCell10" Weight="0.57095980060722118" Multiline="true" Text="Cantidad Recibida" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightSkyBlue" Borders="All">
                      <StylePriority Ref="54" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item5>
                    <Item6 Ref="55" ControlType="XRTableCell" Name="tableCell1" Weight="0.78798770557903053" Multiline="true" Text="Costo" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightSkyBlue" Borders="All">
                      <StylePriority Ref="56" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item6>
                    <Item7 Ref="57" ControlType="XRTableCell" Name="tableCell2" Weight="0.82258205257277983" Multiline="true" Text="Costo Total" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="LightSkyBlue" Borders="All">
                      <StylePriority Ref="58" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item7>
                  </Cells>
                </Item1>
              </Rows>
            </Item1>
          </Controls>
        </Item1>
      </SubBands>
      <Controls>
        <Item1 Ref="59" ControlType="XRLabel" Name="labelObservaciones" Multiline="true" TextAlignment="TopLeft" SizeF="436.874756,94.16669" LocationFloat="462.083435,140.625" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="60" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="61" ControlType="XRLabel" Name="label19" Multiline="true" Text="Observaciones" TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="462.083374,112.5" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="62" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="63" ControlType="XRLabel" Name="labelEstadoMovimiento" Multiline="true" TextAlignment="BottomLeft" SizeF="267.0832,28.125" LocationFloat="631.875061,84.375" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="64" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="65" ControlType="XRLabel" Name="label17" Multiline="true" Text="Estado" TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="462.083374,84.375" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="66" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="67" ControlType="XRLabel" Name="labelFechaAceptado" Multiline="true" TextAlignment="BottomLeft" SizeF="267.0832,28.125" LocationFloat="631.8751,56.25" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="68" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="69" ControlType="XRLabel" Name="label15" Multiline="true" Text="Acep." TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="462.0833,56.25" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="70" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="71" ControlType="XRLabel" Name="labelFechaDespacho" Multiline="true" TextAlignment="BottomLeft" SizeF="267.082947,28.125" LocationFloat="631.8751,28.125" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="72" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="73" ControlType="XRLabel" Name="label13" Multiline="true" Text="Desp." TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="462.0833,28.125" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="74" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="75" ControlType="XRLabel" Name="labelFechaRegistro" Multiline="true" TextAlignment="BottomLeft" SizeF="267.08313,28.125" LocationFloat="631.875061,0" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="76" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="77" ControlType="XRLabel" Name="label11" Multiline="true" Text="Reg." TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="462.0833,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="78" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="79" ControlType="XRLabel" Name="labelABodega" Multiline="true" TextAlignment="BottomLeft" SizeF="282.7166,28.125" LocationFloat="165.625,112.5" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="80" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="81" ControlType="XRLabel" Name="labelDeBodega" Multiline="true" TextAlignment="BottomLeft" SizeF="282.7166,28.125" LocationFloat="165.625,84.375" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="82" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="83" ControlType="XRLabel" Name="label8" Multiline="true" Text="A Bodega" TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="1.04166663,112.5" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="84" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="85" ControlType="XRLabel" Name="label6" Multiline="true" Text="De Bodega" TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="1.04166663,84.375" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="86" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="87" ControlType="XRLabel" Name="labelTipoMovimiento" Multiline="true" TextAlignment="BottomLeft" SizeF="282.7166,28.125" LocationFloat="165.625,56.25" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="88" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item15>
        <Item16 Ref="89" ControlType="XRLabel" Name="label4" Multiline="true" Text="Tipo" TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="1.04166663,56.25" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="90" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item16>
        <Item17 Ref="91" ControlType="XRLabel" Name="labelFechaMovimiento" Multiline="true" TextAlignment="BottomLeft" SizeF="282.7166,28.125" LocationFloat="165.625,28.125" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="92" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item17>
        <Item18 Ref="93" ControlType="XRLabel" Name="label1" Multiline="true" Text="Fecha" TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="1.0416826,28.125" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="94" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item18>
        <Item19 Ref="95" ControlType="XRLabel" Name="labelMovimiento" Multiline="true" TextAlignment="BottomLeft" SizeF="282.7166,28.125" LocationFloat="165.625,0" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="96" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item19>
        <Item20 Ref="97" ControlType="XRLabel" Name="label7" Multiline="true" Text="Movimiento" TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="1.04166663,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="98" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item20>
      </Controls>
    </Item4>
    <Item5 Ref="99" ControlType="GroupFooterBand" Name="GroupFooter1" HeightF="28.125">
      <Controls>
        <Item1 Ref="100" ControlType="XRLabel" Name="label5" TextFormatString="{0:C2}" Multiline="true" TextAlignment="BottomRight" SizeF="154.124939,28.125" LocationFloat="745.875061,0" Font="Arial, 12pt, style=Bold, charSet=0" Padding="2,2,0,0,100" Borders="Bottom" BorderDashStyle="Double">
          <ExpressionBindings>
            <Item1 Ref="101" EventName="BeforePrint" PropertyName="Text" Expression="Sum([Cantidad] * [CostoUnitario])" />
          </ExpressionBindings>
          <StylePriority Ref="102" UseFont="false" UsePadding="false" UseBorders="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="103" ControlType="XRLabel" Name="label3" Multiline="true" Text="GRAN TOTAL:" TextAlignment="BottomLeft" SizeF="191.666687,28.125" LocationFloat="0,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="104" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item2>
      </Controls>
    </Item5>
  </Bands>
</XtraReportsLayoutSerializer>