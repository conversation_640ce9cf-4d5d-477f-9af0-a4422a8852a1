﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="DetalleMovimiento" Landscape="true" Margins="100, 100, 100, 68" PageWidth="1100" PageHeight="850" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin">
      <Controls>
        <Item1 Ref="2" ControlType="XRLabel" Name="labelNombreMovimiento" Multiline="true" Text="MOVIMIENTOS" TextAlignment="MiddleCenter" SizeF="301.0417,30.2083359" LocationFloat="298.958344,59.79166" Font="Arial, 14.25pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="3" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="4" ControlType="XRLabel" Name="labelUsuario" Multiline="true" Text="labelUsuario&#xD;&#xA;" TextAlignment="MiddleRight" SizeF="152.083252,21.8749962" LocationFloat="747.916748,10.0000067" Padding="2,2,0,0,100">
          <StylePriority Ref="5" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="6" ControlType="XRLabel" Name="labelEmpresa" Multiline="true" Text="labelEmpresa" TextAlignment="MiddleCenter" SizeF="300,34.3750038" LocationFloat="300,10.0000067" Font="Arial, 14.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="7" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="8" ControlType="XRLabel" Name="labelFecha" Multiline="true" Text="labelFecha" TextAlignment="MiddleLeft" SizeF="154.166656,21.8749962" LocationFloat="1.04166663,10.0000067" Padding="2,2,0,0,100">
          <StylePriority Ref="9" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item1>
    <Item2 Ref="10" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="67.7083359">
      <Controls>
        <Item1 Ref="11" ControlType="XRLine" Name="line1" SizeF="193.402878,4.166666" LocationFloat="293.75,23.958334" />
        <Item2 Ref="12" ControlType="XRLabel" Name="labelRevisadoOperado" Multiline="true" TextAlignment="BottomRight" SizeF="292.708344,28.125" LocationFloat="1.04166663,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="13" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="14" ControlType="XRLabel" Name="label14" Multiline="true" Text="REVISADO INGRESO:" TextAlignment="BottomRight" SizeF="191.666687,28.125" LocationFloat="498.574951,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="15" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="16" ControlType="XRLine" Name="line2" SizeF="193.402878,4.166666" LocationFloat="690.241638,23.958334" />
      </Controls>
    </Item2>
    <Item3 Ref="17" ControlType="DetailBand" Name="Detail" HeightF="28.125">
      <Controls>
        <Item1 Ref="18" ControlType="XRTable" Name="table3" SizeF="898.9583,23.958334" LocationFloat="1.04166663,0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="19" ControlType="XRTableRow" Name="tableRow3" Weight="1">
              <Cells>
                <Item1 Ref="20" ControlType="XRTableCell" Name="tableCell11" Weight="0.50167203640769664" Multiline="true" TextAlignment="TopLeft" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="21" EventName="BeforePrint" PropertyName="Text" Expression="[PRODUCTO]" />
                  </ExpressionBindings>
                  <StylePriority Ref="22" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="23" ControlType="XRTableCell" Name="tableCell12" Weight="2.3435943154156522" Multiline="true" TextAlignment="TopLeft" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="24" EventName="BeforePrint" PropertyName="Text" Expression="[DESCRIPCION_PRODUCTO]" />
                  </ExpressionBindings>
                  <StylePriority Ref="25" UseBorders="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="26" ControlType="XRTableCell" Name="tableCell13" Weight="0.86991187390155167" Multiline="true" TextAlignment="TopLeft" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="27" EventName="BeforePrint" PropertyName="Text" Expression="[PRESENTACION]" />
                  </ExpressionBindings>
                  <StylePriority Ref="28" UseBorders="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="29" ControlType="XRTableCell" Name="tableCell14" Weight="0.48088640610546823" Multiline="true" TextAlignment="TopLeft" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="30" EventName="BeforePrint" PropertyName="Text" Expression="[CANTIDAD]" />
                  </ExpressionBindings>
                  <StylePriority Ref="31" UseBorders="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="32" ControlType="XRTableCell" Name="tableCell15" Weight="0.75802359512539585" TextFormatString="{0:C2}" Multiline="true" TextAlignment="TopLeft" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="33" EventName="BeforePrint" PropertyName="Text" Expression="[COSTO_ULTIMO]" />
                  </ExpressionBindings>
                  <StylePriority Ref="34" UseBorders="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="35" ControlType="XRTableCell" Name="tableCell2" Weight="1.0251270911912127" TextFormatString="{0:C2}" Multiline="true" Text="tableCell2" TextAlignment="TopLeft" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="36" EventName="BeforePrint" PropertyName="Text" Expression="[COSTO]" />
                  </ExpressionBindings>
                  <StylePriority Ref="37" UseBorders="false" UseTextAlignment="false" />
                </Item6>
              </Cells>
            </Item1>
          </Rows>
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="38" ControlType="PageHeaderBand" Name="PageHeader" HeightF="168.75">
      <SubBands>
        <Item1 Ref="39" ControlType="SubBand" Name="SubBand1" HeightF="44.7916679">
          <Controls>
            <Item1 Ref="40" ControlType="XRTable" Name="table2" SizeF="898.9583,44.7916679" LocationFloat="1.04166663,0" Padding="2,2,0,0,96">
              <Rows>
                <Item1 Ref="41" ControlType="XRTableRow" Name="tableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="42" ControlType="XRTableCell" Name="tableCell6" Weight="0.58202045534590741" Multiline="true" Text="Producto" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="43" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="44" ControlType="XRTableCell" Name="tableCell7" Weight="2.7189468199647306" Multiline="true" Text="Descripcion" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="45" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item2>
                    <Item3 Ref="46" ControlType="XRTableCell" Name="tableCell8" Weight="1.0092379282454691" Multiline="true" Text="Presentacion" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="47" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item3>
                    <Item4 Ref="48" ControlType="XRTableCell" Name="tableCell9" Weight="0.55790569060234707" Multiline="true" Text="Cantidad" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="49" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item4>
                    <Item5 Ref="50" ControlType="XRTableCell" Name="tableCell10" Weight="0.87942947217100309" Multiline="true" Text="Costo Unitario" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="51" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item5>
                    <Item6 Ref="52" ControlType="XRTableCell" Name="tableCell1" Weight="1.1893125534761728" Multiline="true" Text="SubTotal" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="53" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item6>
                  </Cells>
                </Item1>
              </Rows>
            </Item1>
          </Controls>
        </Item1>
      </SubBands>
      <Controls>
        <Item1 Ref="54" ControlType="XRLabel" Name="labelComentario" Multiline="true" TextAlignment="TopLeft" SizeF="403.08316,56.25" LocationFloat="496.91684,112.5" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="55" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="56" ControlType="XRLabel" Name="label13" Multiline="true" Text="Referencia" TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="496.91684,84.375" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="57" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="58" ControlType="XRLabel" Name="labelStatusInventario" Multiline="true" TextAlignment="BottomLeft" SizeF="215.774719,28.125" LocationFloat="684.2253,56.25" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="59" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="60" ControlType="XRLabel" Name="label12" Multiline="true" Text="Estado" TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="496.91684,56.25" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="61" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="62" ControlType="XRLabel" Name="labelCodigoMovimeinto" Multiline="true" TextAlignment="BottomLeft" SizeF="152.5083,28.125" LocationFloat="684.2252,28.125" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="63" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="64" ControlType="XRLabel" Name="label6" Multiline="true" Text="Validacion" TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="496.916779,28.125" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="65" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="66" ControlType="XRLabel" Name="labelFechaRegistro" Multiline="true" TextAlignment="BottomLeft" SizeF="152.5083,28.125" LocationFloat="684.2251,0" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="67" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="68" ControlType="XRLabel" Name="label5" Multiline="true" Text="FechaRegistro" TextAlignment="BottomLeft" SizeF="174.999969,28.125" LocationFloat="496.916779,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="69" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="70" ControlType="XRLabel" Name="labelFechaMov" TextFormatString="{0:d/MM/yyyy}" Multiline="true" TextAlignment="BottomLeft" SizeF="144.175,28.125" LocationFloat="165.625,28.125" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="71" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="72" ControlType="XRLabel" Name="label1" Multiline="true" Text="Fecha" TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="1.0416826,28.125" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="73" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="74" ControlType="XRLabel" Name="labelCodigoOrden" Multiline="true" TextAlignment="BottomLeft" SizeF="243.133377,28.125" LocationFloat="165.625,140.625" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="75" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="76" ControlType="XRLabel" Name="labelBodegaDestino" Multiline="true" TextAlignment="BottomLeft" SizeF="244.175034,28.125" LocationFloat="164.583328,112.5" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="77" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="78" ControlType="XRLabel" Name="labelProveedor" Multiline="true" TextAlignment="BottomLeft" SizeF="285.841766,28.125" LocationFloat="165.625,84.375" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="79" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="80" ControlType="XRLabel" Name="labelFactura" Multiline="true" TextAlignment="BottomLeft" SizeF="244.175034,28.125" LocationFloat="165.625,56.25" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="81" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="82" ControlType="XRLabel" Name="labelContrasena" Multiline="true" TextAlignment="BottomLeft" SizeF="144.175,28.125" LocationFloat="165.625,0" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="83" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item15>
        <Item16 Ref="84" ControlType="XRLabel" Name="label7" Multiline="true" Text="Contraseña" TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="1.04166663,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="85" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item16>
        <Item17 Ref="86" ControlType="XRLabel" Name="label8" Multiline="true" Text="Factura" TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="1.04166663,56.25" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="87" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item17>
        <Item18 Ref="88" ControlType="XRLabel" Name="label9" Multiline="true" Text="Proveedor" TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="1.04166663,84.375" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="89" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item18>
        <Item19 Ref="90" ControlType="XRLabel" Name="label10" Multiline="true" Text="Bodega" TextAlignment="BottomLeft" SizeF="154.166672,28.125" LocationFloat="1.04166663,112.5" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="91" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item19>
        <Item20 Ref="92" ControlType="XRLabel" Name="label11" Multiline="true" Text="No. Orden" TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="1.04166663,140.625" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="93" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item20>
      </Controls>
    </Item4>
    <Item5 Ref="94" ControlType="GroupFooterBand" Name="GroupFooter1" HeightF="29.166666">
      <Controls>
        <Item1 Ref="95" ControlType="XRLabel" Name="label4" TextFormatString="{0:C2}" Multiline="true" Text="[MOVIMIENTO]" TextAlignment="BottomRight" SizeF="154.124939,28.125" LocationFloat="745.875061,0" Font="Arial, 12pt, style=Bold, charSet=0" Padding="2,2,0,0,100" Borders="Bottom" BorderDashStyle="Double">
          <ExpressionBindings>
            <Item1 Ref="96" EventName="BeforePrint" PropertyName="Text" Expression="Sum([COSTO])" />
          </ExpressionBindings>
          <StylePriority Ref="97" UseFont="false" UsePadding="false" UseBorders="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="98" ControlType="XRLabel" Name="label3" Multiline="true" Text="Gran Total:" TextAlignment="BottomLeft" SizeF="154.166656,28.125" LocationFloat="1.04169846,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="99" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item2>
      </Controls>
    </Item5>
  </Bands>
</XtraReportsLayoutSerializer>