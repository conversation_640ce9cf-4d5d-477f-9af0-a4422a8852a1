﻿using DevExpress.XtraPrinting;
using Modelo.Conexion;
using Radiologia;
using Reporte.Estructura;
using Reporte.Reporte;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Reporte.Models
{
    public class ReporteConsumosPorDepartamentoModel
    {


        private IConexion conexion;

        public ReporteConsumosPorDepartamentoModel()
        {
            String Usuario = Startup.Conexion["Tag_Usuario"].ToString();
            String Password = Startup.Conexion["Tag_Password"].ToString();

            String Instancia = Startup.Conexion["Instancia"].ToString();
            DBManager db = new DBManager(Usuario, Password, Instancia);
            conexion = db.ObtenerConexion();
        }


        public MemoryStream GeneraReporteConsumosPorDepartamento(EstructuraSesion consumosDepartamento)
        { 
            ReporteConsumosPorDepartamento reporteConsumosPorDepartamento = new ReporteConsumosPorDepartamento();
            List<SqlParameter> parametrosEntrada = new List<SqlParameter>();
            DataTable resultados = new DataTable();
            DataSet datosProductos = new DataSet();

            try
            {
                parametrosEntrada.Add(conexion.crearParametro("@iEmpresaBodega", SqlDbType.VarChar, consumosDepartamento.session_empresa_bodega));
                parametrosEntrada.Add(conexion.crearParametro("@iListaDepartamentos", SqlDbType.VarChar, consumosDepartamento.opciones.ListaDepartamentos));
                parametrosEntrada.Add(conexion.crearParametro("@iListaProductos", SqlDbType.VarChar, consumosDepartamento.opciones.ListaProductos));
                parametrosEntrada.Add(conexion.crearParametro("@iFechaIncial", SqlDbType.Date, consumosDepartamento.opciones.FechaInicio));
                parametrosEntrada.Add(conexion.crearParametro("@iFechaFinal", SqlDbType.Date, consumosDepartamento.opciones.FechaFin));


                resultados = conexion.getTableBySP("HOSPITAL", "spINVConsumosPorDepartamento", parametrosEntrada);

                DataTable inventarioExistencias = resultados;//.ToDataTable();




                datosProductos.Tables.Add(resultados);
                reporteConsumosPorDepartamento.DataSource = datosProductos;

                reporteConsumosPorDepartamento.FindControl("FechaDesde", true).Text = consumosDepartamento.opciones.FechaInicio.Value.ToString("dd/MM/yyyy");
                reporteConsumosPorDepartamento.FindControl("FechaHasta", true).Text = consumosDepartamento.opciones.FechaFin.Value.ToString("dd/MM/yyyy");
                reporteConsumosPorDepartamento.FindControl("CodDepartamento", true).Text = consumosDepartamento.opciones.ListaDepartamentos.Replace("|", ", ").ToString();


                using (MemoryStream ms2 = new MemoryStream())
                {
                    PdfExportOptions pdfOptions = reporteConsumosPorDepartamento.ExportOptions.Pdf;
                    pdfOptions.ConvertImagesToJpeg = false;
                    pdfOptions.ImageQuality = PdfJpegImageQuality.High;
                    pdfOptions.PdfACompatibility = PdfACompatibility.PdfA3b;
                    pdfOptions.DocumentOptions.Author = "Sighos";
                    pdfOptions.DocumentOptions.Keywords = "Sighos, Reporte, PDF";
                    //pdfOptions.DocumentOptions.Producer = Environment.UserName.ToString();
                    pdfOptions.DocumentOptions.Subject = "Reporte";
                    pdfOptions.DocumentOptions.Title = "Reporte";

                    if (consumosDepartamento.opciones.tiporeporte == "text/csv") reporteConsumosPorDepartamento.ExportToCsv(ms2);
                    if (consumosDepartamento.opciones.tiporeporte == "application/pdf") reporteConsumosPorDepartamento.ExportToPdf(ms2, pdfOptions);
                    if (consumosDepartamento.opciones.tiporeporte == "application/vnd.ms-excel") reporteConsumosPorDepartamento.ExportToXlsx(ms2);

                    ms2.Seek(0, System.IO.SeekOrigin.Begin);
                    conexion.closeConexion();
                    conexion = null;
                    reporteConsumosPorDepartamento.DataSource = null;
                    reporteConsumosPorDepartamento.Dispose();
                    reporteConsumosPorDepartamento = null;
                    System.GC.Collect(2);
                    System.GC.WaitForFullGCComplete();

                    return ms2;
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex);
            }
        }

    }
}
