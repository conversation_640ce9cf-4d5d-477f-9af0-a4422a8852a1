﻿using Modelo.Conexion;
using Radiologia;
using Reporte.Estructura;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Reporte.Models
{ 
    public class ConsultasReportesInventarioModel
    {

        private IConexion conexion;


        public ConsultasReportesInventarioModel()
        {
            String Usuario = Startup.Conexion["Tag_Usuario"].ToString();
            String Password = Startup.Conexion["Tag_Password"].ToString();

            String Instancia = Startup.Conexion["Instancia"].ToString();
            DBManager db = new DBManager(Usuario, Password, Instancia);
            conexion = db.ObtenerConexion();
        }


        public string ConsultarBodegasActivas (EstructuraSesion estructuraSesion)
        {

            List<SqlParameter> parametrosInventario = new List<SqlParameter>();
            ArrayList keys = new ArrayList();
            string resp;

            try {

                parametrosInventario.Add(conexion.crearParametro("@EmpresaBodega", SqlDbType.VarChar, estructuraSesion.session_empresa_bodega));

                string paciente = conexion.jsonTable("spINVConsultaBodegas", "HOSPITAL", parametrosInventario, keys);

                return paciente;
            }
            catch (Exception ex) {

                return resp = "";
            }

        }



    }
}
