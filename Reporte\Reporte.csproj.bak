<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp2.2</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="wwwroot\**" />
    <Content Remove="wwwroot\**" />
    <EmbeddedResource Remove="wwwroot\**" />
    <None Remove="wwwroot\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Reporte\Estado_Cuenta.repx" />
    <None Remove="Reporte\Estado_Cuenta_Factura.repx" />
    <None Remove="Reporte\Memo.repx" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DevExpress.Reporting.CodeCompletion" Version="19.2.3" />
    <PackageReference Include="DevExpress.Reporting.Core" Version="19.2.3" />
    <PackageReference Include="Microsoft.AspNetCore.All" Version="2.0.9" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="2.2.3" />
    <PackageReference Include="MySql.Data" Version="8.0.18" />
    <PackageReference Include="Newtonsoft.Json" Version="12.0.2" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="4.4.*" />
    <PackageReference Include="System.ServiceModel.Http" Version="4.4.*" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="4.4.*" />
    <PackageReference Include="System.ServiceModel.Security" Version="4.4.*" />
  </ItemGroup>

  <ItemGroup>
    <DotNetCliToolReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Tools" Version="2.0.4" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Reporte\Estado_Cuenta_Factura.repx" />
    <EmbeddedResource Include="Reporte\Estado_Cuenta.repx" />
    <EmbeddedResource Include="Reporte\Memo.repx" />
  </ItemGroup>

  <ItemGroup>
    <WCFMetadata Include="Connected Services" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Connected Services\" />
    <Folder Include="DLL\" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Conexion">
      <HintPath>Conexion.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Reporte\Estado_Cuenta_Factura.cs">
      <DependentUpon>Estado_Cuenta_Factura.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\Estado_Cuenta_Factura.Designer.cs">
      <DependentUpon>Estado_Cuenta_Factura.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\Estado_Cuenta.cs">
      <DependentUpon>Estado_Cuenta.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\Estado_Cuenta.Designer.cs">
      <DependentUpon>Estado_Cuenta.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\Memo.cs">
      <DependentUpon>Memo.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\Memo.Designer.cs">
      <DependentUpon>Memo.repx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ProjectExtensions><VisualStudio><UserProperties Properties_4launchSettings_1json__JSONSchema="" /></VisualStudio></ProjectExtensions>

</Project>
