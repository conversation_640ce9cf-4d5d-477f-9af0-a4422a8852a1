﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="ExistenciaPorProducto" Landscape="true" Margins="100, 100, 106, 100" PageWidth="1100" PageHeight="850" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" HeightF="106.25">
      <Controls>
        <Item1 Ref="2" ControlType="XRLabel" Name="label1" Multiline="true" Text="Existencias por Producto" TextAlignment="MiddleCenter" SizeF="592.013855,30.2083359" LocationFloat="155.208328,74.58334" Font="Arial, 14.25pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="3" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="4" ControlType="XRLabel" Name="labelNombreMovimiento" Multiline="true" Text="INVENTARIOS" TextAlignment="MiddleCenter" SizeF="592.013855,30.2083359" LocationFloat="155.208328,44.3750076" Font="Arial, 14.25pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="5" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="6" ControlType="XRLabel" Name="labelEmpresa" Multiline="true" Text="labelEmpresa" TextAlignment="MiddleCenter" SizeF="592.013855,34.3750038" LocationFloat="155.208328,10.0000067" Font="Arial, 14.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="7" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="8" ControlType="XRLabel" Name="labelFecha" Multiline="true" Text="labelFecha" TextAlignment="MiddleLeft" SizeF="154.166656,34.375" LocationFloat="1.04166663,10.0000067" Padding="2,2,0,0,100">
          <StylePriority Ref="9" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="10" ControlType="XRLabel" Name="labelUsuario" Multiline="true" Text="labelUsuario&#xD;&#xA;" TextAlignment="MiddleRight" SizeF="152.083252,34.375" LocationFloat="747.916748,10.0000067" Padding="2,2,0,0,100">
          <StylePriority Ref="11" UseTextAlignment="false" />
        </Item5>
      </Controls>
    </Item1>
    <Item2 Ref="12" ControlType="BottomMarginBand" Name="BottomMargin" />
    <Item3 Ref="13" ControlType="DetailBand" Name="Detail" HeightF="31.25">
      <Controls>
        <Item1 Ref="14" ControlType="XRTable" Name="table1" TextAlignment="BottomLeft" SizeF="900,31.25" LocationFloat="0,0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="15" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="16" ControlType="XRTableCell" Name="tableCell1" Weight="0.60410555014991929" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="17" EventName="BeforePrint" PropertyName="Text" Expression="[codigo]" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="18" ControlType="XRTableCell" Name="tableCell2" Weight="3.7624633252911224" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="19" EventName="BeforePrint" PropertyName="Text" Expression="[Nombre]" />
                  </ExpressionBindings>
                </Item2>
                <Item3 Ref="20" ControlType="XRTableCell" Name="tableCell3" Weight="0.683284955201334" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="21" EventName="BeforePrint" PropertyName="Text" Expression="[SumofExistencia]" />
                  </ExpressionBindings>
                </Item3>
                <Item4 Ref="22" ControlType="XRTableCell" Name="tableCell4" Weight="0.66568878750578775" TextFormatString="{0:N}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="23" EventName="BeforePrint" PropertyName="Text" Expression="[CostoAlto]" />
                  </ExpressionBindings>
                </Item4>
                <Item5 Ref="24" ControlType="XRTableCell" Name="tableCell5" Weight="0.59530772370745277" TextFormatString="{0:N}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="25" EventName="BeforePrint" PropertyName="Text" Expression="[CostoPromedio]" />
                  </ExpressionBindings>
                </Item5>
                <Item6 Ref="26" ControlType="XRTableCell" Name="tableCell6" Weight="0.63049799835174514" TextFormatString="{0:N}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="27" EventName="BeforePrint" PropertyName="Text" Expression="[CostoUltimo]" />
                  </ExpressionBindings>
                </Item6>
                <Item7 Ref="28" ControlType="XRTableCell" Name="tableCell7" Weight="0.65982518067556073" TextFormatString="{0:N}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="29" EventName="BeforePrint" PropertyName="Text" Expression="([SumofExistencia] * [CostoPromedio])" />
                  </ExpressionBindings>
                </Item7>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="30" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="31" ControlType="PageHeaderBand" Name="PageHeader" HeightF="49.9999962">
      <SubBands>
        <Item1 Ref="32" ControlType="SubBand" Name="SubBand1" HeightF="26.041666">
          <Controls>
            <Item1 Ref="33" ControlType="XRTable" Name="table2" TextAlignment="BottomLeft" SizeF="900,26.041666" LocationFloat="0,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,96">
              <Rows>
                <Item1 Ref="34" ControlType="XRTableRow" Name="tableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="35" ControlType="XRTableCell" Name="tableCell8" Weight="0.60410555014991929" Multiline="true" Text="Código" />
                    <Item2 Ref="36" ControlType="XRTableCell" Name="tableCell9" Weight="3.7624633252911224" Multiline="true" Text="Nombre" />
                    <Item3 Ref="37" ControlType="XRTableCell" Name="tableCell10" Weight="0.683284955201334" Multiline="true" Text="Existencia" />
                    <Item4 Ref="38" ControlType="XRTableCell" Name="tableCell11" Weight="0.66568878750578775" Multiline="true" Text="CAlto" />
                    <Item5 Ref="39" ControlType="XRTableCell" Name="tableCell12" Weight="0.59530772370745277" Multiline="true" Text="CProm" />
                    <Item6 Ref="40" ControlType="XRTableCell" Name="tableCell13" Weight="0.63049799835174514" Multiline="true" Text="CUlt" />
                    <Item7 Ref="41" ControlType="XRTableCell" Name="tableCell14" Weight="0.65982518067556073" Multiline="true" Text="CTotal" />
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="42" UseFont="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </SubBands>
      <Controls>
        <Item1 Ref="43" ControlType="XRLabel" Name="labelBodegaExistencia" Multiline="true" TextAlignment="BottomLeft" SizeF="900,49.9999962" LocationFloat="0,0" Padding="2,2,0,0,100">
          <StylePriority Ref="44" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item4>
    <Item5 Ref="45" ControlType="GroupHeaderBand" Name="GroupHeader1" HeightF="28.54166">
      <GroupFields>
        <Item1 Ref="46" FieldName="Proveedor" />
      </GroupFields>
      <Controls>
        <Item1 Ref="47" ControlType="XRLabel" Name="label2" Multiline="true" TextAlignment="BottomLeft" SizeF="900,28.54166" LocationFloat="0,0" Font="Arial, 12pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="48" EventName="BeforePrint" PropertyName="Text" Expression="Iif(IsNullOrEmpty([NombreProveedor]), '', Concat('Proveedor: ', [NombreProveedor]))" />
          </ExpressionBindings>
          <StylePriority Ref="49" UseFont="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item5>
    <Item6 Ref="50" ControlType="PageFooterBand" Name="PageFooter" HeightF="36.4583321">
      <Controls>
        <Item1 Ref="51" ControlType="XRPageInfo" Name="pageInfo1" TextFormatString="Page {0} de {1}" SizeF="94.791626,36.4583321" LocationFloat="805.2083,0" Padding="2,2,0,0,100" />
      </Controls>
    </Item6>
    <Item7 Ref="52" ControlType="GroupFooterBand" Name="GroupFooter1" HeightF="43.33334">
      <Controls>
        <Item1 Ref="53" ControlType="XRLabel" Name="label4" TextFormatString="{0:N}" Multiline="true" TextAlignment="BottomLeft" SizeF="126.527863,32.2916679" LocationFloat="676.7361,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <Summary Ref="54" Running="Group" />
          <ExpressionBindings>
            <Item1 Ref="55" EventName="BeforePrint" PropertyName="Text" Expression="sumSum([SumofExistencia] * [CostoPromedio])&#xA;" />
          </ExpressionBindings>
          <StylePriority Ref="56" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="57" ControlType="XRLabel" Name="label6" Multiline="true" Text="Total Costo X Proveedor:" TextAlignment="BottomLeft" SizeF="181.7362,32.2916679" LocationFloat="71.52777,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="58" UseFont="false" UseTextAlignment="false" />
        </Item2>
      </Controls>
    </Item7>
    <Item8 Ref="59" ControlType="GroupFooterBand" Name="GroupFooter2" Level="1" HeightF="32.2916679">
      <Controls>
        <Item1 Ref="60" ControlType="XRLabel" Name="label5" Multiline="true" Text="Total Costo:" TextAlignment="BottomLeft" SizeF="181.7362,32.2916679" LocationFloat="71.52777,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="61" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="62" ControlType="XRLabel" Name="label3" TextFormatString="{0:N}" Multiline="true" TextAlignment="BottomLeft" SizeF="126.527863,32.2916679" LocationFloat="676.7361,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <Summary Ref="63" Running="Report" />
          <ExpressionBindings>
            <Item1 Ref="64" EventName="BeforePrint" PropertyName="Text" Expression="sumSum([SumofExistencia] * [CostoPromedio])&#xA;" />
          </ExpressionBindings>
          <StylePriority Ref="65" UseFont="false" UseTextAlignment="false" />
        </Item2>
      </Controls>
    </Item8>
  </Bands>
</XtraReportsLayoutSerializer>