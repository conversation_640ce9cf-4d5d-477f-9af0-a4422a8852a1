FROM mcr.microsoft.com/dotnet/core/sdk:2.2 AS build-env
WORKDIR /app

#RUN printf "deb http://archive.debian.org/debian/ jessie main\ndeb-src http://archive.debian.org/debian/ jessie main\ndeb http://security.debian.org jessie/updates main\ndeb-src http://security.debian.org jessie/updates main" > /etc/apt/sources.list
ARG ARG_IMAGEN

# RUN ln -s /lib/x86_64-linux-gnu/libdl-2.24.so /lib/x86_64-linux-gnu/libdl.so

# RUN apt-get update
# RUN apt-get install -y libgdiplus libc6-dev
# RUN apt-get install -y libicu-dev libharfbuzz0b libfontconfig1 libfreetype6

# #RUN sed -i'.bak' 's/$/ contrib/' /etc/apt/sources.list
# #RUN apt-get update; apt-get install -y ttf-mscorefonts-installer fontconfig

# RUN apt-get update; apt-get install -y fontconfig fonts-liberation
# RUN fc-cache -f -v

COPY *.csproj ./
RUN dotnet restore -s "https://nuget.devexpress.com/zEpf9pjR9rwUn6WnVYmmq28cxwe38e8U4dY89SOOOCUqcL6k6T/api" -s "https://api.nuget.org/v3/index.json"

COPY . ./
RUN dotnet publish -c Release -o out

#--------------------------------------------------------------------------------------------------
#--------------------------------------------------------------------------------------------------

#FROM microsoft/dotnet:2.2-runtime
FROM walteromarr/reporte-base2
WORKDIR /app

# RUN ln -s /lib/x86_64-linux-gnu/libdl-2.24.so /lib/x86_64-linux-gnu/libdl.so

# RUN apt-get update
# RUN apt-get install -y libgdiplus libc6-dev
# RUN apt-get install -y libicu-dev libharfbuzz0b libfontconfig1 libfreetype6
# RUN apt-get update; apt-get install -y fontconfig fonts-liberation
# RUN fc-cache -f -v


ARG ARG_IMAGEN

LABEL io.k8s.display-name="${ARG_IMAGEN}" \
      io.k8s.description="API ${ARG_IMAGEN}" \
      io.openshift.expose-services="8080:http"


ENV ASPNETCORE_URLS=http://*:8080


COPY --from=build-env /app/out .
RUN echo "#!/bin/bash \n dotnet ${ARG_IMAGEN}.dll" > ./entrypoint.sh
RUN chmod +x ./entrypoint.sh

EXPOSE 8080
CMD ["./entrypoint.sh"]