﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="PsicotropicoUnProducto" Landscape="true" Margins="12, 18, 100, 100" PaperKind="A2" PageWidth="2339" PageHeight="1654" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" />
    <Item2 Ref="2" ControlType="BottomMarginBand" Name="BottomMargin">
      <Controls>
        <Item1 Ref="3" ControlType="XRLabel" Name="label4" Multiline="true" Text="Revisado por:____________________________________________" TextAlignment="BottomLeft" SizeF="444.038452,37.5" LocationFloat="888.076843,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="4" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="5" ControlType="XRLabel" Name="label3" Multiline="true" Text="Generado o Elaborado por:___________________________________" TextAlignment="BottomLeft" SizeF="444.038452,37.5" LocationFloat="0,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="6" UseFont="false" UseTextAlignment="false" />
        </Item2>
      </Controls>
    </Item2>
    <Item3 Ref="7" ControlType="DetailBand" Name="Detail" HeightF="37.5">
      <Controls>
        <Item1 Ref="8" ControlType="XRTable" Name="table1" SizeF="2309,37.5" LocationFloat="0,0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="9" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="10" ControlType="XRTableCell" Name="tableCell1" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="11" EventName="BeforePrint" PropertyName="Text" Expression="[Empresa]" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="12" ControlType="XRTableCell" Name="tableCell2" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="13" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
                  </ExpressionBindings>
                </Item2>
                <Item3 Ref="14" ControlType="XRTableCell" Name="tableCell3" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="15" EventName="BeforePrint" PropertyName="Text" Expression="[NombreProducto]" />
                  </ExpressionBindings>
                </Item3>
                <Item4 Ref="16" ControlType="XRTableCell" Name="tableCell4" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="17" EventName="BeforePrint" PropertyName="Text" Expression="[Transaccion]" />
                  </ExpressionBindings>
                </Item4>
                <Item5 Ref="18" ControlType="XRTableCell" Name="tableCell5" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="19" EventName="BeforePrint" PropertyName="Text" Expression="[NombreTransaccion]" />
                  </ExpressionBindings>
                </Item5>
                <Item6 Ref="20" ControlType="XRTableCell" Name="tableCell6" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="21" EventName="BeforePrint" PropertyName="Text" Expression="[Fecha]" />
                  </ExpressionBindings>
                </Item6>
                <Item7 Ref="22" ControlType="XRTableCell" Name="tableCell7" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="23" EventName="BeforePrint" PropertyName="Text" Expression="[BodegaFuente]" />
                  </ExpressionBindings>
                </Item7>
                <Item8 Ref="24" ControlType="XRTableCell" Name="tableCell8" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="25" EventName="BeforePrint" PropertyName="Text" Expression="[BodegaDestino]" />
                  </ExpressionBindings>
                </Item8>
                <Item9 Ref="26" ControlType="XRTableCell" Name="tableCell9" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="27" EventName="BeforePrint" PropertyName="Text" Expression="[Proveedor]" />
                  </ExpressionBindings>
                </Item9>
                <Item10 Ref="28" ControlType="XRTableCell" Name="tableCell10" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="29" EventName="BeforePrint" PropertyName="Text" Expression="[Factura]" />
                  </ExpressionBindings>
                </Item10>
                <Item11 Ref="30" ControlType="XRTableCell" Name="tableCell11" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="31" EventName="BeforePrint" PropertyName="Text" Expression="[Cantidad]" />
                  </ExpressionBindings>
                </Item11>
                <Item12 Ref="32" ControlType="XRTableCell" Name="tableCell12" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="33" EventName="BeforePrint" PropertyName="Text" Expression="[StatusInventario]" />
                  </ExpressionBindings>
                </Item12>
                <Item13 Ref="34" ControlType="XRTableCell" Name="tableCell13" Weight="1" TextFormatString="{0:C2}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="35" EventName="BeforePrint" PropertyName="Text" Expression="[CostoUnitario]" />
                  </ExpressionBindings>
                </Item13>
                <Item14 Ref="36" ControlType="XRTableCell" Name="tableCell14" Weight="0.84751793624725935" TextFormatString="{0:C2}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="37" EventName="BeforePrint" PropertyName="Text" Expression="[CostoUltimo]" />
                  </ExpressionBindings>
                </Item14>
                <Item15 Ref="38" ControlType="XRTableCell" Name="tableCell15" Weight="1.1524820637527407" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="39" EventName="BeforePrint" PropertyName="Text" Expression="[Departamento]" />
                  </ExpressionBindings>
                </Item15>
                <Item16 Ref="40" ControlType="XRTableCell" Name="tableCell16" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="41" EventName="BeforePrint" PropertyName="Text" Expression="[CantidadAceptada]" />
                  </ExpressionBindings>
                </Item16>
                <Item17 Ref="42" ControlType="XRTableCell" Name="tableCell17" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="43" EventName="BeforePrint" PropertyName="Text" Expression="[FechaDespacho]" />
                  </ExpressionBindings>
                </Item17>
                <Item18 Ref="44" ControlType="XRTableCell" Name="tableCell18" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="45" EventName="BeforePrint" PropertyName="Text" Expression="[FechaAceptado]" />
                  </ExpressionBindings>
                </Item18>
                <Item19 Ref="46" ControlType="XRTableCell" Name="tableCell19" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="47" EventName="BeforePrint" PropertyName="Text" Expression="[Entrada]" />
                  </ExpressionBindings>
                </Item19>
                <Item20 Ref="48" ControlType="XRTableCell" Name="tableCell20" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="49" EventName="BeforePrint" PropertyName="Text" Expression="[Salida]" />
                  </ExpressionBindings>
                </Item20>
                <Item21 Ref="50" ControlType="XRTableCell" Name="tableCell21" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="51" EventName="BeforePrint" PropertyName="Text" Expression="[Admision]" />
                  </ExpressionBindings>
                </Item21>
                <Item22 Ref="52" ControlType="XRTableCell" Name="tableCell22" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="53" EventName="BeforePrint" PropertyName="Text" Expression="[NombrePaciente]" />
                  </ExpressionBindings>
                </Item22>
                <Item23 Ref="54" ControlType="XRTableCell" Name="tableCell23" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="55" EventName="BeforePrint" PropertyName="Text" Expression="[DPI]" />
                  </ExpressionBindings>
                </Item23>
                <Item24 Ref="56" ControlType="XRTableCell" Name="tableCell24" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="57" EventName="BeforePrint" PropertyName="Text" Expression="[NombreMedico]" />
                  </ExpressionBindings>
                </Item24>
                <Item25 Ref="58" ControlType="XRTableCell" Name="tableCell25" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="59" EventName="BeforePrint" PropertyName="Text" Expression="[Motivo]" />
                  </ExpressionBindings>
                </Item25>
                <Item26 Ref="60" ControlType="XRTableCell" Name="tableCell26" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="61" EventName="BeforePrint" PropertyName="Text" Expression="[Motivo2]" />
                  </ExpressionBindings>
                </Item26>
              </Cells>
            </Item1>
          </Rows>
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="62" ControlType="PageHeaderBand" Name="PageHeader" HeightF="187.5">
      <SubBands>
        <Item1 Ref="63" ControlType="SubBand" Name="SubBand1" HeightF="37.5">
          <Controls>
            <Item1 Ref="64" ControlType="XRTable" Name="table2" TextAlignment="BottomLeft" SizeF="2309,37.5" LocationFloat="0,0" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="SkyBlue" Padding="2,2,0,0,96">
              <Rows>
                <Item1 Ref="65" ControlType="XRTableRow" Name="tableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="66" ControlType="XRTableCell" Name="tableCell27" Weight="1" Multiline="true" Text="Empresa" />
                    <Item2 Ref="67" ControlType="XRTableCell" Name="tableCell28" Weight="1" Multiline="true" Text="Producto" />
                    <Item3 Ref="68" ControlType="XRTableCell" Name="tableCell29" Weight="1" Multiline="true" Text="Nombre Producto" />
                    <Item4 Ref="69" ControlType="XRTableCell" Name="tableCell30" Weight="1" Multiline="true" Text="Transaccion" />
                    <Item5 Ref="70" ControlType="XRTableCell" Name="tableCell31" Weight="1" Multiline="true" Text="Nombre Transaccion" />
                    <Item6 Ref="71" ControlType="XRTableCell" Name="tableCell32" Weight="1" Multiline="true" Text="Fecha" />
                    <Item7 Ref="72" ControlType="XRTableCell" Name="tableCell33" Weight="1" Multiline="true" Text="Bodega Fuente" />
                    <Item8 Ref="73" ControlType="XRTableCell" Name="tableCell34" Weight="1" Multiline="true" Text="Bodega Destino" />
                    <Item9 Ref="74" ControlType="XRTableCell" Name="tableCell35" Weight="1" Multiline="true" Text="Proveedor" />
                    <Item10 Ref="75" ControlType="XRTableCell" Name="tableCell36" Weight="1" Multiline="true" Text="Factura" />
                    <Item11 Ref="76" ControlType="XRTableCell" Name="tableCell37" Weight="1" Multiline="true" Text="Cantidad" />
                    <Item12 Ref="77" ControlType="XRTableCell" Name="tableCell38" Weight="1" Multiline="true" Text="Status Inventario" />
                    <Item13 Ref="78" ControlType="XRTableCell" Name="tableCell39" Weight="1" TextFormatString="{0:C2}" Multiline="true" Text="Costo Unitario" />
                    <Item14 Ref="79" ControlType="XRTableCell" Name="tableCell40" Weight="0.84751793624725935" TextFormatString="{0:C2}" Multiline="true" Text="Costo Ultimo" />
                    <Item15 Ref="80" ControlType="XRTableCell" Name="tableCell41" Weight="1.1524820637527407" Multiline="true" Text="Departamento" />
                    <Item16 Ref="81" ControlType="XRTableCell" Name="tableCell42" Weight="1" Multiline="true" Text="Cantidad Aceptada" />
                    <Item17 Ref="82" ControlType="XRTableCell" Name="tableCell43" Weight="1" Multiline="true" Text="Fecha Despacho" />
                    <Item18 Ref="83" ControlType="XRTableCell" Name="tableCell44" Weight="1" Multiline="true" Text="Fecha Aceptado" />
                    <Item19 Ref="84" ControlType="XRTableCell" Name="tableCell45" Weight="1" Multiline="true" Text="Entrada" />
                    <Item20 Ref="85" ControlType="XRTableCell" Name="tableCell46" Weight="1" Multiline="true" Text="Salida" />
                    <Item21 Ref="86" ControlType="XRTableCell" Name="tableCell47" Weight="1" Multiline="true" Text="Admisión" />
                    <Item22 Ref="87" ControlType="XRTableCell" Name="tableCell48" Weight="1" Multiline="true" Text="Nombre Paciente" />
                    <Item23 Ref="88" ControlType="XRTableCell" Name="tableCell49" Weight="1" Multiline="true" Text="DPI" />
                    <Item24 Ref="89" ControlType="XRTableCell" Name="tableCell50" Weight="1" Multiline="true" Text="Nombre Medico" />
                    <Item25 Ref="90" ControlType="XRTableCell" Name="tableCell51" Weight="1" Multiline="true" Text="Motivo" />
                    <Item26 Ref="91" ControlType="XRTableCell" Name="tableCell52" Weight="1" Multiline="true" Text="Motivo 2" />
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="92" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </SubBands>
      <Controls>
        <Item1 Ref="93" ControlType="XRLabel" Name="labelProducto" Multiline="true" TextAlignment="BottomLeft" SizeF="710.4615,37.5" LocationFloat="0,150" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="94" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="95" ControlType="XRLabel" Name="labelSucursalFamilia" Multiline="true" Text="Sucursal: Familia:" TextAlignment="BottomLeft" SizeF="710.4615,37.5" LocationFloat="0,112.5" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="96" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="97" ControlType="XRLabel" Name="labelRangoFechas" Multiline="true" Text="DEl: Al:" TextAlignment="BottomLeft" SizeF="444.038452,37.5" LocationFloat="0,75" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="98" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="99" ControlType="XRLabel" Name="label2" Multiline="true" Text="REPORTE DE CONSUMO POR PACIENTE MEDICAMENTOS PSICOTROPICOS Y ESTUPEFACIENTES" TextAlignment="BottomLeft" SizeF="710.4615,37.5" LocationFloat="0,37.5" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="100" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="101" ControlType="XRLabel" Name="label1" Multiline="true" Text="Servicios Médicos y Hospitalarios Centroamericanos, S.A." TextAlignment="BottomLeft" SizeF="444.038452,37.5" LocationFloat="0,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="102" UseFont="false" UseTextAlignment="false" />
        </Item5>
      </Controls>
    </Item4>
  </Bands>
</XtraReportsLayoutSerializer>