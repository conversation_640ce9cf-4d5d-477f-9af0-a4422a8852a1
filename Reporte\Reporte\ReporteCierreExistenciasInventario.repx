﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="ReporteCierreExistenciasInventario" Margins="95, 88, 74, 0" PaperKind="Custom" PageWidth="2301" PageHeight="1276" Version="20.1" EventsInfo="|tableCell34,BeforePrint,tableCell34_BeforePrint" TextAlignment="MiddleCenter" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" HeightF="74">
      <Controls>
        <Item1 Ref="2" ControlType="XRLabel" Name="label2" Multiline="true" Text="label1" TextAlignment="TopCenter" SizeF="2117.48877,22.321434" LocationFloat="0.5111694,34.42638" Font="Arial, 10pt, style=Bold" ForeColor="255,0,96,151" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="3" EventName="BeforePrint" PropertyName="Text" Expression="[DescripcionPeriodo]" />
          </ExpressionBindings>
          <StylePriority Ref="4" UseFont="false" UseForeColor="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="5" ControlType="XRLabel" Name="uiTitulo" Multiline="true" CanShrink="true" Text="Reporte de Inventario Valorizado de fin de Mes" TextAlignment="MiddleCenter" SizeF="2118,24.4264" LocationFloat="0,9.99998951" Font="Arial, 11.25pt, style=Bold, charSet=0" ForeColor="255,0,96,151">
          <StylePriority Ref="6" UseFont="false" UseForeColor="false" UseTextAlignment="false" />
        </Item2>
      </Controls>
    </Item1>
    <Item2 Ref="7" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" />
    <Item3 Ref="8" ControlType="DetailBand" Name="Detail" HeightF="18.21">
      <Controls>
        <Item1 Ref="9" ControlType="XRTable" Name="table1" TextAlignment="MiddleLeft" SizeF="2118,18.21" LocationFloat="0,0" Font="Arial, 11pt" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="10" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="11" ControlType="XRTableCell" Name="Periodo" Weight="0.20359860842027513" Multiline="true" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="12" EventName="BeforePrint" PropertyName="Text" Expression="[Periodo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="13" UseFont="false" />
                </Item1>
                <Item2 Ref="14" ControlType="XRTableCell" Name="Producto" Weight="0.17528085430063048" Multiline="true" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="15" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="16" UseFont="false" />
                </Item2>
                <Item3 Ref="17" ControlType="XRTableCell" Name="Hospital" Weight="1.5408071424216718" Multiline="true" TextAlignment="MiddleLeft" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="18" EventName="BeforePrint" PropertyName="Text" Expression="[NombreProducto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="19" UseFont="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="20" ControlType="XRTableCell" Name="tableCell20" Weight="0.32212004001117489" Multiline="true" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="21" EventName="BeforePrint" PropertyName="Text" Expression="[UnidadMedida]" />
                  </ExpressionBindings>
                  <StylePriority Ref="22" UseFont="false" />
                </Item4>
                <Item5 Ref="23" ControlType="XRTableCell" Name="tableCell21" Weight="0.15308905012790897" Multiline="true" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="24" EventName="BeforePrint" PropertyName="Text" Expression="[Bodega]" />
                  </ExpressionBindings>
                  <StylePriority Ref="25" UseFont="false" />
                </Item5>
                <Item6 Ref="26" ControlType="XRTableCell" Name="tableCell22" Weight="0.63454285685649026" Multiline="true" Text="tableCell22" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="27" EventName="BeforePrint" PropertyName="Text" Expression="[NombreBodega]" />
                  </ExpressionBindings>
                  <StylePriority Ref="28" UseFont="false" />
                </Item6>
                <Item7 Ref="29" ControlType="XRTableCell" Name="tableCell23" Weight="0.2122853652724408" Multiline="true" Text="tableCell23" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="30" EventName="BeforePrint" PropertyName="Text" Expression="[Existencia]" />
                  </ExpressionBindings>
                  <StylePriority Ref="31" UseFont="false" />
                </Item7>
                <Item8 Ref="32" ControlType="XRTableCell" Name="tableCell24" Weight="0.262161969253621" Multiline="true" Text="tableCell24" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="33" EventName="BeforePrint" PropertyName="Text" Expression="[FechaRegistro]" />
                  </ExpressionBindings>
                  <StylePriority Ref="34" UseFont="false" />
                </Item8>
                <Item9 Ref="35" ControlType="XRTableCell" Name="tableCell7" Weight="0.16232482555476513" Multiline="true" Text="tableCell7" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="36" EventName="BeforePrint" PropertyName="Text" Expression="[Activo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="37" UseFont="false" />
                </Item9>
                <Item10 Ref="38" ControlType="XRTableCell" Name="tableCell12" Weight="0.32679672180423225" Multiline="true" Text="tableCell12" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="39" EventName="BeforePrint" PropertyName="Text" Expression="[CostoUltimo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="40" UseFont="false" />
                </Item10>
                <Item11 Ref="41" ControlType="XRTableCell" Name="tableCell14" Weight="0.314954652612216" Multiline="true" Text="tableCell14" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="42" EventName="BeforePrint" PropertyName="Text" Expression="[CostoPromedio]" />
                  </ExpressionBindings>
                  <StylePriority Ref="43" UseFont="false" />
                </Item11>
                <Item12 Ref="44" ControlType="XRTableCell" Name="tableCell16" Weight="0.31625645003516079" Multiline="true" Text="tableCell16" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="45" EventName="BeforePrint" PropertyName="Text" Expression="[CostoAlto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="46" UseFont="false" />
                </Item12>
                <Item13 Ref="47" ControlType="XRTableCell" Name="tableCell18" Weight="0.27375822081191403" Multiline="true" Text="tableCell18" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="48" EventName="BeforePrint" PropertyName="Text" Expression="[CostoTotal]" />
                  </ExpressionBindings>
                  <StylePriority Ref="49" UseFont="false" />
                </Item13>
                <Item14 Ref="50" ControlType="XRTableCell" Name="tableCell25" Weight="0.41049216234719621" Multiline="true" Text="tableCell25" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="51" EventName="BeforePrint" PropertyName="Text" Expression="[EmpresaReal]" />
                  </ExpressionBindings>
                  <StylePriority Ref="52" UseFont="false" />
                </Item14>
                <Item15 Ref="53" ControlType="XRTableCell" Name="tableCell27" Weight="0.23254150612824454" Multiline="true" Text="tableCell27" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="54" EventName="BeforePrint" PropertyName="Text" Expression="[Debe]" />
                  </ExpressionBindings>
                  <StylePriority Ref="55" UseFont="false" />
                </Item15>
                <Item16 Ref="56" ControlType="XRTableCell" Name="tableCell29" Weight="0.2029762835182185" Multiline="true" Text="tableCell29" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="57" EventName="BeforePrint" PropertyName="Text" Expression="[Haber]" />
                  </ExpressionBindings>
                  <StylePriority Ref="58" UseFont="false" />
                </Item16>
                <Item17 Ref="59" ControlType="XRTableCell" Name="tableCell31" Weight="0.60727444475492187" Multiline="true" Text="tableCell31" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="60" EventName="BeforePrint" PropertyName="Text" Expression="[NombreCategoria]" />
                  </ExpressionBindings>
                  <StylePriority Ref="61" UseFont="false" />
                </Item17>
                <Item18 Ref="62" ControlType="XRTableCell" Name="tableCell35" Weight="0.41132508512090887" Multiline="true" Text="tableCell35" Font="Arial, 8pt">
                  <ExpressionBindings>
                    <Item1 Ref="63" EventName="BeforePrint" PropertyName="Text" Expression="[Cargo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="64" UseFont="false" />
                </Item18>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="65" UseFont="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="66" ControlType="ReportFooterBand" Name="ReportFooter">
      <Controls>
        <Item1 Ref="67" ControlType="XRLabel" Name="SumaTotal" TextFormatString="{0:Q #,#.00}" Multiline="true" TextAlignment="TopLeft" SizeF="214.302612,28.1249962" LocationFloat="1448.27673,61.8750038" Font="Arial, 10pt, style=Bold" ForeColor="255,64,64,64" Padding="2,2,0,0,100" BorderWidth="0">
          <StylePriority Ref="68" UseFont="false" UseForeColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="69" ControlType="XRLabel" Name="label3" Multiline="true" Text=" SUMA COSTO TOTAL :" TextAlignment="MiddleCenter" SizeF="197.69165,28.1250038" LocationFloat="1250.58508,61.8750038" Font="Arial, 10pt, style=Bold" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="70" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item2>
      </Controls>
    </Item4>
    <Item5 Ref="71" ControlType="PageHeaderBand" Name="PageHeader" HeightF="19.33407">
      <Controls>
        <Item1 Ref="72" ControlType="XRTable" Name="table2" KeepTogether="true" SizeF="2117.48877,19.33407" LocationFloat="0.5112272,0" Padding="0,0,2,2,100" BorderColor="255,0,96,151">
          <Rows>
            <Item1 Ref="73" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="74" ControlType="XRTableCell" Name="tableCell8" Weight="0.3757741973053263" Multiline="true" Text="Periodo" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" Borders="None">
                  <StylePriority Ref="75" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorders="false" />
                </Item1>
                <Item2 Ref="76" ControlType="XRTableCell" Name="tableCell6" Weight="0.32612390035122185" Multiline="true" Text="Producto" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="77" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item2>
                <Item3 Ref="78" ControlType="XRTableCell" Name="tableCell1" Weight="2.8667928019342721" Multiline="true" Text="Nombre Producto" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="79" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item3>
                <Item4 Ref="80" ControlType="XRTableCell" Name="tableCell2" Weight="0.59932968634132178" Multiline="true" Text="Unidad de Medida" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="81" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item4>
                <Item5 Ref="82" ControlType="XRTableCell" Name="tableCell3" Weight="0.28483458024624619" Multiline="true" Text="Bodega" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="83" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item5>
                <Item6 Ref="84" ControlType="XRTableCell" Name="tableCell4" Weight="1.180616934712557" Multiline="true" Text="Nombre Bodega" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="85" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item6>
                <Item7 Ref="86" ControlType="XRTableCell" Name="tableCell5" Weight="0.39497391485938405" Multiline="true" Text="Existencia" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="87" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item7>
                <Item8 Ref="88" ControlType="XRTableCell" Name="tableCell9" Weight="0.48777293087111218" Multiline="true" Text="Fecha Registro" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="89" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item8>
                <Item9 Ref="90" ControlType="XRTableCell" Name="tableCell10" Weight="0.30201803501940105" Multiline="true" Text="Activo" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="91" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item9>
                <Item10 Ref="92" ControlType="XRTableCell" Name="tableCell11" Weight="0.60803111812357624" Multiline="true" Text="Costo Ultimo" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="93" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item10>
                <Item11 Ref="94" ControlType="XRTableCell" Name="tableCell13" Weight="0.58599801531005968" Multiline="true" Text="Costo Promedio" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="95" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item11>
                <Item12 Ref="96" ControlType="XRTableCell" Name="tableCell15" Weight="0.58842011198902888" Multiline="true" Text="Costo Alto" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="97" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item12>
                <Item13 Ref="98" ControlType="XRTableCell" Name="tableCell17" Weight="0.50934880792170167" Multiline="true" Text="Total Costo" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="99" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item13>
                <Item14 Ref="100" ControlType="XRTableCell" Name="tableCell19" Weight="0.76375230943424732" Multiline="true" Text="Empresa Real" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="101" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item14>
                <Item15 Ref="102" ControlType="XRTableCell" Name="tableCell26" Weight="0.43266265525812209" Multiline="true" Text="Debe" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="103" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item15>
                <Item16 Ref="104" ControlType="XRTableCell" Name="tableCell28" Weight="0.37765418371191295" Multiline="true" Text="Haber" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="105" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item16>
                <Item17 Ref="106" ControlType="XRTableCell" Name="tableCell30" Weight="1.1298812804550695" Multiline="true" Text="NombreCategoria" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="107" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item17>
                <Item18 Ref="108" ControlType="XRTableCell" Name="tableCell34" Weight="0.76530345787668919" Multiline="true" Text="Cargo" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="109" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item18>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="110" UsePadding="false" UseBorderColor="false" />
        </Item1>
      </Controls>
    </Item5>
  </Bands>
</XtraReportsLayoutSerializer>