﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="*********" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="MovimientosRequerimientos" Margins="50, 50, 14, 2" PageWidth="850" PageHeight="1100" Version="20.1" EventsInfo="|cellNombreProducto,BeforePrint,CalculaAncho;cellPresentacion,BeforePrint,CalculaAncho;encNombreProducto,BeforePrint,CalculaAncho;encPresentacion,BeforePrint,CalculaAncho">
  <Parameters>
    <Item1 Ref="2" Description="VerCostoPromedio" ValueInfo="False" Name="VerCostoPromedio" Type="#Ref-1" />
  </Parameters>
  <Bands>
    <Item1 Ref="3" ControlType="TopMarginBand" Name="topMarginBand1" HeightF="13.8750076" />
    <Item2 Ref="4" ControlType="DetailBand" Name="detailBand1" HeightF="23.958334">
      <Controls>
        <Item1 Ref="5" ControlType="XRTable" Name="table3" ProcessHiddenCellMode="StretchPreviousCell" SizeF="620.0037,23.958334" LocationFloat="0,0" Padding="2,2,0,0,96" BorderColor="Black">
          <Rows>
            <Item1 Ref="6" ControlType="XRTableRow" Name="tableRow5" Weight="1">
              <Cells>
                <Item1 Ref="7" ControlType="XRTableCell" Name="tableCell23" Weight="0.6704124968015942" Multiline="true" TextAlignment="MiddleLeft" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="8" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="9" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="10" ControlType="XRTableCell" Name="tableCell24" Weight="3.1968855635783484" Multiline="true" TextAlignment="MiddleLeft" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="11" EventName="BeforePrint" PropertyName="Text" Expression="[NombreProducto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="12" UseBorders="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="13" ControlType="XRTableCell" Name="tableCell27" Weight="0.77340524194224558" Multiline="true" TextAlignment="MiddleLeft" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="14" EventName="BeforePrint" PropertyName="Text" Expression="[Descripcion]" />
                  </ExpressionBindings>
                  <StylePriority Ref="15" UseBorders="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="16" ControlType="XRTableCell" Name="tableCell28" Weight="0.61875087847534338" Multiline="true" TextAlignment="MiddleLeft" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="17" EventName="BeforePrint" PropertyName="Text" Expression="[CantidadPedida]" />
                  </ExpressionBindings>
                  <StylePriority Ref="18" UseBorders="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="19" ControlType="XRTableCell" Name="tableCell31" Weight="0.56718486392173284" Multiline="true" TextAlignment="MiddleRight" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="20" EventName="BeforePrint" PropertyName="Text" Expression="[Cantidad]" />
                  </ExpressionBindings>
                  <StylePriority Ref="21" UseBorders="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="22" ControlType="XRTableCell" Name="tableCell32" Weight="0.56711944230301481" Multiline="true" TextAlignment="MiddleRight" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="23" EventName="BeforePrint" PropertyName="Text" Expression="[Cantidad]" />
                  </ExpressionBindings>
                  <StylePriority Ref="24" UseBorders="false" UseTextAlignment="false" />
                </Item6>
              </Cells>
            </Item1>
          </Rows>
          <ExpressionBindings>
            <Item1 Ref="25" EventName="BeforePrint" PropertyName="Visible" Expression=" Not ?VerCostoPromedio" />
          </ExpressionBindings>
          <StylePriority Ref="26" UseBorderColor="false" />
        </Item1>
        <Item2 Ref="27" ControlType="XRTable" Name="tableDet" ProcessHiddenCellMode="StretchPreviousCell" SizeF="750,23.958334" LocationFloat="0,0" Padding="2,2,0,0,96" BorderColor="Black">
          <Rows>
            <Item1 Ref="28" ControlType="XRTableRow" Name="tableRow3" Weight="1">
              <Cells>
                <Item1 Ref="29" ControlType="XRTableCell" Name="tableCell11" Weight="0.6704124968015942" Multiline="true" TextAlignment="MiddleLeft" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="30" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="31" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="32" ControlType="XRTableCell" Name="cellNombreProducto" Weight="3.1968855635783484" Multiline="true" TextAlignment="MiddleLeft" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="33" EventName="BeforePrint" PropertyName="Text" Expression="[NombreProducto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="34" UseBorders="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="35" ControlType="XRTableCell" Name="cellPresentacion" Weight="0.77340524194224558" Multiline="true" TextAlignment="MiddleLeft" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="36" EventName="BeforePrint" PropertyName="Text" Expression="[Descripcion]" />
                  </ExpressionBindings>
                  <StylePriority Ref="37" UseBorders="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="38" ControlType="XRTableCell" Name="cellCostoPromedio" Weight="0.61875087847534338" Multiline="true" TextAlignment="MiddleLeft" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="39" EventName="BeforePrint" PropertyName="Text" Expression="[CantidadPedida]" />
                  </ExpressionBindings>
                  <StylePriority Ref="40" UseBorders="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="41" ControlType="XRTableCell" Name="tableCell4" Weight="0.61870870004796141" Multiline="true" TextAlignment="MiddleRight" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="42" EventName="BeforePrint" PropertyName="Text" Expression="[CostoUnitario]" />
                  </ExpressionBindings>
                  <StylePriority Ref="43" UseBorders="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="44" ControlType="XRTableCell" Name="tableCellPedidosDet" Weight="0.72187162001267213" Multiline="true" TextAlignment="MiddleRight" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="45" EventName="BeforePrint" PropertyName="Text" Expression="[Costo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="46" UseBorders="false" UseTextAlignment="false" />
                </Item6>
                <Item7 Ref="47" ControlType="XRTableCell" Name="tableCell14" Weight="0.56718486392173284" Multiline="true" TextAlignment="MiddleRight" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="48" EventName="BeforePrint" PropertyName="Text" Expression="[Cantidad]" />
                  </ExpressionBindings>
                  <StylePriority Ref="49" UseBorders="false" UseTextAlignment="false" />
                </Item7>
                <Item8 Ref="50" ControlType="XRTableCell" Name="tableCell15" Weight="0.56711944230301481" Multiline="true" TextAlignment="MiddleRight" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="51" EventName="BeforePrint" PropertyName="Text" Expression="[Cantidad]" />
                  </ExpressionBindings>
                  <StylePriority Ref="52" UseBorders="false" UseTextAlignment="false" />
                </Item8>
              </Cells>
            </Item1>
          </Rows>
          <ExpressionBindings>
            <Item1 Ref="53" EventName="BeforePrint" PropertyName="Visible" Expression="?VerCostoPromedio" />
          </ExpressionBindings>
          <StylePriority Ref="54" UseBorderColor="false" />
        </Item2>
      </Controls>
    </Item2>
    <Item3 Ref="55" ControlType="BottomMarginBand" Name="bottomMarginBand1" HeightF="1.9582113" />
    <Item4 Ref="56" ControlType="PageHeaderBand" Name="PageHeader" HeightF="6.706629">
      <SubBands>
        <Item1 Ref="57" ControlType="SubBand" Name="SubBand1" HeightF="35">
          <Controls>
            <Item1 Ref="58" ControlType="XRTable" Name="table2" ProcessHiddenCellMode="StretchPreviousCell" AnchorHorizontal="Both" SizeF="619.999939,35" LocationFloat="0,0" Padding="2,2,0,0,96">
              <Rows>
                <Item1 Ref="59" ControlType="XRTableRow" Name="tableRow4" Weight="1">
                  <Cells>
                    <Item1 Ref="60" ControlType="XRTableCell" Name="tableCell19" Weight="0.59073202636226707" Multiline="true" Text="Producto" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="61" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="62" ControlType="XRTableCell" Name="tableCell20" Weight="2.8169255874857684" Multiline="true" Text="Descripción" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="63" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item2>
                    <Item3 Ref="64" ControlType="XRTableCell" Name="tableCell21" Weight="0.68148691092137" Multiline="true" Text="Presentación" TextAlignment="BottomLeft" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="65" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item3>
                    <Item4 Ref="66" ControlType="XRTableCell" Name="tableCell22" Weight="0.545207213176242" Multiline="true" Text="Cantidad&#xD;&#xA;Pedida" TextAlignment="BottomLeft" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="67" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item4>
                    <Item5 Ref="68" ControlType="XRTableCell" Name="tableCell25" Weight="0.49977327409953037" Multiline="true" Text="Cantidad&#xD;&#xA;Despachada" TextAlignment="BottomLeft" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Padding="1,1,0,0,100" Borders="None">
                      <StylePriority Ref="69" UseFont="false" UseBackColor="false" UsePadding="false" UseBorders="false" UseTextAlignment="false" />
                    </Item5>
                    <Item6 Ref="70" ControlType="XRTableCell" Name="tableCell26" Weight="0.49968245689996688" Multiline="true" Text="Cantidad&#xD;&#xA;Recibida" TextAlignment="BottomLeft" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="71" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item6>
                  </Cells>
                </Item1>
              </Rows>
              <ExpressionBindings>
                <Item1 Ref="72" EventName="BeforePrint" PropertyName="Visible" Expression="Not ?VerCostoPromedio" />
              </ExpressionBindings>
            </Item1>
            <Item2 Ref="73" ControlType="XRTable" Name="tableTitulos" ProcessHiddenCellMode="StretchPreviousCell" AnchorHorizontal="Both" SizeF="749.996155,35" LocationFloat="0,0" Padding="2,2,0,0,96">
              <Rows>
                <Item1 Ref="74" ControlType="XRTableRow" Name="tableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="75" ControlType="XRTableCell" Name="tableCell6" Weight="0.59073202636226707" Multiline="true" Text="Producto" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="76" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="77" ControlType="XRTableCell" Name="encNombreProducto" Weight="2.8169255874857684" Multiline="true" Text="Descripción" TextAlignment="BottomLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="78" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item2>
                    <Item3 Ref="79" ControlType="XRTableCell" Name="encPresentacion" Weight="0.68148691092137" Multiline="true" Text="Presentación" TextAlignment="BottomLeft" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="80" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item3>
                    <Item4 Ref="81" ControlType="XRTableCell" Name="encCostoPromedio" Weight="0.545207213176242" Multiline="true" Text="Cantidad&#xD;&#xA;Pedida" TextAlignment="BottomLeft" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="82" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item4>
                    <Item5 Ref="83" ControlType="XRTableCell" Name="tableCell2" Weight="0.54517266254988261" Multiline="true" Text="Costo Promedio" TextAlignment="BottomLeft" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="84" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item5>
                    <Item6 Ref="85" ControlType="XRTableCell" Name="tableCellPedidosEnc" Weight="0.63607508725305617" Multiline="true" Text="Costo Promedio Total" TextAlignment="BottomLeft" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="86" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item6>
                    <Item7 Ref="87" ControlType="XRTableCell" Name="tableCell9" Weight="0.49977327409953037" Multiline="true" Text="Cantidad&#xD;&#xA;Despachada" TextAlignment="BottomLeft" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Padding="1,1,0,0,100" Borders="None">
                      <StylePriority Ref="88" UseFont="false" UseBackColor="false" UsePadding="false" UseBorders="false" UseTextAlignment="false" />
                    </Item7>
                    <Item8 Ref="89" ControlType="XRTableCell" Name="tableCell10" Weight="0.49968245689996688" Multiline="true" Text="Cantidad&#xD;&#xA;Recibida" TextAlignment="BottomLeft" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="90" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item8>
                  </Cells>
                </Item1>
              </Rows>
              <ExpressionBindings>
                <Item1 Ref="91" EventName="BeforePrint" PropertyName="Visible" Expression="?VerCostoPromedio" />
              </ExpressionBindings>
            </Item2>
          </Controls>
        </Item1>
      </SubBands>
    </Item4>
    <Item5 Ref="92" ControlType="GroupFooterBand" Name="GroupFooter1" HeightF="50.083416">
      <Controls>
        <Item1 Ref="93" ControlType="XRLabel" Name="label14" TextFormatString="{0:c2}" Multiline="true" Text="label14" TextAlignment="MiddleRight" SizeF="99.1665649,23" LocationFloat="540.839661,0" Font="Times New Roman, 9.75pt, style=Bold" Padding="2,2,0,0,100">
          <Summary Ref="94" Running="Report" />
          <ExpressionBindings>
            <Item1 Ref="95" EventName="BeforePrint" PropertyName="Text" Expression="sumSum([Costo])" />
          </ExpressionBindings>
          <StylePriority Ref="96" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="97" ControlType="XRLabel" Name="label12" Multiline="true" Text="Total del Movimiento" TextAlignment="MiddleRight" SizeF="165.827209,23" LocationFloat="375.012451,0" Font="Times New Roman, 9.75pt, style=Bold" Padding="2,2,0,0,100">
          <StylePriority Ref="98" UseFont="false" UseTextAlignment="false" />
        </Item2>
      </Controls>
    </Item5>
    <Item6 Ref="99" ControlType="ReportFooterBand" Name="ReportFooter" PrintAtBottom="true">
      <Controls>
        <Item1 Ref="100" ControlType="XRLine" Name="line1" SizeF="158.9707,4.171688" LocationFloat="216.041626,51.66664" />
        <Item2 Ref="101" ControlType="XRLine" Name="line2" SizeF="179.993652,4.171688" LocationFloat="570.0064,51.66664" />
        <Item3 Ref="102" ControlType="XRLabel" Name="label2" Multiline="true" Text="REVISADO INGRESO:" TextAlignment="BottomRight" SizeF="194.993683,28.130022" LocationFloat="375.012451,27.7083073" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="Transparent" Padding="0,0,0,0,100">
          <StylePriority Ref="103" UseFont="false" UseBackColor="false" UsePadding="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="104" ControlType="XRLabel" Name="labelRevisadoOperado" Multiline="true" TextAlignment="BottomRight" SizeF="215,28.13" LocationFloat="1.04165077,27.7083073" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="Transparent" Padding="0,0,0,0,100">
          <StylePriority Ref="105" UseFont="false" UseBackColor="false" UsePadding="false" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item6>
    <Item7 Ref="106" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="329.202728">
      <Controls>
        <Item1 Ref="107" ControlType="XRLabel" Name="label5" Multiline="true" Text="label5" TextAlignment="BottomLeft" SizeF="245.0125,28.2083435" LocationFloat="129.999985,298.6867" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="108" EventName="BeforePrint" PropertyName="Text" Expression="Iif(Lower([TipoSolicitud])=='requerimiento',[CorporativoDespacho]+' '+[NombreDespacho],'')&#xA;" />
          </ExpressionBindings>
          <StylePriority Ref="109" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="110" ControlType="XRLabel" Name="label3" Multiline="true" Text="label3" TextAlignment="BottomLeft" SizeF="129.999985,28.2083435" LocationFloat="0,298.6867" Font="Arial, 12pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="111" EventName="BeforePrint" PropertyName="Text" Expression="Iif(Lower([TipoSolicitud])=='requerimiento','Usuario Recepción','')" />
          </ExpressionBindings>
          <StylePriority Ref="112" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="113" ControlType="XRLabel" Name="label10" Multiline="true" TextAlignment="BottomLeft" SizeF="245.012482,28.125" LocationFloat="130.000015,270.561737" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="114" EventName="BeforePrint" PropertyName="Text" Expression="[Departamento]+ ' '+[DepartamentoNombre]&#xA;" />
          </ExpressionBindings>
          <StylePriority Ref="115" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="116" ControlType="XRLabel" Name="label9" Multiline="true" Text="Departamento" TextAlignment="BottomLeft" SizeF="129.999985,28.125" LocationFloat="0,270.561737" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="117" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="118" ControlType="XRLabel" Name="labelObservaciones" Multiline="true" TextAlignment="TopLeft" SizeF="374.987518,56.7499847" LocationFloat="375.012482,270.561737" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="119" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="120" ControlType="XRLabel" Name="label19" Multiline="true" Text="Observaciones" TextAlignment="BottomLeft" SizeF="134.997559,28.125" LocationFloat="375.0124,242.436752" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="121" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="122" ControlType="XRLabel" Name="labelEstadoMovimiento" Multiline="true" TextAlignment="BottomLeft" SizeF="239.989929,28.125" LocationFloat="510.01004,214.311722" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="123" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="124" ControlType="XRLabel" Name="label17" Multiline="true" Text="Estado" TextAlignment="BottomLeft" SizeF="134.997467,28.125" LocationFloat="375.012482,214.311722" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="125" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="126" ControlType="XRLabel" Name="labelFechaAceptado" Multiline="true" TextAlignment="BottomLeft" SizeF="239.990051,28.125" LocationFloat="510.009949,186.186737" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="127" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="128" ControlType="XRLabel" Name="label15" Multiline="true" Text="Acep." TextAlignment="BottomLeft" SizeF="134.997437,28.125" LocationFloat="375.012482,186.186737" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="129" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="130" ControlType="XRLabel" Name="labelFechaDespacho" Multiline="true" TextAlignment="BottomLeft" SizeF="239.990051,28.125" LocationFloat="510.009949,158.0617" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="131" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="132" ControlType="XRLabel" Name="label13" Multiline="true" Text="Desp." TextAlignment="BottomLeft" SizeF="134.9974,28.125" LocationFloat="375.012482,158.0617" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="133" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="134" ControlType="XRLabel" Name="labelFechaRegistro" Multiline="true" TextAlignment="BottomLeft" SizeF="239.990051,28.125" LocationFloat="510.009949,129.936722" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="135" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="136" ControlType="XRLabel" Name="label11" Multiline="true" Text="Reg." TextAlignment="BottomLeft" SizeF="134.9974,28.125" LocationFloat="375.012482,129.936722" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="137" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="138" ControlType="XRLabel" Name="labelABodega" Multiline="true" TextAlignment="BottomLeft" SizeF="245.0125,28.125" LocationFloat="129.999985,242.436752" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="139" EventName="BeforePrint" PropertyName="Text" Expression="[BodegaDestino]+ ' '+[DestinoNombre]&#xA;" />
          </ExpressionBindings>
          <StylePriority Ref="140" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item15>
        <Item16 Ref="141" ControlType="XRLabel" Name="labelDeBodega" Multiline="true" TextAlignment="BottomLeft" SizeF="245.01236,28.125" LocationFloat="130.000015,214.311722" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="142" EventName="BeforePrint" PropertyName="Text" Expression="[BodegaFuente]+ ' '+[FuenteNombre]" />
          </ExpressionBindings>
          <StylePriority Ref="143" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item16>
        <Item17 Ref="144" ControlType="XRLabel" Name="label8" Multiline="true" Text="A Bodega" TextAlignment="BottomLeft" SizeF="129.999985,28.125" LocationFloat="0,242.436752" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="145" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item17>
        <Item18 Ref="146" ControlType="XRLabel" Name="label6" Multiline="true" Text="De Bodega" TextAlignment="BottomLeft" SizeF="130,28.125" LocationFloat="0,214.311722" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="147" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item18>
        <Item19 Ref="148" ControlType="XRLabel" Name="labelTipoMovimiento" Multiline="true" TextAlignment="BottomLeft" SizeF="245.01236,28.125" LocationFloat="130.000015,186.186737" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="149" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item19>
        <Item20 Ref="150" ControlType="XRLabel" Name="label4" Multiline="true" Text="Tipo" TextAlignment="BottomLeft" SizeF="130,28.125" LocationFloat="0,186.186737" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="151" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item20>
        <Item21 Ref="152" ControlType="XRLabel" Name="labelFechaMovimiento" Multiline="true" TextAlignment="BottomLeft" SizeF="245.01239,28.125" LocationFloat="130.000015,158.0617" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="153" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item21>
        <Item22 Ref="154" ControlType="XRLabel" Name="label1" Multiline="true" Text="Fecha" TextAlignment="BottomLeft" SizeF="130,28.125" LocationFloat="0,158.0617" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="155" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item22>
        <Item23 Ref="156" ControlType="XRLabel" Name="labelMovimiento" Multiline="true" TextAlignment="BottomLeft" SizeF="245.01239,28.125" LocationFloat="130.000015,129.936722" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="157" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item23>
        <Item24 Ref="158" ControlType="XRLabel" Name="label7" Multiline="true" TextAlignment="BottomLeft" SizeF="130,28.12" LocationFloat="0,129.936722" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="159" EventName="BeforePrint" PropertyName="Text" Expression="[TipoSolicitud]" />
          </ExpressionBindings>
          <StylePriority Ref="160" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item24>
        <Item25 Ref="161" ControlType="XRLabel" Name="labelNombeMovimiento" Multiline="true" Text="MOVIMIENTOS" TextAlignment="MiddleCenter" SizeF="750,25.9374924" LocationFloat="0,91.89422" Font="Arial, 14.25pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="162" UseFont="false" UseTextAlignment="false" />
        </Item25>
        <Item26 Ref="163" ControlType="XRLabel" Name="labelFecha" Multiline="true" Text="labelFecha" TextAlignment="MiddleLeft" SizeF="375.01236,21.875" LocationFloat="2.38418579E-05,0" Padding="2,2,0,0,100">
          <StylePriority Ref="164" UseTextAlignment="false" />
        </Item26>
        <Item27 Ref="165" ControlType="XRLabel" Name="labelEmpresa" Multiline="true" Text="labelEmpresa" TextAlignment="MiddleCenter" SizeF="750,34.375" LocationFloat="2.38418579E-05,45.4999924" Font="Arial, 14.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="166" UseFont="false" UseTextAlignment="false" />
        </Item27>
        <Item28 Ref="167" ControlType="XRLabel" Name="labelUsuario" Multiline="true" Text="labelUsuario&#xD;&#xA;" TextAlignment="MiddleRight" SizeF="239.990051,21.875" LocationFloat="510.009949,0" Padding="2,2,0,0,100">
          <StylePriority Ref="168" UseTextAlignment="false" />
        </Item28>
        <Item29 Ref="169" ControlType="XRTable" Name="table1" SizeF="748.9584,25" LocationFloat="1.04167461,20.4999924" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="170" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="171" ControlType="XRTableCell" Name="tableCell1" Weight="0.22037498279749695" Multiline="true" />
                <Item2 Ref="172" ControlType="XRTableCell" Name="tableCell3" Weight="0.22389452349911376" Multiline="true" />
                <Item3 Ref="173" ControlType="XRTableCell" Name="tableCell5" Weight="0.29641884314383726" Multiline="true" />
                <Item4 Ref="174" ControlType="XRTableCell" Name="tableCell7" Weight="0.5476641122918775" Multiline="true" />
                <Item5 Ref="175" ControlType="XRTableCell" Name="tableCell8" Weight="0.2583704359890131" Multiline="true" />
                <Item6 Ref="176" ControlType="XRTableCell" Name="tableCell12" Weight="0.20670474518139848" Multiline="true" />
                <Item7 Ref="177" ControlType="XRTableCell" Name="tableCell13" Weight="0.20669078527142279" Multiline="true" />
                <Item8 Ref="178" ControlType="XRTableCell" Name="tableCell16" Weight="0.20669263665297896" Multiline="true" />
                <Item9 Ref="179" ControlType="XRTableCell" Name="tableCell17" Weight="0.20669860882178226" Multiline="true" />
                <Item10 Ref="180" ControlType="XRTableCell" Name="tableCell18" Weight="0.20669860882178226" Multiline="true" />
              </Cells>
            </Item1>
          </Rows>
        </Item29>
      </Controls>
    </Item7>
  </Bands>
  <ObjectStorage>
    <Item1 ObjectType="DevExpress.XtraReports.Serialization.ObjectStorageInfo, DevExpress.XtraReports.v20.1" Ref="1" Content="System.Boolean" Type="System.Type" />
  </ObjectStorage>
</XtraReportsLayoutSerializer>