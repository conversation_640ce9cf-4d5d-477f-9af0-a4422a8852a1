﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="DetalleInventario" Landscape="true" Margins="12, 24, 100, 78" PaperKind="LegalExtra" PageWidth="1500" PageHeight="927" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" />
    <Item2 Ref="2" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="78.125">
      <Controls>
        <Item1 Ref="3" ControlType="XRLabel" Name="label8" Multiline="true" Text="Generado y Elavorado Por:" TextAlignment="BottomRight" SizeF="243.236145,28.125" LocationFloat="49.4722061,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="4" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="5" ControlType="XRLine" Name="line2" SizeF="193.402878,4.166666" LocationFloat="1121.7915,23.958334" />
        <Item3 Ref="6" ControlType="XRLabel" Name="label14" Multiline="true" Text="Revisado Por:" TextAlignment="BottomRight" SizeF="191.666687,28.125" LocationFloat="930.1248,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="7" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="8" ControlType="XRLine" Name="line1" SizeF="193.402878,4.166666" LocationFloat="292.708344,23.958334" />
      </Controls>
    </Item2>
    <Item3 Ref="9" ControlType="DetailBand" Name="Detail" HeightF="34.375">
      <Controls>
        <Item1 Ref="10" ControlType="XRTable" Name="table2" TextAlignment="BottomLeft" SizeF="1464.00012,34.375" LocationFloat="0,0" Padding="2,2,0,0,96" Borders="All">
          <Rows>
            <Item1 Ref="11" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="12" ControlType="XRTableCell" Name="tableCell19" Weight="0.***************" Multiline="true" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="13" EventName="BeforePrint" PropertyName="Text" Expression="[Codigo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="14" UseFont="false" />
                </Item1>
                <Item2 Ref="15" ControlType="XRTableCell" Name="tableCell20" Weight="2.6173640659877231" Multiline="true" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="16" EventName="BeforePrint" PropertyName="Text" Expression="[NombreProducto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="17" UseFont="false" />
                </Item2>
                <Item3 Ref="18" ControlType="XRTableCell" Name="tableCell21" Weight="0.54147058501279477" Multiline="true" TextAlignment="BottomLeft" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="19" EventName="BeforePrint" PropertyName="Text" Expression="[UnidadMedida]" />
                  </ExpressionBindings>
                  <StylePriority Ref="20" UseFont="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="21" ControlType="XRTableCell" Name="tableCell22" Weight="0.39626416227871319" Multiline="true" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="22" EventName="BeforePrint" PropertyName="Text" Expression="[Bodega]" />
                  </ExpressionBindings>
                  <StylePriority Ref="23" UseFont="false" />
                </Item4>
                <Item5 Ref="24" ControlType="XRTableCell" Name="tableCell23" Weight="0.67398981223429044" Multiline="true" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="25" EventName="BeforePrint" PropertyName="Text" Expression="[NombreBodega]" />
                  </ExpressionBindings>
                  <StylePriority Ref="26" UseFont="false" />
                </Item5>
                <Item6 Ref="27" ControlType="XRTableCell" Name="tableCell24" Weight="0.46252287240852985" Multiline="true" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="28" EventName="BeforePrint" PropertyName="Text" Expression="[Existencia]" />
                  </ExpressionBindings>
                  <StylePriority Ref="29" UseFont="false" />
                </Item6>
                <Item7 Ref="30" ControlType="XRTableCell" Name="tableCell25" Weight="0.24224641269310965" Multiline="true" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="31" EventName="BeforePrint" PropertyName="Text" Expression="[Tipo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="32" UseFont="false" />
                </Item7>
                <Item8 Ref="33" ControlType="XRTableCell" Name="tableCell26" Weight="0.76210056390977443" Multiline="true" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="34" EventName="BeforePrint" PropertyName="Text" Expression="[Fecha]" />
                  </ExpressionBindings>
                  <StylePriority Ref="35" UseFont="false" />
                </Item8>
                <Item9 Ref="36" ControlType="XRTableCell" Name="tableCell27" Weight="0.31273496240601473" Multiline="true" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="37" EventName="BeforePrint" PropertyName="Text" Expression="[Activo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="38" UseFont="false" />
                </Item9>
                <Item10 Ref="39" ControlType="XRTableCell" Name="tableCell28" Weight="0.48014584735848576" TextFormatString="{0:C2}" Multiline="true" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="40" EventName="BeforePrint" PropertyName="Text" Expression="[CostoUltimo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="41" UseFont="false" />
                </Item10>
                <Item11 Ref="42" ControlType="XRTableCell" Name="tableCell29" Weight="0.48014584735848576" TextFormatString="{0:C2}" Multiline="true" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="43" EventName="BeforePrint" PropertyName="Text" Expression="[CostoPromedio]" />
                  </ExpressionBindings>
                  <StylePriority Ref="44" UseFont="false" />
                </Item11>
                <Item12 Ref="45" ControlType="XRTableCell" Name="tableCell30" Weight="0.48014584735848576" TextFormatString="{0:C2}" Multiline="true" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="46" EventName="BeforePrint" PropertyName="Text" Expression="[CostoTotal]" />
                  </ExpressionBindings>
                  <StylePriority Ref="47" UseFont="false" />
                </Item12>
                <Item13 Ref="48" ControlType="XRTableCell" Name="tableCell31" Weight="0.25986842583296793" Multiline="true" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="49" EventName="BeforePrint" PropertyName="Text" Expression="[Serie]" />
                  </ExpressionBindings>
                  <StylePriority Ref="50" UseFont="false" />
                </Item13>
                <Item14 Ref="51" ControlType="XRTableCell" Name="tableCell32" Weight="0.524201123727171" Multiline="true" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="52" EventName="BeforePrint" PropertyName="Text" Expression="[Documento]" />
                  </ExpressionBindings>
                  <StylePriority Ref="53" UseFont="false" />
                </Item14>
                <Item15 Ref="54" ControlType="XRTableCell" Name="tableCell33" Weight="1.0792982378272307" Multiline="true" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="55" EventName="BeforePrint" PropertyName="Text" Expression="[Observacion]" />
                  </ExpressionBindings>
                  <StylePriority Ref="56" UseFont="false" />
                </Item15>
                <Item16 Ref="57" ControlType="XRTableCell" Name="tableCell34" Weight="0.70923241093933653" Multiline="true" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="58" EventName="BeforePrint" PropertyName="Text" Expression="[UltimaCompra]" />
                  </ExpressionBindings>
                  <StylePriority Ref="59" UseFont="false" />
                </Item16>
                <Item17 Ref="60" ControlType="XRTableCell" Name="tableCell35" Weight="1.1057319245894826" Multiline="true" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="61" EventName="BeforePrint" PropertyName="Text" Expression="[UltimaFacturaCompra]" />
                  </ExpressionBindings>
                  <StylePriority Ref="62" UseFont="false" />
                </Item17>
                <Item18 Ref="63" ControlType="XRTableCell" Name="tableCell36" Weight="0.83753261814819013" Multiline="true" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="64" EventName="BeforePrint" PropertyName="Text" Expression="[UltimoInventario]" />
                  </ExpressionBindings>
                  <StylePriority Ref="65" UseFont="false" />
                </Item18>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="66" UseBorders="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="67" ControlType="PageHeaderBand" Name="PageHeader" HeightF="154.166656">
      <SubBands>
        <Item1 Ref="68" ControlType="SubBand" Name="SubBand1" HeightF="34.375">
          <Controls>
            <Item1 Ref="69" ControlType="XRTable" Name="table1" TextAlignment="BottomLeft" SizeF="1464.00012,34.375" LocationFloat="0,0" Font="Arial Black, 9.75pt, style=Bold, charSet=0" ForeColor="Black" BackColor="SkyBlue" Padding="2,2,0,0,96" Borders="All">
              <Rows>
                <Item1 Ref="70" ControlType="XRTableRow" Name="tableRow1" Weight="1">
                  <Cells>
                    <Item1 Ref="71" ControlType="XRTableCell" Name="tableCell1" Weight="0.***************" Multiline="true" Text="Codigo" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                      <StylePriority Ref="72" UseFont="false" />
                    </Item1>
                    <Item2 Ref="73" ControlType="XRTableCell" Name="tableCell2" Weight="2.6173640659877231" Multiline="true" Text="Nombre" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                      <StylePriority Ref="74" UseFont="false" />
                    </Item2>
                    <Item3 Ref="75" ControlType="XRTableCell" Name="tableCell3" Weight="0.54147058501279477" Multiline="true" Text="Unidad de Medida" TextAlignment="BottomLeft" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                      <StylePriority Ref="76" UseFont="false" UseTextAlignment="false" />
                    </Item3>
                    <Item4 Ref="77" ControlType="XRTableCell" Name="tableCell4" Weight="0.39626416227871319" Multiline="true" Text="Bodega" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                      <StylePriority Ref="78" UseFont="false" />
                    </Item4>
                    <Item5 Ref="79" ControlType="XRTableCell" Name="tableCell5" Weight="0.67398981223429044" Multiline="true" Text="Bod Nombre " Font="Calibri, 8.25pt, style=Bold, charSet=0">
                      <StylePriority Ref="80" UseFont="false" />
                    </Item5>
                    <Item6 Ref="81" ControlType="XRTableCell" Name="tableCell6" Weight="0.46252287240852985" Multiline="true" Text="Existencia" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                      <StylePriority Ref="82" UseFont="false" />
                    </Item6>
                    <Item7 Ref="83" ControlType="XRTableCell" Name="tableCell7" Weight="0.24224641269310965" Multiline="true" Text="Tipo" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                      <StylePriority Ref="84" UseFont="false" />
                    </Item7>
                    <Item8 Ref="85" ControlType="XRTableCell" Name="tableCell8" Weight="0.76210056390977443" Multiline="true" Text="Fecha" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                      <StylePriority Ref="86" UseFont="false" />
                    </Item8>
                    <Item9 Ref="87" ControlType="XRTableCell" Name="tableCell9" Weight="0.31273496240601473" Multiline="true" Text="Activo" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                      <StylePriority Ref="88" UseFont="false" />
                    </Item9>
                    <Item10 Ref="89" ControlType="XRTableCell" Name="tableCell10" Weight="0.48014584735848576" Multiline="true" Text="Costo Ultimo&#xD;&#xA;" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                      <StylePriority Ref="90" UseFont="false" />
                    </Item10>
                    <Item11 Ref="91" ControlType="XRTableCell" Name="tableCell11" Weight="0.48014584735848576" Multiline="true" Text="Costo Promedio" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                      <StylePriority Ref="92" UseFont="false" />
                    </Item11>
                    <Item12 Ref="93" ControlType="XRTableCell" Name="tableCell12" Weight="0.48014584735848576" Multiline="true" Text="CostoTotal" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                      <StylePriority Ref="94" UseFont="false" />
                    </Item12>
                    <Item13 Ref="95" ControlType="XRTableCell" Name="tableCell13" Weight="0.25986842583296793" Multiline="true" Text="Serie" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                      <StylePriority Ref="96" UseFont="false" />
                    </Item13>
                    <Item14 Ref="97" ControlType="XRTableCell" Name="tableCell14" Weight="0.524201123727171" Multiline="true" Text="Documento" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                      <StylePriority Ref="98" UseFont="false" />
                    </Item14>
                    <Item15 Ref="99" ControlType="XRTableCell" Name="tableCell15" Weight="1.0792982378272307" Multiline="true" Text="Observacion" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                      <StylePriority Ref="100" UseFont="false" />
                    </Item15>
                    <Item16 Ref="101" ControlType="XRTableCell" Name="tableCell16" Weight="0.70923241093933653" Multiline="true" Text="Ultima Compra" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                      <StylePriority Ref="102" UseFont="false" />
                    </Item16>
                    <Item17 Ref="103" ControlType="XRTableCell" Name="tableCell17" Weight="1.1057319245894826" Multiline="true" Text="Ultima Factura Compra" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                      <StylePriority Ref="104" UseFont="false" />
                    </Item17>
                    <Item18 Ref="105" ControlType="XRTableCell" Name="tableCell18" Weight="0.83753261814819013" Multiline="true" Text="Ultimo Inventario" Font="Calibri, 8.25pt, style=Bold, charSet=0">
                      <StylePriority Ref="106" UseFont="false" />
                    </Item18>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="107" UseFont="false" UseForeColor="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </SubBands>
      <Controls>
        <Item1 Ref="108" ControlType="XRLabel" Name="label5" Multiline="true" Text="Tipo: RL Aceptación Requerimiento/DL Aceptacion Devolucion/CT Cargos admision/OR Devolucion Cargos/VL Venta Directa/VF Devolucion de Venta Directa/ML Aceptación Despachos de Bodega" TextAlignment="MiddleCenter" SizeF="1464.00024,37.4999924" LocationFloat="0,116.666664" Font="Arial, 9.75pt, style=Bold, charSet=0" ForeColor="Blue" Padding="2,2,0,0,100">
          <StylePriority Ref="109" UseFont="false" UseForeColor="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="110" ControlType="XRLabel" Name="labelBodega" Multiline="true" Text="Bodega:" SizeF="398.958344,29.166666" LocationFloat="0,87.5" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="111" UseFont="false" />
        </Item2>
        <Item3 Ref="112" ControlType="XRLabel" Name="label3" Multiline="true" Text="(Cifras en Quetzales)" SizeF="398.958344,29.166666" LocationFloat="0,58.3333321" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="113" UseFont="false" />
        </Item3>
        <Item4 Ref="114" ControlType="XRLabel" Name="labelFecha" Multiline="true" Text="Reporte de Existencias de Inventario al 31/01/2023" SizeF="398.958344,29.166666" LocationFloat="0,29.166666" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="115" UseFont="false" />
        </Item4>
        <Item5 Ref="116" ControlType="XRLabel" Name="label1" Multiline="true" Text="Servicios Médicos y Hospitalarios Centroamericanos, S.A." SizeF="398.958344,29.166666" LocationFloat="0,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="117" UseFont="false" />
        </Item5>
      </Controls>
    </Item4>
    <Item5 Ref="118" ControlType="GroupFooterBand" Name="GroupFooter1" HeightF="51.0416679">
      <Controls>
        <Item1 Ref="119" ControlType="XRLabel" Name="label4" TextFormatString="{0:C2}" Multiline="true" TextAlignment="TopRight" SizeF="126.527863,32.2916679" LocationFloat="803.5969,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <Summary Ref="120" Running="Group" />
          <ExpressionBindings>
            <Item1 Ref="121" EventName="BeforePrint" PropertyName="Text" Expression="sumSum([CostoTotal])" />
          </ExpressionBindings>
          <StylePriority Ref="122" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="123" ControlType="XRLabel" Name="label2" Multiline="true" SizeF="126.527863,32.2916679" LocationFloat="422.916473,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="124" EventName="BeforePrint" PropertyName="Text" Expression="Concat('Total: ', [Bodega])" />
          </ExpressionBindings>
          <StylePriority Ref="125" UseFont="false" />
        </Item2>
      </Controls>
    </Item5>
    <Item6 Ref="126" ControlType="GroupFooterBand" Name="GroupFooter2" Level="1" HeightF="43.75">
      <Controls>
        <Item1 Ref="127" ControlType="XRLabel" Name="label7" TextFormatString="{0:C2}" Multiline="true" TextAlignment="TopRight" SizeF="126.527893,32.2916641" LocationFloat="803.5969,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <Summary Ref="128" Running="Report" />
          <ExpressionBindings>
            <Item1 Ref="129" EventName="BeforePrint" PropertyName="Text" Expression="sumSum([CostoTotal])" />
          </ExpressionBindings>
          <StylePriority Ref="130" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="131" ControlType="XRLabel" Name="label6" Multiline="true" Text="Total General" SizeF="126.527863,32.2916679" LocationFloat="422.916473,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="132" UseFont="false" />
        </Item2>
      </Controls>
    </Item6>
    <Item7 Ref="133" ControlType="GroupHeaderBand" Name="GroupHeader1" HeightF="0">
      <GroupFields>
        <Item1 Ref="134" FieldName="Bodega" />
      </GroupFields>
    </Item7>
  </Bands>
</XtraReportsLayoutSerializer>