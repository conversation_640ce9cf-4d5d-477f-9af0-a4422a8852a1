﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="EnvioConsignacionOrtopediaPdf" Margins="100, 100, 380, 46" PageWidth="850" PageHeight="1100" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" HeightF="379.993439">
      <Controls>
        <Item1 Ref="2" ControlType="XRLabel" Name="label13" Multiline="true" TextAlignment="MiddleLeft" SizeF="52.0833321,19.7899933" LocationFloat="0,70.8333359" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="3" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="4" ControlType="XRLabel" Name="label10" Multiline="true" TextAlignment="MiddleLeft" SizeF="52.0833321,19.7899933" LocationFloat="0,130.204956" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="5" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="6" ControlType="XRLabel" Name="label9" Multiline="true" TextAlignment="MiddleLeft" SizeF="52.0833321,19.7899933" LocationFloat="0,169.784988" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="7" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="8" ControlType="XRLabel" Name="labelNoBodega" Multiline="true" TextAlignment="MiddleCenter" SizeF="152.083282,71.67673" LocationFloat="497.916718,288.525024" Font="Times New Roman, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100" Borders="All">
          <StylePriority Ref="9" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="10" ControlType="XRLabel" Name="label19" Multiline="true" Text="Correspondiente a la Bodega No." TextAlignment="MiddleCenter" SizeF="369.7916,71.67673" LocationFloat="128.125061,288.525024" Font="Times New Roman, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100" Borders="None">
          <StylePriority Ref="11" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="12" ControlType="XRLabel" Name="labelSucursal" Multiline="true" TextAlignment="MiddleCenter" SizeF="128.125,71.67673" LocationFloat="0,288.525024" Font="Times New Roman, 6.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100" Borders="All">
          <StylePriority Ref="13" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="14" ControlType="XRLabel" Name="label17" Multiline="true" Text="Por este medio, remito reporte de Envíos de Proveedores de Productos a Consignación de FARMACIA:" TextAlignment="MiddleLeft" SizeF="650,19.789978" LocationFloat="0,268.735" Font="Times New Roman, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="15" UseFont="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="16" ControlType="XRLabel" Name="labelFecha" Multiline="true" TextAlignment="MiddleLeft" SizeF="597.9167,19.7900085" LocationFloat="52.0833321,248.944977" Font="Times New Roman, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="17" UseFont="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="18" ControlType="XRLabel" Name="label15" Multiline="true" Text="Fecha:" TextAlignment="MiddleLeft" SizeF="52.0833321,19.7899933" LocationFloat="0,248.944977" Font="Times New Roman, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="19" UseFont="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="20" ControlType="XRLabel" Name="label14" Multiline="true" TextAlignment="MiddleLeft" SizeF="650,19.7899933" LocationFloat="0,229.155029" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="21" UseFont="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="22" ControlType="XRLabel" Name="labelMes" Multiline="true" TextAlignment="MiddleCenter" SizeF="81.25006,19.7899933" LocationFloat="568.749939,209.365021" Font="Times New Roman, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100" Borders="All">
          <StylePriority Ref="23" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="24" ControlType="XRLabel" Name="label12" Multiline="true" Text="Mes:" TextAlignment="MiddleCenter" SizeF="70.83334,19.7899933" LocationFloat="497.916656,209.36496" Font="Times New Roman, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="25" UseFont="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="26" ControlType="XRLabel" Name="labelDe" Multiline="true" TextAlignment="MiddleCenter" SizeF="369.791718,19.7899933" LocationFloat="128.125,209.365021" Font="Times New Roman, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100" Borders="All">
          <StylePriority Ref="27" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="28" ControlType="XRLabel" Name="labelPara" Multiline="true" TextAlignment="MiddleCenter" SizeF="369.791656,19.7899933" LocationFloat="128.125,189.575012" Font="Times New Roman, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="29" UseFont="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="30" ControlType="XRLabel" Name="label8" Multiline="true" Text="(Supervisor de Farmacia)" TextAlignment="MiddleCenter" SizeF="76.04167,19.7899933" LocationFloat="52.0833321,209.365021" Font="Times New Roman, 5.25pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="31" UseFont="false" UseTextAlignment="false" />
        </Item15>
        <Item16 Ref="32" ControlType="XRLabel" Name="label7" Multiline="true" Text="De:" TextAlignment="MiddleLeft" SizeF="52.0833321,19.7899933" LocationFloat="0,209.365021" Font="Times New Roman, 9.75pt, style=Bold, charSet=0" BackColor="SkyBlue" Padding="2,2,0,0,100" Borders="All">
          <StylePriority Ref="33" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item16>
        <Item17 Ref="34" ControlType="XRLabel" Name="label6" Multiline="true" Text="Para:" TextAlignment="MiddleLeft" SizeF="52.0833321,19.7899933" LocationFloat="0,189.575012" Font="Times New Roman, 9.75pt, style=Bold, charSet=0" BackColor="LightBlue" Padding="2,2,0,0,100" Borders="All">
          <StylePriority Ref="35" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item17>
        <Item18 Ref="36" ControlType="XRLabel" Name="label4" Multiline="true" Text="Suministro Médico" TextAlignment="MiddleRight" SizeF="377.083252,19.7899933" LocationFloat="191.666672,149.99498" Font="Times New Roman, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="37" UseFont="false" UseTextAlignment="false" />
        </Item18>
        <Item19 Ref="38" ControlType="XRLabel" Name="label5" Multiline="true" Text="X" TextAlignment="MiddleCenter" SizeF="81.25006,19.7899933" LocationFloat="568.749939,149.99498" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="39" UseFont="false" UseTextAlignment="false" />
        </Item19>
        <Item20 Ref="40" ControlType="XRLabel" Name="label3" Multiline="true" TextAlignment="MiddleCenter" SizeF="63.54167,19.7899933" LocationFloat="128.125,149.99498" Font="Times New Roman, 9.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="41" UseFont="false" UseTextAlignment="false" />
        </Item20>
        <Item21 Ref="42" ControlType="XRLabel" Name="label2" Multiline="true" Text="Medicamentos" TextAlignment="MiddleCenter" SizeF="128.125,19.7899933" LocationFloat="0,149.99498" Font="Times New Roman, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="43" UseFont="false" UseTextAlignment="false" />
        </Item21>
        <Item22 Ref="44" ControlType="XRLabel" Name="label1" Multiline="true" Text="SERVICIOS MEDICOS Y HOSPITALARIOS CENTROAMERICANOS" TextAlignment="MiddleCenter" SizeF="650,19.79" LocationFloat="0,110.414986" Font="Times New Roman, 12pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="45" UseFont="false" UseTextAlignment="false" />
        </Item22>
        <Item23 Ref="46" ControlType="XRLabel" Name="labelTituloUno" Multiline="true" Text="REPORTE  DE ENVÍOS DE PRODUCTO EN CONSIGNACION" TextAlignment="MiddleCenter" SizeF="650,19.7916641" LocationFloat="0,90.6233139" Font="Times New Roman, 12pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="47" UseFont="false" UseTextAlignment="false" />
        </Item23>
        <Item24 Ref="48" ControlType="XRPictureBox" Name="pictureBox1" ImageSource="img,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" SizeF="191.666672,70.8333359" LocationFloat="0,0" />
      </Controls>
    </Item1>
    <Item2 Ref="49" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="46.04823" />
    <Item3 Ref="50" ControlType="DetailBand" Name="Detail" HeightF="19.791666">
      <Controls>
        <Item1 Ref="51" ControlType="XRTable" Name="table2" TextAlignment="MiddleCenter" SizeF="650,19.791666" LocationFloat="0,0" Font="Times New Roman, 8.25pt, charSet=0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="52" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="53" ControlType="XRTableCell" Name="tableCell8" Weight="0.72463775013004916" Multiline="true" TextAlignment="TopCenter" Font="Times New Roman, 6.75pt, style=Bold, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="54" EventName="BeforePrint" PropertyName="Text" Expression="[Numero]" />
                  </ExpressionBindings>
                  <StylePriority Ref="55" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="56" ControlType="XRTableCell" Name="tableCell9" Weight="1.0579711424345522" Multiline="true" TextAlignment="TopCenter" Font="Times New Roman, 6.75pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="57" EventName="BeforePrint" PropertyName="Text" Expression="[EnvioProveedor]" />
                  </ExpressionBindings>
                  <StylePriority Ref="58" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="59" ControlType="XRTableCell" Name="tableCell10" Weight="0.88405803357521173" Multiline="true" TextAlignment="TopCenter" Font="Times New Roman, 6.75pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="60" EventName="BeforePrint" PropertyName="Text" Expression="[FechaEnvio]" />
                  </ExpressionBindings>
                  <StylePriority Ref="61" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="62" ControlType="XRTableCell" Name="tableCell11" Weight="0.88405803357521173" Multiline="true" TextAlignment="TopCenter" Font="Times New Roman, 6.75pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="63" EventName="BeforePrint" PropertyName="Text" Expression="[Validacion]" />
                  </ExpressionBindings>
                  <StylePriority Ref="64" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="65" ControlType="XRTableCell" Name="tableCell12" Weight="3.3768121934141577" Multiline="true" TextAlignment="TopLeft" Font="Times New Roman, 6.75pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="66" EventName="BeforePrint" PropertyName="Text" Expression="[NombreProveedor]" />
                  </ExpressionBindings>
                  <StylePriority Ref="67" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="68" ControlType="XRTableCell" Name="tableCell13" Weight="0.98550573824786158" Multiline="true" TextAlignment="TopCenter" Font="Times New Roman, 6.75pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="69" EventName="BeforePrint" PropertyName="Text" Expression="[Nit]" />
                  </ExpressionBindings>
                  <StylePriority Ref="70" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item6>
                <Item7 Ref="71" ControlType="XRTableCell" Name="tableCell14" Weight="1.1304357118464308" TextFormatString="{0:C2}" Multiline="true" TextAlignment="TopRight" Font="Times New Roman, 6.75pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="72" EventName="BeforePrint" PropertyName="Text" Expression="[Monto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="73" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item7>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="74" UseFont="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="75" ControlType="PageHeaderBand" Name="PageHeader" HeightF="19.791666">
      <Controls>
        <Item1 Ref="76" ControlType="XRTable" Name="table1" TextAlignment="MiddleCenter" SizeF="650,19.791666" LocationFloat="0,0" Font="Times New Roman, 8.25pt, charSet=0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="77" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="78" ControlType="XRTableCell" Name="tableCell1" Weight="0.72463775013004916" Multiline="true" Text="No." Font="Times New Roman, 8.25pt, style=Bold, charSet=0" Borders="All">
                  <StylePriority Ref="79" UseFont="false" UseBorders="false" />
                </Item1>
                <Item2 Ref="80" ControlType="XRTableCell" Name="tableCell2" Weight="1.0579711424345522" Multiline="true" Text="No. Envio" Font="Times New Roman, 8.25pt, style=Bold, charSet=0" Borders="All">
                  <StylePriority Ref="81" UseFont="false" UseBorders="false" />
                </Item2>
                <Item3 Ref="82" ControlType="XRTableCell" Name="tableCell3" Weight="0.88405803357521173" Multiline="true" Text="Fecha Envio" Font="Times New Roman, 8.25pt, style=Bold, charSet=0" Borders="All">
                  <StylePriority Ref="83" UseFont="false" UseBorders="false" />
                </Item3>
                <Item4 Ref="84" ControlType="XRTableCell" Name="tableCell4" Weight="0.88405803357521173" Multiline="true" Text="Validación" Font="Times New Roman, 8.25pt, style=Bold, charSet=0" Borders="All">
                  <StylePriority Ref="85" UseFont="false" UseBorders="false" />
                </Item4>
                <Item5 Ref="86" ControlType="XRTableCell" Name="tableCell5" Weight="3.3768121934141577" Multiline="true" Text="Nombre Proveedor" Font="Times New Roman, 8.25pt, style=Bold, charSet=0" Borders="All">
                  <StylePriority Ref="87" UseFont="false" UseBorders="false" />
                </Item5>
                <Item6 Ref="88" ControlType="XRTableCell" Name="tableCell6" Weight="0.98550573824786158" Multiline="true" Text="Nit" Font="Times New Roman, 8.25pt, style=Bold, charSet=0" Borders="All">
                  <StylePriority Ref="89" UseFont="false" UseBorders="false" />
                </Item6>
                <Item7 Ref="90" ControlType="XRTableCell" Name="tableCell7" Weight="1.1304357118464308" Multiline="true" Text="Monto" Font="Times New Roman, 8.25pt, style=Bold, charSet=0" Borders="All">
                  <StylePriority Ref="91" UseFont="false" UseBorders="false" />
                </Item7>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="92" UseFont="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item4>
    <Item5 Ref="93" ControlType="ReportFooterBand" Name="ReportFooter" HeightF="425.6775">
      <Controls>
        <Item1 Ref="94" ControlType="XRLabel" Name="label26" Multiline="true" Text="DEPARTAMENTO DE CONTABILIDAD" TextAlignment="MiddleLeft" SizeF="369.791656,19.7899857" LocationFloat="128.125,383.323151" Font="Times New Roman, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="95" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="96" ControlType="XRLabel" Name="label25" Multiline="true" Text="Nombre y firma de RECIBIDO" TextAlignment="MiddleLeft" SizeF="369.791656,19.7899857" LocationFloat="128.125,363.533142" Font="Times New Roman, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100" Borders="Top">
          <StylePriority Ref="97" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="98" ControlType="XRLabel" Name="label22" Multiline="true" Text="Nombre y Firma Elaborado" TextAlignment="MiddleLeft" SizeF="369.791656,19.7899857" LocationFloat="128.125,176.036514" Font="Times New Roman, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100" Borders="Top">
          <StylePriority Ref="99" UseFont="false" UseBorders="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="100" ControlType="XRLabel" Name="label21" TextFormatString="{0:C2}" Multiline="true" TextAlignment="MiddleRight" SizeF="81.25006,19.7899933" LocationFloat="568.749939,0" Font="Times New Roman, 6.75pt, style=Bold, charSet=0" BackColor="LightGray" Padding="2,2,0,0,100" Borders="All">
          <Summary Ref="101" Running="Report" />
          <ExpressionBindings>
            <Item1 Ref="102" EventName="BeforePrint" PropertyName="Text" Expression="sumSum([Monto])" />
          </ExpressionBindings>
          <StylePriority Ref="103" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="104" ControlType="XRLabel" Name="label11" Multiline="true" Text="*Al reporte se adjuntan la docuentación correspondiente que respalda la información reportada." TextAlignment="MiddleCenter" SizeF="497.916718,19.789978" LocationFloat="0,0" Font="Times New Roman, 6.75pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="105" UseFont="false" UseTextAlignment="false" />
        </Item5>
      </Controls>
    </Item5>
  </Bands>
</XtraReportsLayoutSerializer>