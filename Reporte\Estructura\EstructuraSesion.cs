﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Reporte.Estructura
{
    public class EstructuraSesion
    {
        public ReporteInfo opciones;
        /* session */
        public string session_id { get; set; }
        public string session_Ip { get; set; }
        public string session_ajeno { get; set; }
        public string session_nombre { get; set; }
        public string session_sucursal { get; set; }
        public string session_expiration { get; set; }
        public string session_empresa_real { get; set; }
        public string session_funcionalidad { get; set; }
        public string session_empresa_bodega { get; set; }
        public string session_empresa_unificadora { get; set; }
        public string session_empresa_sucursal { get; set; }
    }


    public class ReporteInfo
    {      
        public String tiporeporte { get; set; } = "application/pdf";
        public int? Bodega { get; set; }
        public int? Periodo { get; set; }
        public int? TipoBusqueda { get; set; }
        public DateTime? FechaInicio { set; get; }
        public DateTime? FechaFin { set; get; }
        public String ListaDepartamentos { get; set; }
        public String ListaProductos { get; set; }


    }


}
