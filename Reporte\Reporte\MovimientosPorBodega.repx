﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="MovimientosPorBodega" Landscape="true" Margins="100, 99, 100, 100" PageWidth="1100" PageHeight="850" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin">
      <Controls>
        <Item1 Ref="2" ControlType="XRLabel" Name="labelUsuario" Multiline="true" Text="labelUsuario&#xD;&#xA;" TextAlignment="MiddleRight" SizeF="152.083252,34.375" LocationFloat="747.916748,5.20832825" Padding="2,2,0,0,100">
          <StylePriority Ref="3" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="4" ControlType="XRLabel" Name="labelFecha" Multiline="true" Text="labelFecha" TextAlignment="MiddleLeft" SizeF="154.166656,34.375" LocationFloat="1.04166663,5.20832825" Padding="2,2,0,0,100">
          <StylePriority Ref="5" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="6" ControlType="XRLabel" Name="labelEmpresa" Multiline="true" Text="labelEmpresa" TextAlignment="MiddleCenter" SizeF="592.013855,34.3750038" LocationFloat="155.208328,5.20832825" Font="Arial, 14.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="7" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="8" ControlType="XRLabel" Name="labelNombreMovimiento" Multiline="true" Text="INVENTARIOS" TextAlignment="MiddleCenter" SizeF="592.013855,30.2083359" LocationFloat="155.208328,39.58333" Font="Arial, 14.25pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="9" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="10" ControlType="XRLabel" Name="label1" Multiline="true" Text="Movimientos Por Bodega" TextAlignment="MiddleCenter" SizeF="592.013855,30.2083359" LocationFloat="155.208328,69.7916641" Font="Arial, 14.25pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="11" UseFont="false" UseTextAlignment="false" />
        </Item5>
      </Controls>
    </Item1>
    <Item2 Ref="12" ControlType="BottomMarginBand" Name="BottomMargin" />
    <Item3 Ref="13" ControlType="DetailBand" Name="Detail" HeightF="27.083334">
      <Controls>
        <Item1 Ref="14" ControlType="XRTable" Name="table1" TextAlignment="BottomLeft" SizeF="900,26.4583263" LocationFloat="0,0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="15" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="16" ControlType="XRTableCell" Name="tableCell1" Weight="1" TextFormatString="{0:N}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="17" EventName="BeforePrint" PropertyName="Text" Expression="[CantidadPedida]" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="18" ControlType="XRTableCell" Name="tableCell2" Weight="1" TextFormatString="{0:N}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="19" EventName="BeforePrint" PropertyName="Text" Expression="[cantidad]" />
                  </ExpressionBindings>
                </Item2>
                <Item3 Ref="20" ControlType="XRTableCell" Name="tableCell3" Weight="1" TextFormatString="{0:N}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="21" EventName="BeforePrint" PropertyName="Text" Expression="[CantidadAceptada]" />
                  </ExpressionBindings>
                </Item3>
                <Item4 Ref="22" ControlType="XRTableCell" Name="tableCell4" Weight="1" TextFormatString="{0:N}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="23" EventName="BeforePrint" PropertyName="Text" Expression="[CostoMovimiento]" />
                  </ExpressionBindings>
                </Item4>
                <Item5 Ref="24" ControlType="XRTableCell" Name="tableCell5" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="25" EventName="BeforePrint" PropertyName="Text" Expression="[UnidadMedida]" />
                  </ExpressionBindings>
                </Item5>
                <Item6 Ref="26" ControlType="XRTableCell" Name="tableCell6" Weight="4" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="27" EventName="BeforePrint" PropertyName="Text" Expression="Concat([Producto], ' ', [NombreProducto])" />
                  </ExpressionBindings>
                </Item6>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="28" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="29" ControlType="PageHeaderBand" Name="PageHeader" HeightF="30.208334">
      <SubBands>
        <Item1 Ref="30" ControlType="SubBand" Name="SubBand1" HeightF="32.2916679">
          <Controls>
            <Item1 Ref="31" ControlType="XRTable" Name="table2" SizeF="900,29.166666" LocationFloat="0,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,96">
              <Rows>
                <Item1 Ref="32" ControlType="XRTableRow" Name="tableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="33" ControlType="XRTableCell" Name="tableCell7" Weight="1" TextFormatString="{0:N}" Multiline="true" Text="Cant. Ped." TextAlignment="BottomLeft">
                      <StylePriority Ref="34" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="35" ControlType="XRTableCell" Name="tableCell8" Weight="1" TextFormatString="{0:N}" Multiline="true" Text="Cant. Des." TextAlignment="BottomLeft">
                      <StylePriority Ref="36" UseTextAlignment="false" />
                    </Item2>
                    <Item3 Ref="37" ControlType="XRTableCell" Name="tableCell9" Weight="1" TextFormatString="{0:N}" Multiline="true" Text="Cant. Ace." TextAlignment="BottomLeft">
                      <StylePriority Ref="38" UseTextAlignment="false" />
                    </Item3>
                    <Item4 Ref="39" ControlType="XRTableCell" Name="tableCell10" Weight="1" TextFormatString="{0:N}" Multiline="true" Text="CProm" TextAlignment="BottomLeft">
                      <StylePriority Ref="40" UseTextAlignment="false" />
                    </Item4>
                    <Item5 Ref="41" ControlType="XRTableCell" Name="tableCell11" Weight="1" Multiline="true" Text="U. Medida" TextAlignment="BottomLeft">
                      <StylePriority Ref="42" UseTextAlignment="false" />
                    </Item5>
                    <Item6 Ref="43" ControlType="XRTableCell" Name="tableCell12" Weight="4" Multiline="true" Text="Producto" TextAlignment="BottomCenter">
                      <StylePriority Ref="44" UseTextAlignment="false" />
                    </Item6>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="45" UseFont="false" />
            </Item1>
          </Controls>
        </Item1>
      </SubBands>
      <Controls>
        <Item1 Ref="46" ControlType="XRLabel" Name="labelGroupHeader" Multiline="true" SizeF="900,30.208334" LocationFloat="0,0" Font="Arial, 9pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="47" UseFont="false" />
        </Item1>
      </Controls>
    </Item4>
    <Item5 Ref="48" ControlType="GroupFooterBand" Name="GroupFooter1" HeightF="68.75">
      <Controls>
        <Item1 Ref="49" ControlType="XRLabel" Name="label4" TextFormatString="{0:N}" Multiline="true" TextAlignment="BottomLeft" SizeF="100,32.2916679" LocationFloat="200,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <Summary Ref="50" Running="Group" />
          <ExpressionBindings>
            <Item1 Ref="51" EventName="BeforePrint" PropertyName="Text" Expression="sumSum([CostoMovimiento])&#xA;" />
          </ExpressionBindings>
          <StylePriority Ref="52" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="53" ControlType="XRLabel" Name="label6" Multiline="true" Text="Total:" TextAlignment="BottomLeft" SizeF="198.958328,32.2916679" LocationFloat="1.04166663,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="54" UseFont="false" UseTextAlignment="false" />
        </Item2>
      </Controls>
    </Item5>
    <Item6 Ref="55" ControlType="GroupFooterBand" Name="GroupFooter2" Level="1" HeightF="33.3333321">
      <Controls>
        <Item1 Ref="56" ControlType="XRLabel" Name="label3" TextFormatString="{0:N}" Multiline="true" TextAlignment="BottomLeft" SizeF="100,32.2916679" LocationFloat="200,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <Summary Ref="57" Running="Report" />
          <ExpressionBindings>
            <Item1 Ref="58" EventName="BeforePrint" PropertyName="Text" Expression="sumSum([CostoMovimiento])&#xA;" />
          </ExpressionBindings>
          <StylePriority Ref="59" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="60" ControlType="XRLabel" Name="label5" Multiline="true" Text="Gran Total Costo:" TextAlignment="BottomLeft" SizeF="198.958328,32.2916679" LocationFloat="1.04166663,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="61" UseFont="false" UseTextAlignment="false" />
        </Item2>
      </Controls>
    </Item6>
    <Item7 Ref="62" ControlType="PageFooterBand" Name="PageFooter" HeightF="37.5">
      <Controls>
        <Item1 Ref="63" ControlType="XRPageInfo" Name="pageInfo1" TextFormatString="Page {0} de {1}" SizeF="94.791626,36.4583321" LocationFloat="805.2083,0" Padding="2,2,0,0,100" />
      </Controls>
    </Item7>
    <Item8 Ref="64" ControlType="GroupHeaderBand" Name="GroupHeader1" HeightF="91.6666641">
      <GroupFields>
        <Item1 Ref="65" FieldName="Empresa" />
        <Item2 Ref="66" FieldName="Codigo" />
      </GroupFields>
      <Controls>
        <Item1 Ref="67" ControlType="XRLabel" Name="label8" Multiline="true" TextAlignment="BottomLeft" SizeF="899.958252,23" LocationFloat="1.04173028,60.4166336" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="68" EventName="BeforePrint" PropertyName="Text" Expression="Iif(IsNull([IdSolicitud], '') = '', '', Concat('No. Solicitud. ', [IdSolicitud]))" />
          </ExpressionBindings>
          <StylePriority Ref="69" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="70" ControlType="XRLabel" Name="label2" Multiline="true" TextAlignment="BottomLeft" SizeF="899.958252,23" LocationFloat="1.04173028,10.0000067" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="71" EventName="BeforePrint" PropertyName="Text" Expression="Concat([Codigo], '&#x9;', [Fecha], '&#x9;', [NombreTipo], '&#x9;', Iif(IsNull([Departamento]), Concat(' Bod. ', IsNull([BodegaFuente],''), '&#x9;',Iif(IsNull([BodegaDestino]), '',  Concat('a la Bod.  ', [BodegaDestino], ' '))), Concat('Depto. ', [Departamento], ' ')), IsNull([Proveedor],''), ' ', IsNull([NotaCredito],''), ' ', IsNull([Factura],''))&#xA;" />
          </ExpressionBindings>
          <StylePriority Ref="72" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="73" ControlType="XRLabel" Name="label7" Multiline="true" TextAlignment="BottomLeft" SizeF="899.958252,23" LocationFloat="0,37.41665" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="74" EventName="BeforePrint" PropertyName="Text" Expression="Concat('Reg. ', IsNull([FechaRegistro], ''), '&#x9;', ' Desp. ', IsNull([FechaDespacho], ''), '&#x9;', ' Acep. ', [FechaAceptado], '&#x9;', [Estado])" />
          </ExpressionBindings>
          <StylePriority Ref="75" UseFont="false" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item8>
  </Bands>
</XtraReportsLayoutSerializer>