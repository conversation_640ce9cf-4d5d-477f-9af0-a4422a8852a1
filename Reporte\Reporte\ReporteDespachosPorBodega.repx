﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="ReporteDespachosPorBodega" Margins="0, 21, 100, 0" PaperKind="Custom" PageWidth="1241" PageHeight="1500" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" HeightF="100.000008">
      <Controls>
        <Item1 Ref="2" ControlType="XRPageInfo" Name="pageInfo1" PageInfo="DateTime" TextFormatString="Fecha Generación: {0:dd/MM/yyyy HH:mm:ss}" SizeF="308.6745,13.3580132" LocationFloat="0,50.1191673" Font="Arial, 6.5pt" Padding="2,2,0,0,100">
          <StylePriority Ref="3" UseFont="false" />
        </Item1>
        <Item2 Ref="4" ControlType="XRLabel" Name="label1" Multiline="true" CanShrink="true" Text="Reporte de Requerimientos despachados por bodega" TextAlignment="MiddleLeft" SizeF="485.536957,14.6666746" LocationFloat="0,24.66669" Font="Arial, 10pt, style=Bold" ForeColor="255,0,96,151" Padding="2,2,0,0,100">
          <StylePriority Ref="5" UseFont="false" UseForeColor="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="6" ControlType="XRLabel" Name="uiTitulo" Multiline="true" CanShrink="true" Text="Servicios Médicos y Hospitalarios Centroamericanos, S.A." TextAlignment="MiddleLeft" SizeF="485.536957,14.6666746" LocationFloat="0,10.0000067" Font="Arial, 10pt, style=Bold" ForeColor="255,0,96,151" Padding="2,2,0,0,100">
          <StylePriority Ref="7" UseFont="false" UseForeColor="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="8" ControlType="XRTable" Name="table2" KeepTogether="true" SizeF="1220.00024,19.3340759" LocationFloat="0,80.66593" Padding="0,0,2,2,100" BorderColor="255,0,96,151">
          <Rows>
            <Item1 Ref="9" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="10" ControlType="XRTableCell" Name="tableCell8" Weight="0.35669454310238868" Multiline="true" Text="Empresa" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" Borders="None">
                  <StylePriority Ref="11" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorders="false" />
                </Item1>
                <Item2 Ref="12" ControlType="XRTableCell" Name="tableCell6" Weight="0.29653402811497109" Multiline="true" Text="Req." Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="13" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item2>
                <Item3 Ref="14" ControlType="XRTableCell" Name="tableCell7" Weight="0.92365806781095183" Multiline="true" Text="Nombre Producto" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="15" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item3>
                <Item4 Ref="16" ControlType="XRTableCell" Name="tableCell1" Weight="0.22033566134200516" Multiline="true" Text="Cantidad Aceptada" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="17" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item4>
                <Item5 Ref="18" ControlType="XRTableCell" Name="tableCell2" Weight="0.35337506297554183" Multiline="true" Text="Costo" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="19" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item5>
                <Item6 Ref="20" ControlType="XRTableCell" Name="tableCell3" Weight="0.33064116781053809" Multiline="true" Text="Fecha" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="21" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item6>
                <Item7 Ref="22" ControlType="XRTableCell" Name="tableCell4" Weight="0.33012282220525119" Multiline="true" Text="Bodega" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="23" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item7>
                <Item8 Ref="24" ControlType="XRTableCell" Name="tableCell5" Weight="0.8678486153388032" Multiline="true" Text="Bodega Despacho" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="25" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item8>
                <Item9 Ref="26" ControlType="XRTableCell" Name="tableCell9" Weight="0.38844210977682825" Multiline="true" Text="Departamento" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="27" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item9>
                <Item10 Ref="28" ControlType="XRTableCell" Name="tableCell10" Weight="1.2664720054952228" Multiline="true" Text="Nombre" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="29" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item10>
                <Item11 Ref="30" ControlType="XRTableCell" Name="tableCell11" Weight="0.3599047847283276" Multiline="true" Text="Mes" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="31" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item11>
                <Item12 Ref="32" ControlType="XRTableCell" Name="tableCell12" Weight="0.38811316406735874" Multiline="true" Text="Año" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="33" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item12>
                <Item13 Ref="34" ControlType="XRTableCell" Name="tableCell13" Weight="0.53371314370531286" Multiline="true" Text="Empleado" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="35" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item13>
                <Item14 Ref="36" ControlType="XRTableCell" Name="tableCell14" Weight="0.87299118197074077" Multiline="true" Text="Corporativo BI" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="37" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item14>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="38" UsePadding="false" UseBorderColor="false" />
        </Item4>
      </Controls>
    </Item1>
    <Item2 Ref="39" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" />
    <Item3 Ref="40" ControlType="DetailBand" Name="Detail" HeightF="18.21">
      <Controls>
        <Item1 Ref="41" ControlType="XRTable" Name="table1" SizeF="1220,18.21" LocationFloat="0,0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="42" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="43" ControlType="XRTableCell" Name="Periodo" Weight="0.18502480931196727" Multiline="true" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="44" EventName="BeforePrint" PropertyName="Text" Expression="[Empresa]" />
                  </ExpressionBindings>
                  <StylePriority Ref="45" UseFont="false" />
                </Item1>
                <Item2 Ref="46" ControlType="XRTableCell" Name="Producto" Weight="0.15381841962129877" Multiline="true" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="47" EventName="BeforePrint" PropertyName="Text" Expression="[Requerimiento]" />
                  </ExpressionBindings>
                  <StylePriority Ref="48" UseFont="false" />
                </Item2>
                <Item3 Ref="49" ControlType="XRTableCell" Name="NumeroSerie" Weight="0.47912050254359806" Multiline="true" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="50" EventName="BeforePrint" PropertyName="Text" Expression="[NombreProd]" />
                  </ExpressionBindings>
                  <StylePriority Ref="51" UseFont="false" />
                </Item3>
                <Item4 Ref="52" ControlType="XRTableCell" Name="Hospital" Weight="0.11429263931141279" Multiline="true" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="53" EventName="BeforePrint" PropertyName="Text" Expression="[CantidadAceptada]" />
                  </ExpressionBindings>
                  <StylePriority Ref="54" UseFont="false" />
                </Item4>
                <Item5 Ref="55" ControlType="XRTableCell" Name="tableCell16" Weight="0.18330290351689466" Multiline="true" Text="tableCell16" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="56" EventName="BeforePrint" PropertyName="Text" Expression="[Costo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="57" UseFont="false" />
                </Item5>
                <Item6 Ref="58" ControlType="XRTableCell" Name="tableCell17" Weight="0.17150979192839227" Multiline="true" Text="tableCell17" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="59" EventName="BeforePrint" PropertyName="Text" Expression="[Fecha]" />
                  </ExpressionBindings>
                  <StylePriority Ref="60" UseFont="false" />
                </Item6>
                <Item7 Ref="61" ControlType="XRTableCell" Name="tableCell18" Weight="0.17124208502679392" Multiline="true" Text="tableCell18" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="62" EventName="BeforePrint" PropertyName="Text" Expression="[Bodega]" />
                  </ExpressionBindings>
                  <StylePriority Ref="63" UseFont="false" />
                </Item7>
                <Item8 Ref="64" ControlType="XRTableCell" Name="tableCell19" Weight="0.45017089952754419" Multiline="true" Text="tableCell19" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="65" EventName="BeforePrint" PropertyName="Text" Expression="[BodegaDespacho]" />
                  </ExpressionBindings>
                  <StylePriority Ref="66" UseFont="false" />
                </Item8>
                <Item9 Ref="67" ControlType="XRTableCell" Name="tableCell20" Weight="0.2014923259650736" Multiline="true" Text="tableCell20" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="68" EventName="BeforePrint" PropertyName="Text" Expression="[Departamento]" />
                  </ExpressionBindings>
                  <StylePriority Ref="69" UseFont="false" />
                </Item9>
                <Item10 Ref="70" ControlType="XRTableCell" Name="tableCell21" Weight="0.65694604809640245" Multiline="true" Text="tableCell21" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="71" EventName="BeforePrint" PropertyName="Text" Expression="[Nombre]" />
                  </ExpressionBindings>
                  <StylePriority Ref="72" UseFont="false" />
                </Item10>
                <Item11 Ref="73" ControlType="XRTableCell" Name="tableCell22" Weight="0.18669000658432036" Multiline="true" Text="tableCell22" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="74" EventName="BeforePrint" PropertyName="Text" Expression="[Mes]" />
                  </ExpressionBindings>
                  <StylePriority Ref="75" UseFont="false" />
                </Item11>
                <Item12 Ref="76" ControlType="XRTableCell" Name="tableCell23" Weight="0.20132168997745209" Multiline="true" Text="tableCell23" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="77" EventName="BeforePrint" PropertyName="Text" Expression="[Año]" />
                  </ExpressionBindings>
                  <StylePriority Ref="78" UseFont="false" />
                </Item12>
                <Item13 Ref="79" ControlType="XRTableCell" Name="tableCell24" Weight="0.2768475922903329" Multiline="true" Text="tableCell24" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="80" EventName="BeforePrint" PropertyName="Text" Expression="[Empleado]" />
                  </ExpressionBindings>
                  <StylePriority Ref="81" UseFont="false" />
                </Item13>
                <Item14 Ref="82" ControlType="XRTableCell" Name="tableCell25" Weight="0.45283809440056466" Multiline="true" Text="tableCell25" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="83" EventName="BeforePrint" PropertyName="Text" Expression="[CorporativoBI]" />
                  </ExpressionBindings>
                  <StylePriority Ref="84" UseFont="false" />
                </Item14>
              </Cells>
            </Item1>
          </Rows>
        </Item1>
      </Controls>
    </Item3>
  </Bands>
</XtraReportsLayoutSerializer>