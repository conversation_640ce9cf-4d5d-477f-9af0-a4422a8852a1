﻿using Radiologia;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace API.Services
{

    public class UsuarioAuth
    {
        public int Id { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
    }


    public interface IUserService
    {
        Task<UsuarioAuth> Authenticate(string username, string password);
        Task<IEnumerable<UsuarioAuth>> GetAll();
    }

    public class UserService : IUserService
    {
        // users hardcoded for simplicity, store in a db with hashed passwords in production applications
        private List<UsuarioAuth> _users = new List<UsuarioAuth>
        {
            new UsuarioAuth { Id = 1,  Username = Startup.Configuration["BasicAuth:UserName"], Password = Startup.Configuration["BasicAuth:Password"] }
        };

        public async Task<UsuarioAuth> Authenticate(string username, string password)
        {
            var user = await Task.Run(() => _users.SingleOrDefault(x => x.Username == username && x.Password == password));

            // return null if user not found
            if (user == null)
                return null;

            // authentication successful so return user details without password
            user.Password = null;
            return user;
        }

        public async Task<IEnumerable<UsuarioAuth>> GetAll()
        {
            // return users without passwords
            return await Task.Run(() => _users.Select(x => {
                x.Password = null;
                return x;
            }));
        }
    }
}