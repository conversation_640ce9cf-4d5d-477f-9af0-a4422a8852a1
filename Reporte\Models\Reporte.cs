
using DevExpress.XtraPrinting.Drawing;
using DevExpress.XtraReports.UI;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Modelo.Conexion;
using Radiologia;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using DevExpress.XtraPrinting;
using Reporte.Models;
using System.Drawing.Imaging;
using Reporte.Reporte;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using Reporte.Estructura;
using ReporteCons.Models;


namespace Reportes.Models
{
    public class Info
    {

        public string tiporeporte { get; set; } = "application/pdf";
        public string EmpresaReal { get; set; } 
        public string nombrereporte { get; set; } = "";
        public string Hospital { get; set; } = "";
        public int IdCodigoUnico { get; set; } = 0;
        public string FechaInicial { get; set; } = null;
        public string FechaFinal { get; set; } = null;
        public string EmpresaProveedor { get; set; } = null;
        public string CodigoProveedor { get; set; } = null;
        public string json { get; set; } = null;
        public string Estado { get; set; } = null;
        public string TipoDocumento { get; set; } = null;
        public string IDORDENCOMPRAENC { get; set; } = null;
        public string CODIGO_MOVIMIENTO { get; set; } = null;
        public string CODIGO_REQUERIMIENTO { get; set; } = null;
        public string CODIGO_SOLICITUD { get; set; } = null;
        public string IDPROVEEDORFK { get; set; } = null;

        public string EmpresaInventario { get; set; } = null;
        public string Proveedor { get; set; } = null;
        public string Documento { get; set; } = null;
        public string CajaChica { get; set; } = null;
        public string Lote { get; set; } = null;
        public string NoPedidosSugeridos { get; set; } = null;
        public string SubOpcion { get; set; } = null;
        public string periodo { get; set; } = null;
        public string Empresa { get; set; } = null;
        public string ExcentoIva { get; set; } = null;
        public string VerCostoPromedio { get; set; } = "N";

        //Opciones Pelico 
        public string Fecha { get; set; } = null;
        public string FechaF { get; set; } = null;
        public string Operacion { get; set; } = null;

        public String ListaTecnicos { get; set; }

      
        public string CodigoBodega { get; set; }
        public string NombreBodega { get; set; }
        public string Existencia { get; set; }
        public string CodigoFamilia { get; set; }
        public string NombreFamilia { get; set; }
        public string CodigoProducto { get; set; }
        public string NombreProducto { get; set; }
        public string ProductoDel { get; set; }
        public string ProductoAl { get; set; }
        public string CodigoMovimiento { get; set; }
        public string TipoMovimiento { get; set; }
        public string BodegaFuente { get; set; }
        public string BodegaDestino { get; set; }
        public string OrdenarPor { get; set; }
        public string CodigoNota { get; set; }

        public string Pedido { get; set; }
        public string Envio { get; set; }
        public string Devolucion { get; set; }
        public string NombreProveedor { get; set; }
        public string Producto { get; set; }
        public string TipoLiquidacion { get; set; }
        public string Liquidacion { get; set; }
        public string CodigoLiquidacion {get;set;}
        public string OrdenCompra { get; set; }
        public string Factura { get; set; }
        public string NitProveedor { get; set; }
        public string FechaLiquidacion { get; set; }
        public string Parametro { get; set; }
        public string Tipo { get; set; }
        public string Mes { get; set; }
        public string ParaNombre { get; set; }
        public string DeNombre { get; set; }

        public int? AjusteImpresion { get; set; } = 0; //0 -  Valor por defecto que indica que el reporte se imprime en hoja tamaño carta / 1 - si el valor es uno imprime en media carta.
    }


    public class InfoEmpresa
    {
        public string nombre { get; set; }
        public byte[] Logo { get; set; }
        public byte[] LogoDomicilio { get; set; }
        public byte[] LogoPiePagina { get; set; }
    }

   

    public class Reporte
    {

        private IConexion conexion;
        public Info opciones;
        
        /* session */
        public string session_ip { get; set; }
        public string session_id { get; set; }
        public string session_nombre { get; set; }
        public string session_empresa_bodega { get; set; }
        public string session_empresa_unificadora { get; set; }
        public string session_empresa_real { get; set; }
        public string session_sucursal { get; set; }
        public string session_expira { get; set; }
        public string session_empresa_sucursal { get; set; }

        

        public Reporte()
        {
            String Usuario = Startup.Conexion["Tag_Usuario"].ToString();
            String Password = Startup.Conexion["Tag_Password"].ToString();

            String Instancia = Startup.Conexion["Instancia"].ToString();
            DBManager db = new  DBManager(Usuario, Password, Instancia);
            conexion = db.ObtenerConexion();
        }

        public InfoEmpresa ObtenerEmpresa(string Empresa = "MED")
        {
            InfoEmpresa respuesta = new InfoEmpresa();
            DataTable dt = new DataTable();
            try
            {   //actualizacion de git
                dt = conexion.getTableByQuery("select Nombre,Logo,LogoDomicilio,LogoPiePagina from  ContaDB.[dbo].Empresas ContaDB where Codigo  = '" + Empresa + "'", "HOSPITAL");
                if (dt.Rows.Count > 0)
                {
                    if (dt.Rows.Count > 0) respuesta.nombre = dt.Rows[0]["Nombre"].ToString().Trim();
                    if (dt.Rows.Count > 0 && dt.Rows[0]["Logo"] != null) respuesta.Logo = dt.Rows[0]["Logo"] as byte[];
                    if (dt.Rows.Count > 0 && dt.Rows[0]["LogoDomicilio"] != null) respuesta.LogoDomicilio = dt.Rows[0]["LogoDomicilio"] as byte[];
                    if (dt.Rows.Count > 0 && dt.Rows[0]["LogoPiePagina"] != null) respuesta.LogoPiePagina = dt.Rows[0]["LogoPiePagina"] as byte[];
                    return respuesta;
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        

        public MemoryStream ReporteGenerar(Reporte parametros)
        {
            
            XtraReport Reporte = null;
            if (parametros.opciones.nombrereporte == "reportedetalleordencompra") Reporte = ReporteDetalleOrdenCompra(parametros);
            if (parametros.opciones.nombrereporte == "reportedetallemovimiento") Reporte = ReporteDetalleMovimiento(parametros);
            if (parametros.opciones.nombrereporte == "reportecontrasenainventario") Reporte = ReporteContrasenaInventario(parametros);
            if (parametros.opciones.nombrereporte == "reportedetallelote") Reporte = ReporteDetalleLote(parametros);
            if (parametros.opciones.nombrereporte == "reportemovimientonuevo") Reporte = ReporteMovimientoNuevo(parametros);
            if (parametros.opciones.nombrereporte == "reportemovimientosrequerimientos" && parametros.opciones.tiporeporte == "application/vnd.ms-excel") Reporte = ReporteMovimientosRequerimientosExcel(parametros);
            if (parametros.opciones.nombrereporte == "reportemovimientosrequerimientos" && parametros.opciones.tiporeporte == "application/pdf") Reporte = ReporteMovimientosRequerimientos(parametros);
            if (parametros.opciones.nombrereporte == "RequerimientosDespachados") Reporte = RequerimientosDespachados(parametros);
            if (parametros.opciones.nombrereporte == "ListadePrecios") Reporte = ListadePrecios(parametros);

            if (parametros.opciones.nombrereporte == "reportependienteaceptartexto") Reporte = ReportePendienteAceptarTexto(parametros);
            if (parametros.opciones.nombrereporte == "reportependienteaceptarexcel") Reporte = ReportePendienteAceptarExcel(parametros);
            if (parametros.opciones.nombrereporte == "reportePedidosConsolidados") Reporte = ReportePedidosConsolidados(parametros);
            if (parametros.opciones.nombrereporte == "reporteBitacoraMontosLimite") Reporte = ReporteBitacoraMontosLimite(parametros);
            if (parametros.opciones.nombrereporte == "reporteCargosNoFacturadosPacientes") Reporte = CargosNoFacturadosPacientes(parametros);
            if (parametros.opciones.nombrereporte == "ReporteStatusTecnicosSoporte") Reporte = ReporteStatusTecnicosSoporte(parametros);


            if (parametros.opciones.nombrereporte == "reportedetalleinventario") Reporte = ReporteDetalleInventario(parametros);

            if (parametros.opciones.nombrereporte == "psicotropicounproducto") Reporte = ReportePsicotropicoUnProducto(parametros);
            if (parametros.opciones.nombrereporte == "psicotropicounafamilia") Reporte = ReportePsicotropicoUnaFamilia(parametros);
            if (parametros.opciones.nombrereporte == "psicotropicounproductointegrado") Reporte = ReportePsicotropicoUnProductoIntegrado(parametros);
            if (parametros.opciones.nombrereporte == "psicotropicounafamiliaintegrada") Reporte = ReportePsicotropicoUnaFamiliaIntegrada(parametros);
            if (parametros.opciones.nombrereporte == "bitacoraingresosbodega") Reporte = ReporteBitacoraIngresosBodega(parametros);
            if (parametros.opciones.nombrereporte == "existenciaporproducto") Reporte = ReporteExistenciaPorProducto(parametros);
            if (parametros.opciones.nombrereporte == "movimientosporbodega") Reporte = ReporteMovimientosPorBodega(parametros);
            if (parametros.opciones.nombrereporte == "reportemovimientonota") Reporte = ReporteMovimientoNota(parametros);

            if (parametros.opciones.nombrereporte == "reportepedidoconsignacion") Reporte = ReportePedidoConsignacion(parametros);
            if (parametros.opciones.nombrereporte == "reporteenvioconsignacion") Reporte = ReporteEnvioConsignacion(parametros);
            if (parametros.opciones.nombrereporte == "reportedevolucionconsignacion") Reporte = ReporteDevolucionConsignacion(parametros);
            if (parametros.opciones.nombrereporte == "reportedevolucionproveedorconsignacion") Reporte = ReporteDevolucionProveedorConsignacion(parametros);
            if (parametros.opciones.nombrereporte == "reportecargosproductosconsignacionexcel") Reporte = ReporteCargoProductoConsignacionExcel(parametros);
            if (parametros.opciones.nombrereporte == "reportecargosproductosconsignacionpdf") Reporte = ReporteCargoProductoConsignacionPDF(parametros);
            if (parametros.opciones.nombrereporte == "reporteliquidacionesexcel") Reporte = ReporteLiquidacionesExcel(parametros);
            if (parametros.opciones.nombrereporte == "reporteliquidaciondetalle") Reporte = ReporteLiquidacionDetalle(parametros);
            if (parametros.opciones.nombrereporte == "reporteliquidacioncostoultimo") Reporte = ReporteLiquidacionCostoUltimo(parametros);
            if (parametros.opciones.nombrereporte == "reporteliquidaciondetalleexcel") Reporte = ReporteLiquidacionDetalleExcel(parametros);
            if (parametros.opciones.nombrereporte == "reporteconveniosproveedores") Reporte = ReporteConveniosProveedores(parametros);
            if (parametros.opciones.nombrereporte == "reporteproductossinconvenioproveedor") Reporte = ReporteProductosSinConvenioProveedor(parametros);
            if (parametros.opciones.nombrereporte == "reporteconveniosinactivos") Reporte = ReporteConveniosInactivos(parametros);
            if (parametros.opciones.nombrereporte == "reportecontrolincidentes") Reporte = ReporteControlIncidentes(parametros);

            if (parametros.opciones.nombrereporte == "reporteenvioconsignacionortopedia") Reporte = EnvioConsignacionOrtopedia(parametros);

            if (parametros.opciones.nombrereporte == "reportebitacoramovimientoproducto") Reporte = ReporteBitacoraMovimientoProducto(parametros);

            
            //if (parametros.opciones.nombrereporte == "rptExistenciasProductosConsignacion") Reporte = RptConsingacionProductosConsigacion();

            using (MemoryStream ms2 = new MemoryStream())
            {
                if (parametros.opciones.tiporeporte == "text/csv") Reporte.ExportToCsv(ms2);
                if (parametros.opciones.tiporeporte == "application/vnd.ms-excel") Reporte.ExportToXlsx(ms2);
                if (parametros.opciones.tiporeporte == "application/pdf") Reporte.ExportToPdf(ms2);
                ms2.Seek(0, System.IO.SeekOrigin.Begin);
                return ms2;
            }
        }

        public ReporteStatusTecnicosSoporte ReporteStatusTecnicosSoporte(Reporte parametros)
        {
            try
            {
                ReporteStatusTecnicosSoporte reporte = new ReporteStatusTecnicosSoporte();
                DataTable dt = new DataTable();
                DataTable dtDetalle = new DataTable();
                DataColumn column;
                DataRow row;
                DataTable headerTable = new DataTable();
                List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
                List<SqlParameter> parametrosCabecera = new List<SqlParameter>();

                parametrosDeEntrada.Add(conexion.crearParametro("@i_Operacion", SqlDbType.Char, parametros.opciones.Operacion));
                parametrosDeEntrada.Add(conexion.crearParametro("@i_Fecha", SqlDbType.Date, parametros.opciones.Fecha ));
                parametrosDeEntrada.Add(conexion.crearParametro("@i_FechaF", SqlDbType.Date,parametros.opciones.FechaF));
                parametrosDeEntrada.Add(conexion.crearParametro("@i_ListadoTecnicos", SqlDbType.VarChar, parametros.opciones.ListaTecnicos));


                dt = conexion.getTableBySP("HOSPITAL", "SpStatusSolcitudesporTecnico", parametrosDeEntrada);

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "HOSPITAL";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "UsuarioAtendido";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "TecnicoFinaliza";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "departamento";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "sdescrpcion";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "sanalisis";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "FECHA";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;
                column = new DataColumn();

                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "FECHAFINALIZADO";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;


                reporte.DataSource = dt;
                reporte.DataMember = "Reporte";


                return reporte;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public CargosNoFacturadosPacientes CargosNoFacturadosPacientes(Reporte parametros)
        {
            try
            {
                CargosNoFacturadosPacientes reporte = new CargosNoFacturadosPacientes();
                DataTable dt = new DataTable();
                DataTable dtDetalle = new DataTable();
                DataColumn column;
                DataRow row;
                DataTable headerTable = new DataTable();
                List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
                List<SqlParameter> parametrosCabecera = new List<SqlParameter>();

                parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.Char, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.Char, "1"));
                parametrosDeEntrada.Add(conexion.crearParametro("@periodoIng", SqlDbType.Int, parametros.opciones.periodo));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaR", SqlDbType.Char, parametros.opciones.Hospital));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaU", SqlDbType.Char, parametros.session_empresa_unificadora));
                

                 dt = conexion.getTableBySP("HOSPITAL", "SpHisRptCargosNoFacturadosPacientes", parametrosDeEntrada);

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "TipoOrden";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "Orden";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "Linea";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "SerieAdmision";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "Admision";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "FechaCargo";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "Producto";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;


                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "Tipo";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "Nombre";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "PrecioUnitario";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "Cantidad";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "Costo";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "Valor";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;


                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "Entrada";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "Salida";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "Paciente";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "Nomb_Paciente";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "Codigo";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "NombreBodega";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;


                XRLabel LabelEmpresa = (XRLabel)reporte.FindControl("LBL_Empresa", false);
                LabelEmpresa.Text = dt.Rows[0]["Empresa"].ToString();

                reporte.DataSource = dt;
                reporte.DataMember = "Reporte";



                return reporte;
            }
            catch (Exception ex)
            {
                throw ex;
            }
           
        }

        public DetalleMovimiento ReporteDetalleMovimiento(Reporte parametros)
        {
            try
            {
                DetalleMovimiento reporte = new DetalleMovimiento();
                DataTable dt = new DataTable();
                DataTable headerTable = new DataTable();
                List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
                List<SqlParameter> parametrosCabecera = new List<SqlParameter>();
                string NombreEmpresa = "";
                string FechaActual = "";

                parametrosDeEntrada.Clear();
                parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
                dt = conexion.getTableBySP("HOSPITAL", "SpHisParametrosReporteInventario", parametrosDeEntrada);
                if (dt.Rows.Count > 0)
                {
                    NombreEmpresa = dt.Rows[0]["Nombre"].ToString();
                    FechaActual = dt.Rows[0]["FechaActual"].ToString();

                }

                dt.Clear();

                parametrosCabecera.Clear();
                parametrosCabecera.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
                parametrosCabecera.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "2"));
                parametrosCabecera.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
                parametrosCabecera.Add(conexion.crearParametro("@IEmpresaSucursal", SqlDbType.VarChar, parametros.session_empresa_sucursal));
                parametrosCabecera.Add(conexion.crearParametro("@ICodigoMovimiento", SqlDbType.VarChar, parametros.opciones.CODIGO_MOVIMIENTO));
                headerTable = conexion.getTableBySP("HOSPITAL", "SpHisParametrosReporteInventario", parametrosCabecera);

                parametrosDeEntrada.Clear();
                parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "3"));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
                parametrosDeEntrada.Add(conexion.crearParametro("@ICodigoMovimiento", SqlDbType.VarChar, parametros.opciones.CODIGO_MOVIMIENTO));
                parametrosDeEntrada.Add(conexion.crearParametro("@IExcentoIva", SqlDbType.VarChar, parametros.opciones.ExcentoIva));
                dt = conexion.getTableBySP("HOSPITAL", "SpHisParametrosReporteInventario", parametrosDeEntrada);


                reporte.DataSource = dt;

                XRLabel LabelEmpresa = (XRLabel)reporte.FindControl("labelEmpresa", false);
                LabelEmpresa.Text = NombreEmpresa;

                XRLabel labelUsuario = (XRLabel)reporte.FindControl("labelUsuario", false);
                labelUsuario.Text = parametros.session_id;

                XRLabel labelFecha = (XRLabel)reporte.FindControl("labelFecha", false);
                labelFecha.Text = FechaActual;

                XRLabel labelRevisadoOperado = (XRLabel)reporte.FindControl("labelRevisadoOperado", false);
                labelRevisadoOperado.Text = "FIRMA INGRESADO POR " + parametros.session_id + ":";

                if (headerTable.Rows.Count > 0)
                {
                    string Contrasena = headerTable.Rows[0]["CONTRASENA"].ToString().Trim();
                    string FechaMov = headerTable.Rows[0]["FECHA"].ToString().Trim();
                    string Factura = headerTable.Rows[0]["FACTURA"].ToString().Trim();
                    string Proveedor = headerTable.Rows[0]["PROVEEDOR"].ToString().Trim();
                    string BodegaDestino = headerTable.Rows[0]["BODEGA_DESTINO"].ToString().Trim();
                    string CodigoOrden = headerTable.Rows[0]["CODIGO_ORDEN"].ToString().Trim();
                    string FechaRegistro = headerTable.Rows[0]["FECHA_REGISTRO"].ToString().Trim();
                    string CodigoMovimiento = headerTable.Rows[0]["CODIGO_MOVIMIENTO"].ToString().Trim();
                    string StatusInventario = headerTable.Rows[0]["STATUS_INVENTARIO"].ToString().Trim();
                    string Comentario = headerTable.Rows[0]["COMENTARIO"].ToString().Trim();
                    string NombreTipo = headerTable.Rows[0]["NombreTipo"].ToString().Trim();

                    XRLabel tableContrasena = (XRLabel)reporte.FindControl("labelContrasena", false);
                    tableContrasena.Text = Contrasena;

                    XRLabel tableFechaMov = (XRLabel)reporte.FindControl("labelFechaMov", false);
                    tableFechaMov.Text = FechaMov;

                    XRLabel tableFactura = (XRLabel)reporte.FindControl("labelFactura", false);
                    tableFactura.Text = Factura;

                    XRLabel tableProveedor = (XRLabel)reporte.FindControl("labelProveedor", false);
                    tableProveedor.Text = Proveedor;

                    XRLabel tableBodegaDestino = (XRLabel)reporte.FindControl("labelBodegaDestino", false);
                    tableBodegaDestino.Text = BodegaDestino;

                    XRLabel tableCodigoOrden = (XRLabel)reporte.FindControl("labelCodigoOrden", false);
                    tableCodigoOrden.Text = CodigoOrden;

                    XRLabel tableFechaRegistro = (XRLabel)reporte.FindControl("labelFechaRegistro", false);
                    tableFechaRegistro.Text = FechaRegistro;

                    XRLabel tableCodigoMovimiento = (XRLabel)reporte.FindControl("labelCodigoMovimeinto", false);
                    tableCodigoMovimiento.Text = CodigoMovimiento;

                    XRLabel tableStatusInventario = (XRLabel)reporte.FindControl("labelStatusInventario", false);
                    tableStatusInventario.Text = StatusInventario;

                    XRLabel tableComentario = (XRLabel)reporte.FindControl("labelComentario", false);
                    tableComentario.Text = Comentario;

                    XRLabel tableNombreTipo = (XRLabel)reporte.FindControl("labelNombreMovimiento", false);
                    tableNombreTipo.Text = NombreTipo;
                }


                return reporte;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public ContrasenaInventario ReporteContrasenaInventario(Reporte parametros)
        {
            ContrasenaInventario reporte = new ContrasenaInventario();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "4"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaSucursal", SqlDbType.VarChar, parametros.session_empresa_sucursal));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaUnificadora", SqlDbType.VarChar, parametros.session_empresa_unificadora));
            parametrosDeEntrada.Add(conexion.crearParametro("@IDocumento", SqlDbType.VarChar, parametros.opciones.Documento));
            parametrosDeEntrada.Add(conexion.crearParametro("@IProveedor", SqlDbType.VarChar, parametros.opciones.IDPROVEEDORFK));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisParametrosReporteInventario", parametrosDeEntrada);

            reporte.DataSource = dt;

            return reporte;

        }

        public PedidosConsolidados ReportePedidosConsolidados(Reporte parametros)
        {
            try
            {
                PedidosConsolidados reporte = new PedidosConsolidados();
                DataTable dt = new DataTable();
                DataTable headerTable = new DataTable();
                List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
                List<SqlParameter> parametrosCabecera = new List<SqlParameter>();
                string NombreEmpresa = "";
                string FechaActual = "";
                
                parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
                dt = conexion.getTableBySP("HOSPITAL", "SpHisParametrosReporteInventario", parametrosDeEntrada);
                if (dt.Rows.Count > 0)
                {
                    NombreEmpresa = dt.Rows[0]["Nombre"].ToString();
                    FechaActual = dt.Rows[0]["FechaActual"].ToString();

                }
                dt.Clear();
                parametrosDeEntrada.Clear();
                parametrosDeEntrada.Add(conexion.crearParametro("@Opcion", SqlDbType.VarChar, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@SubOpcion", SqlDbType.VarChar, "A"));
                parametrosDeEntrada.Add(conexion.crearParametro("@EmpresaBodega", SqlDbType.VarChar, parametros.session_empresa_bodega));
                parametrosDeEntrada.Add(conexion.crearParametro("@EmpresaUnificada", SqlDbType.VarChar, parametros.session_empresa_unificadora));
                parametrosDeEntrada.Add(conexion.crearParametro("@EmpresaHospital", SqlDbType.VarChar, parametros.session_empresa_real));
                parametrosDeEntrada.Add(conexion.crearParametro("@ListaNoPedidos", SqlDbType.VarChar, parametros.opciones.NoPedidosSugeridos));
                dt = conexion.getTableBySP("HOSPITAL", "spHisPedidoSugerido", parametrosDeEntrada);

                reporte.DataSource = dt;

                XRLabel LabelEmpresa = (XRLabel)reporte.FindControl("labelEmpresa", false);
                LabelEmpresa.Text = NombreEmpresa;

                XRLabel labelUsuario = (XRLabel)reporte.FindControl("labelUsuario", false);
                labelUsuario.Text = "Usuario: "+parametros.session_id;

                XRLabel labelFecha = (XRLabel)reporte.FindControl("labelFecha", false);
                labelFecha.Text = FechaActual;

                XRLabel labelPedidos = (XRLabel)reporte.FindControl("pedidoConsolidado", false);
                labelPedidos.Text = "Pedidos Consolidados: "+ parametros.opciones.NoPedidosSugeridos;


                return reporte;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public BitacoraMontosLimite ReporteBitacoraMontosLimite(Reporte parametros)
        {
            try
            {
                BitacoraMontosLimite reporte = new BitacoraMontosLimite();
                DataTable dt = new DataTable();
                DataTable dtDetalle = new DataTable();
                DataColumn column;
                DataRow row;
                DataTable headerTable = new DataTable();
                List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
                List<SqlParameter> parametrosCabecera = new List<SqlParameter>();
                string NombreEmpresa = "";
                string FechaActual = "";

                parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
                dt = conexion.getTableBySP("HOSPITAL", "SpHisParametrosReporteInventario", parametrosDeEntrada);
                if (dt.Rows.Count > 0)
                {
                    NombreEmpresa = dt.Rows[0]["Nombre"].ToString();
                    FechaActual = dt.Rows[0]["FechaActual"].ToString();

                }
                dt.Clear();
                parametrosDeEntrada.Clear();
                parametrosDeEntrada.Add(conexion.crearParametro("@Opcion", SqlDbType.Char, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@SubOpcion", SqlDbType.Char, parametros.opciones.SubOpcion));
                parametrosDeEntrada.Add(conexion.crearParametro("@EmpresaUnificada", SqlDbType.VarChar, parametros.session_empresa_unificadora));
                parametrosDeEntrada.Add(conexion.crearParametro("@EmpresaBodega", SqlDbType.VarChar, parametros.session_empresa_bodega));
                parametrosDeEntrada.Add(conexion.crearParametro("@EmpresaReal", SqlDbType.VarChar, parametros.session_empresa_real));
                parametrosDeEntrada.Add(conexion.crearParametro("@Hospital", SqlDbType.VarChar, parametros.session_empresa_sucursal));
                parametrosDeEntrada.Add(conexion.crearParametro("@CodigoAgrupacionMenor", SqlDbType.Int, 0));
                parametrosDeEntrada.Add(conexion.crearParametro("@CodigoAgrupacionMayor", SqlDbType.Int, 0));
                parametrosDeEntrada.Add(conexion.crearParametro("@CodigosBodegas", SqlDbType.VarChar, ""));
                parametrosDeEntrada.Add(conexion.crearParametro("@MontosMensuales", SqlDbType.VarChar, ""));
                parametrosDeEntrada.Add(conexion.crearParametro("@Estados", SqlDbType.VarChar, ""));
                parametrosDeEntrada.Add(conexion.crearParametro("@TiposCambio", SqlDbType.VarChar, ""));
                parametrosDeEntrada.Add(conexion.crearParametro("@IdFuncionalidad", SqlDbType.Int, 0));
                parametrosDeEntrada.Add(conexion.crearParametro("@FechaInicial", SqlDbType.VarChar, parametros.opciones.FechaInicial));
                parametrosDeEntrada.Add(conexion.crearParametro("@FechaFinal", SqlDbType.VarChar, parametros.opciones.FechaFinal));
                parametrosDeEntrada.Add(conexion.crearParametro("@Corporativo", SqlDbType.Int, parametros.session_id));
                dt = conexion.getTableBySP("HOSPITAL", "HisMontosLimiteSubBodega", parametrosDeEntrada);

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.Int32");
                column.ColumnName = "corporativo";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                dtDetalle.Columns.Add(column);

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.String");
                column.ColumnName = "fecha";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                dtDetalle.Columns.Add(column);

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.String");
                column.ColumnName = "nombreCorporativo";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                dtDetalle.Columns.Add(column);

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.String");
                column.ColumnName = "bodega";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                dtDetalle.Columns.Add(column);

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.String");
                column.ColumnName = "nombreBodega";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                dtDetalle.Columns.Add(column);

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.String");
                column.ColumnName = "valorAnterior";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                dtDetalle.Columns.Add(column);

                column = new DataColumn();
                column.DataType = System.Type.GetType("System.String");
                column.ColumnName = "valorActual";
                column.AutoIncrement = false;
                column.ReadOnly = false;
                column.Unique = false;

                dtDetalle.Columns.Add(column);

                if (parametros.opciones.SubOpcion.Equals("E"))
                {
                    XRTableCell valorAnteriorTitulo = (XRTableCell)reporte.FindControl("valorAnteriorTitulo", false);
                    valorAnteriorTitulo.Text = "Estado Anterior";
                    XRTableCell valorActualTitulo = (XRTableCell)reporte.FindControl("valorActualTitulo", false);
                    valorActualTitulo.Text = "Estado Actual";
                    XRTableCell valorAnterior = (XRTableCell)reporte.FindControl("valorAnterior", false);
                    valorAnterior.TextFormatString = "";
                    valorAnterior.TextAlignment = TextAlignment.TopCenter;
                    valorAnterior.ExpressionBindings.Add(new ExpressionBinding("Text", "valorAnterior"));
                    XRTableCell valorActual = (XRTableCell)reporte.FindControl("valorActual", false);
                    valorActual.TextFormatString = "";
                    valorActual.ExpressionBindings.Add(new ExpressionBinding("Text", "valorActual"));
                    valorActual.TextAlignment = TextAlignment.TopCenter;


                    foreach (DataRow fila in dt.Rows)
                    {
                        List<EstructuraBitacora> registrosBitacorra = JsonConvert.DeserializeObject<List<EstructuraBitacora>>(fila["registros"].ToString());
                        foreach(EstructuraBitacora filaBitacora in registrosBitacorra)
                        {
                            row = dtDetalle.NewRow();
                            row["corporativo"] = fila["corporativo"];
                            row["fecha"] = fila["fecha"];
                            row["nombreCorporativo"] = fila["nombres"] + " " + fila["apellidos"];
                            row["bodega"] = filaBitacora.bodega;
                            row["nombreBodega"] = filaBitacora.nombre;
                            row["valorAnterior"] = filaBitacora.estadoAnterior.Equals("S")?"ACTIVO":"INACTIVO";
                            row["valorActual"] = filaBitacora.estado.Equals("S") ? "ACTIVO" : "INACTIVO";
                            dtDetalle.Rows.Add(row);
                        }
                    }
                }
                else
                {
                    XRTableCell valorAnteriorTitulo = (XRTableCell)reporte.FindControl("valorAnteriorTitulo", false);
                    valorAnteriorTitulo.Text = "Monto Anterior";
                    XRTableCell valorActualTitulo = (XRTableCell)reporte.FindControl("valorActualTitulo", false);
                    valorActualTitulo.Text = "Monto Actual";                    
                    foreach (DataRow fila in dt.Rows)
                    {
                        List<EstructuraBitacora> registrosBitacorra = JsonConvert.DeserializeObject<List<EstructuraBitacora>>(fila["registros"].ToString());
                        foreach (EstructuraBitacora filaBitacora in registrosBitacorra)
                        {
                            row = dtDetalle.NewRow();
                            row["corporativo"] = fila["corporativo"];
                            row["fecha"] = fila["fecha"];
                            row["nombreCorporativo"] = fila["nombres"] + " " + fila["apellidos"];
                            row["bodega"] = filaBitacora.bodega;
                            row["nombreBodega"] = filaBitacora.nombre;
                            row["valorAnterior"] = filaBitacora.montoMensualAnterior;
                            row["valorActual"] = filaBitacora.montoMensual;
                            dtDetalle.Rows.Add(row);
                        }
                    }
                }

                reporte.DataSource = dtDetalle;
                
                XRLabel LabelEmpresa = (XRLabel)reporte.FindControl("labelEmpresa", false);
                LabelEmpresa.Text = NombreEmpresa;

                XRLabel labelUsuario = (XRLabel)reporte.FindControl("labelUsuario", false);
                labelUsuario.Text = "Usuario: " + parametros.session_id;

                XRLabel labelFecha = (XRLabel)reporte.FindControl("labelFecha", false);
                labelFecha.Text = FechaActual;

                return reporte;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public DetalleOrdenCompra ReporteDetalleOrdenCompra(Reporte parametros)
        {
            try
            {
                DetalleOrdenCompra reporte = new DetalleOrdenCompra();
                DataTable dt = new DataTable();
                DataTable headerTable = new DataTable();
                List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
                List<SqlParameter> parametrosCabecera = new List<SqlParameter>();
                string NombreEmpresa = "";
                string FechaActual = "";

                parametrosDeEntrada.Clear();
                parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
                dt = conexion.getTableBySP("HOSPITAL", "SpHisParametrosReporteInventario", parametrosDeEntrada);
                if (dt.Rows.Count > 0)
                {
                    NombreEmpresa = dt.Rows[0]["Nombre"].ToString();
                    FechaActual = dt.Rows[0]["FechaActual"].ToString();

                }

                dt.Clear();

                parametrosCabecera.Clear();
                parametrosCabecera.Add(conexion.crearParametro("@i_OrdenCompraEnc", SqlDbType.VarChar, parametros.opciones.IDORDENCOMPRAENC));
                headerTable = conexion.getTableBySP("HOSPITAL", "sp_inv_rep_movimientos", parametrosCabecera);

                parametrosDeEntrada.Clear();
                parametrosDeEntrada.Add(conexion.crearParametro("@c_IdOrdenEnc", SqlDbType.VarChar, parametros.opciones.IDORDENCOMPRAENC));
                dt = conexion.getTableBySP("HOSPITAL", "sp_inv_rep_movimientos_lineas", parametrosDeEntrada);


                reporte.DataSource = dt;

                XRLabel LabelEmpresa = (XRLabel)reporte.FindControl("labelEmpresa", false);
                LabelEmpresa.Text = NombreEmpresa;

                XRLabel labelUsuario = (XRLabel)reporte.FindControl("labelUsuario", false);
                labelUsuario.Text = parametros.session_id;

                XRLabel labelFecha = (XRLabel)reporte.FindControl("labelFecha", false);
                labelFecha.Text = FechaActual;

                if (headerTable.Rows.Count > 0)
                {
                    string CodigoOrdenCompra = headerTable.Rows[0]["CODIGOORDEN"].ToString().Trim();
                    string Proveedor = headerTable.Rows[0]["PROVEEDOR"].ToString().Trim();
                    string BodegaDestino = headerTable.Rows[0]["BODEGA_DESTINO"].ToString().Trim();
                    string Factura = headerTable.Rows[0]["FACTURA"].ToString().Trim();
                    string FechaFactura = headerTable.Rows[0]["FACT_FECHA"].ToString().Trim();

                    XRLabel tableCodigoOrdenCompra = (XRLabel)reporte.FindControl("labelCodigoOrdenCompra", false);
                    tableCodigoOrdenCompra.Text = CodigoOrdenCompra;

                    XRLabel tableProveedor = (XRLabel)reporte.FindControl("labelProveedor", false);
                    tableProveedor.Text = Proveedor;

                    XRLabel tableBodegaDestino = (XRLabel)reporte.FindControl("labelBodegaDestino", false);
                    tableBodegaDestino.Text = BodegaDestino;

                    XRLabel tableFactura = (XRLabel)reporte.FindControl("labelFactura", false);
                    tableFactura.Text = Factura;

                    XRLabel tableFechaFactura = (XRLabel)reporte.FindControl("labelFechaFactura", false);
                    tableFechaFactura.Text = FechaFactura;

                }
                

                return reporte;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }


        public ListaPrecios ListadePrecios(Reporte parametros)
        {
            ListaPrecios reporte = new ListaPrecios();
            try
            {
                DataTable dt = new DataTable();
                List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
                DateTime localDate = DateTime.Now;
                var culture = new CultureInfo("en-US");

                //XRLabel lbl_Documento = (XRLabel)reporte.FindControl("lbl_Documento", false);
                //lbl_Documento.Text = "Documento: " + parametros.opciones.Documento;
                //XRLabel lbl_Proveedor = (XRLabel)reporte.FindControl("lbl_Proveedor", false);
                //lbl_Proveedor.Text = "Proveedor: " + parametros.opciones.Proveedor;


                parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEstado", SqlDbType.VarChar, "I"));
                parametrosDeEntrada.Add(conexion.crearParametro("@IProveedor", SqlDbType.VarChar, parametros.opciones.Proveedor));
                parametrosDeEntrada.Add(conexion.crearParametro("@IDocumento", SqlDbType.VarChar, parametros.opciones.Documento));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaUnificadora", SqlDbType.VarChar, parametros.session_empresa_unificadora));
                //parametrosDeEntrada.Add(conexion.crearParametro("@IFechaInicio", SqlDbType.VarChar, FormatFecha(Convert.ToDateTime(parametros.opciones.FechaInicial.Substring(0, 10) + " 00:00:00"), "sp")));
                //parametrosDeEntrada.Add(conexion.crearParametro("@IFechaFin", SqlDbType.VarChar, FormatFecha(Convert.ToDateTime(parametros.opciones.FechaFinal.Substring(0, 10) + " 23:59:59"), "sp")));

                dt = conexion.getTableBySP("HOSPITAL", "SpInventarioNivelesPrecio", parametrosDeEntrada);

                reporte.DataSource = dt;
                reporte.DataMember = "Reporte";


               

            }
            catch (Exception ex)
            {
                Console.WriteLine(" info:" + ex.ToString());
                throw ex;
            }
            return reporte;
        }

        public RequerimientosDespachados RequerimientosDespachados(Reporte parametros)
        {
            try
            {

                RequerimientosDespachados Reporte = new RequerimientosDespachados();
                DataTable dt = new DataTable();
                List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
                List<Objeto> objetos;
                DateTime localDate = DateTime.Now;
                var culture = new CultureInfo("en-US");

                parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));

                parametrosDeEntrada.Add(conexion.crearParametro("@FechaInicial", SqlDbType.VarChar, DateTime.ParseExact(parametros.opciones.FechaInicial.ToString(), "dd/MM/yyyy", System.Globalization.CultureInfo.InvariantCulture).ToString("yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture)));
                parametrosDeEntrada.Add(conexion.crearParametro("@FechaFinal", SqlDbType.VarChar, DateTime.ParseExact(parametros.opciones.FechaFinal.ToString(), "dd/MM/yyyy", System.Globalization.CultureInfo.InvariantCulture).ToString("yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture)));
                parametrosDeEntrada.Add(conexion.crearParametro("@EmpresaBodega", SqlDbType.VarChar, parametros.session_empresa_bodega));
                dt = conexion.getTableBySP("HOSPITAL", "SpReporteRequerimientosDespachados", parametrosDeEntrada);

                Reporte.DataSource = dt;
                Reporte.DataMember = "Reporte";

                objetos = JsonConvert.DeserializeObject<List<Objeto>>(parametros.opciones.json);
                int posX = 0;
                foreach (var objeto in objetos)
                {
                    if (objeto.value.Equals("1"))
                    {
                        XRLabel b = new XRLabel();
                        b.Text = "prueba";
                        b.LocationF = new PointF(posX, 0);
                        b.SizeF = new Size(125, 50);
                        b.Font = new Font("Arial", 9);
                        b.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
                        b.CanGrow = false;
                        b.AutoWidth = true;
                        b.Multiline = true;
                        b.ExpressionBindings.Add(new ExpressionBinding("Text", objeto.Parametro));
                        DetailBand detalle = (DetailBand)Reporte.FindControl("Detail", false);
                        detalle.Controls.Add(b);

                        XRLabel c = new XRLabel();
                        c.Text = objeto.Etiqueta;
                        c.LocationF = new PointF(posX, 0);
                        c.SizeF = new Size(125, 50);
                        c.AutoWidth = true;
                        c.Font = new Font("Arial", 9, FontStyle.Bold);
                        c.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
                        c.CanGrow = false;
                        TopMarginBand enc = (TopMarginBand)Reporte.FindControl("TopMargin", false);
                        enc.Controls.Add(c);

                        posX = posX + 125;
                    }
                }

                return Reporte;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public DetalleLote ReporteDetalleLote(Reporte parametros)
        {
            DetalleLote reporte = new DetalleLote();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "5"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaSucursal", SqlDbType.VarChar, parametros.session_empresa_sucursal));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaUnificadora", SqlDbType.VarChar, parametros.session_empresa_unificadora));
            parametrosDeEntrada.Add(conexion.crearParametro("@ICajaChica", SqlDbType.VarChar, parametros.opciones.CajaChica));
            parametrosDeEntrada.Add(conexion.crearParametro("@ILote", SqlDbType.VarChar, parametros.opciones.Lote));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisParametrosReporteInventario", parametrosDeEntrada);

            reporte.DataSource = dt;

            return reporte;

        }

        public MovimientoNuevo ReporteMovimientoNuevo(Reporte parametros)
        {
            try
            {
                MovimientoNuevo reporte = new MovimientoNuevo();

                DataTable dt = new DataTable();
                List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
                string NombreEmpresa = "";
                string FechaActual = "";

                parametrosDeEntrada.Clear();
                parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
                dt = conexion.getTableBySP("HOSPITAL", "SpHisParametrosReporteInventario", parametrosDeEntrada);
                if (dt.Rows.Count > 0)
                {
                    NombreEmpresa = dt.Rows[0]["Nombre"].ToString();
                    FechaActual = dt.Rows[0]["FechaActual"].ToString();

                }

                dt.Clear();
                parametrosDeEntrada.Clear();
                parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "6"));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
                parametrosDeEntrada.Add(conexion.crearParametro("@ICodigoMovimiento", SqlDbType.VarChar, parametros.opciones.CODIGO_MOVIMIENTO));
                dt = conexion.getTableBySP("HOSPITAL", "SpHisParametrosReporteInventario", parametrosDeEntrada);


                reporte.DataSource = dt;

                XRLabel LabelEmpresa = (XRLabel)reporte.FindControl("labelEmpresa", false);
                LabelEmpresa.Text = NombreEmpresa;

                XRLabel labelUsuario = (XRLabel)reporte.FindControl("labelUsuario", false);
                labelUsuario.Text = parametros.session_id;

                XRLabel labelFecha = (XRLabel)reporte.FindControl("labelFecha", false);
                labelFecha.Text = FechaActual;

                XRLabel labelRevisadoOperado = (XRLabel)reporte.FindControl("labelRevisadoOperado", false);
                labelRevisadoOperado.Text = "FIRMA INGRESADO POR " + parametros.session_id + ":";

                if (dt.Rows.Count > 0)
                {
                    string CodigoMovimiento = dt.Rows[0]["Codigo"].ToString().Trim();
                    string FechaMov = dt.Rows[0]["Fecha"].ToString().Trim();
                    string TipoMovimiento = dt.Rows[0]["TipoMovimiento"].ToString().Trim();
                    string DeBodega = dt.Rows[0]["BodegaFuente"].ToString().Trim();
                    string ABodega = dt.Rows[0]["BodegaDestino"].ToString().Trim();
                    string FechaRegistro = dt.Rows[0]["FechaRegistro"].ToString().Trim();
                    string FechaDespacho = dt.Rows[0]["FechaDespacho"].ToString().Trim();
                    string FechaAceptada = dt.Rows[0]["FechaAceptado"].ToString().Trim();
                    string EstadoMovimiento = dt.Rows[0]["StatusInventario"].ToString().Trim();
                    string Observaciones = dt.Rows[0]["Comentario"].ToString().Trim();
                    string NombreMovimiento = dt.Rows[0]["NombreTipo"].ToString().Trim();

                    XRLabel tableCodigoMovimiento = (XRLabel)reporte.FindControl("labelMovimiento", false);
                    tableCodigoMovimiento.Text = CodigoMovimiento;

                    XRLabel tableFechaMov = (XRLabel)reporte.FindControl("labelFechaMovimiento", false);
                    tableFechaMov.Text = FechaMov;

                    XRLabel tableTipoMovimiento = (XRLabel)reporte.FindControl("labelTipoMovimiento", false);
                    tableTipoMovimiento.Text = TipoMovimiento;

                    XRLabel tableDeBodega = (XRLabel)reporte.FindControl("labelDeBodega", false);
                    tableDeBodega.Text = DeBodega;

                    XRLabel tableABodega = (XRLabel)reporte.FindControl("labelABodega", false);
                    tableABodega.Text = ABodega;

                    XRLabel tableFechaRegistro = (XRLabel)reporte.FindControl("labelFechaRegistro", false);
                    tableFechaRegistro.Text = FechaRegistro;

                    XRLabel tableFechaDespacho = (XRLabel)reporte.FindControl("labelFechaDespacho", false);
                    tableFechaDespacho.Text = FechaDespacho;

                    XRLabel tableFechaAceptado = (XRLabel)reporte.FindControl("labelFechaAceptado", false);
                    tableFechaAceptado.Text = FechaAceptada;

                    XRLabel tableEstadoMovimiento = (XRLabel)reporte.FindControl("labelEstadoMovimiento", false);
                    tableEstadoMovimiento.Text = EstadoMovimiento;

                    XRLabel tableObservaciones = (XRLabel)reporte.FindControl("labelObservaciones", false);
                    tableObservaciones.Text = Observaciones;

                    XRLabel tableNombreMovimiento = (XRLabel)reporte.FindControl("labelNombeMovimiento", false);
                    tableNombreMovimiento.Text = NombreMovimiento;
                }


                return reporte;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public MovimientosRequerimientos ReporteMovimientosRequerimientos(Reporte parametros)
        {
            try
            {
                MovimientosRequerimientos reporte = new MovimientosRequerimientos();
                  if (parametros.opciones.AjusteImpresion == 1)
                {
                    //Reporte.PaperKind
                    reporte.PaperKind = System.Drawing.Printing.PaperKind.Custom;
                    reporte.PageWidth = 850;   // 8.5 inches
                    reporte.PageHeight = 550; // 10.5 inches
                    reporte.Margins.Left = 13;
                }
                DataTable dt = new DataTable();
                List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
                string NombreEmpresa = "";
                string FechaActual = "";
                reporte.Parameters["VerCostoPromedio"].Value = parametros.opciones.VerCostoPromedio == "S";
                parametrosDeEntrada.Clear();
                parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
                dt = conexion.getTableBySP("HOSPITAL", "SpHisParametrosReporteInventario", parametrosDeEntrada);
                if (dt.Rows.Count > 0)
                {
                    NombreEmpresa = dt.Rows[0]["Nombre"].ToString();
                    FechaActual = dt.Rows[0]["FechaActual"].ToString();

                }

                dt.Clear();
                parametrosDeEntrada.Clear();
                parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_bodega));
                parametrosDeEntrada.Add(conexion.crearParametro("@ICodigoSolicitud", SqlDbType.VarChar, parametros.opciones.CODIGO_SOLICITUD));
                parametrosDeEntrada.Add(conexion.crearParametro("@ICodigoMovimiento", SqlDbType.VarChar, parametros.opciones.CODIGO_MOVIMIENTO));
                parametrosDeEntrada.Add(conexion.crearParametro("@ICodigoRequerimiento", SqlDbType.VarChar, parametros.opciones.CODIGO_REQUERIMIENTO));
                dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteMovimientosRequerimientos", parametrosDeEntrada);


                reporte.DataSource = dt;

                XRLabel LabelEmpresa = (XRLabel)reporte.FindControl("labelEmpresa", false);
                LabelEmpresa.Text = NombreEmpresa;

                XRLabel labelUsuario = (XRLabel)reporte.FindControl("labelUsuario", false);
                labelUsuario.Text = parametros.session_id;

                XRLabel labelFecha = (XRLabel)reporte.FindControl("labelFecha", false);
                labelFecha.Text = FechaActual;

                GroupFooterBand totalizar = (GroupFooterBand)reporte.FindControl("GroupFooter1", false);
                totalizar.Visible = parametros.opciones.VerCostoPromedio == "S";

                if (dt.Rows.Count > 0)
                {
                    string CodigoMovimiento = dt.Rows[0]["Codigo"].ToString().Trim();
                    string CodigoSolicitud = dt.Rows[0]["CodigoSolicitud"].ToString().Trim();
                    string FechaMov = dt.Rows[0]["Fecha"].ToString().Trim();
                    string TipoMovimiento = dt.Rows[0]["TipoMovimiento"].ToString().Trim();
                    //string DeBodega = dt.Rows[0]["BodegaFuente"].ToString().Trim();
                    //string ABodega = dt.Rows[0]["BodegaDestino"].ToString().Trim();
                    string FechaRegistro = dt.Rows[0]["FechaRegistro"].ToString().Trim();
                    string FechaDespacho = dt.Rows[0]["FechaDespacho"].ToString().Trim();
                    string FechaAceptada = dt.Rows[0]["FechaAceptado"].ToString().Trim();
                    string EstadoMovimiento = dt.Rows[0]["StatusInventario"].ToString().Trim();
                    string Observaciones = dt.Rows[0]["Comentario"].ToString().Trim();
                    string NombreMovimiento = dt.Rows[0]["NombreTipo"].ToString().Trim();
                    string Usuario = dt.Rows[0]["Usuario"].ToString().Trim();

                    XRLabel tableCodigoMovimiento = (XRLabel)reporte.FindControl("labelMovimiento", false);
                    tableCodigoMovimiento.Text = CodigoMovimiento + (CodigoSolicitud == "0" || CodigoSolicitud == "" ? "": " - Solicitud:"+CodigoSolicitud);                    

                    XRLabel tableFechaMov = (XRLabel)reporte.FindControl("labelFechaMovimiento", false);
                    tableFechaMov.Text = FechaMov;

                    XRLabel tableTipoMovimiento = (XRLabel)reporte.FindControl("labelTipoMovimiento", false);
                    tableTipoMovimiento.Text = TipoMovimiento;

                    //XRLabel tableDeBodega = (XRLabel)reporte.FindControl("labelDeBodega", false);
                    //tableDeBodega.Text = DeBodega;

                    //XRLabel tableABodega = (XRLabel)reporte.FindControl("labelABodega", false);
                    //tableABodega.Text = ABodega;

                    XRLabel tableFechaRegistro = (XRLabel)reporte.FindControl("labelFechaRegistro", false);
                    tableFechaRegistro.Text = FechaRegistro;

                    XRLabel tableFechaDespacho = (XRLabel)reporte.FindControl("labelFechaDespacho", false);
                    tableFechaDespacho.Text = FechaDespacho;

                    XRLabel tableFechaAceptado = (XRLabel)reporte.FindControl("labelFechaAceptado", false);
                    tableFechaAceptado.Text = FechaAceptada;

                    XRLabel tableEstadoMovimiento = (XRLabel)reporte.FindControl("labelEstadoMovimiento", false);
                    tableEstadoMovimiento.Text = EstadoMovimiento;

                    XRLabel tableObservaciones = (XRLabel)reporte.FindControl("labelObservaciones", false);
                    tableObservaciones.Text = Observaciones;

                    XRLabel tableNombreMovimiento = (XRLabel)reporte.FindControl("labelNombeMovimiento", false);
                    tableNombreMovimiento.Text = NombreMovimiento;

                    XRLabel labelRevisadoOperado = (XRLabel)reporte.FindControl("labelRevisadoOperado", false);
                    labelRevisadoOperado.Text = "FIRMA INGRESADO POR " + Usuario + ":";
                }


                return reporte;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public MovimientosRequerimientosExcel ReporteMovimientosRequerimientosExcel(Reporte parametros)
        {
            try
            {
                MovimientosRequerimientosExcel reporte = new MovimientosRequerimientosExcel();
                DataTable dt = new DataTable();
                List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
                string NombreEmpresa = "";
                string FechaActual = "";
                reporte.Parameters["VerCostoPromedio"].Value = parametros.opciones.VerCostoPromedio == "S";
                parametrosDeEntrada.Clear();
                parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
                dt = conexion.getTableBySP("HOSPITAL", "SpHisParametrosReporteInventario", parametrosDeEntrada);
                if (dt.Rows.Count > 0)
                {
                    NombreEmpresa = dt.Rows[0]["Nombre"].ToString();
                    FechaActual = dt.Rows[0]["FechaActual"].ToString();

                }

                dt.Clear();
                parametrosDeEntrada.Clear();
                parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_bodega));
                parametrosDeEntrada.Add(conexion.crearParametro("@ICodigoSolicitud", SqlDbType.VarChar, parametros.opciones.CODIGO_SOLICITUD));
                parametrosDeEntrada.Add(conexion.crearParametro("@ICodigoMovimiento", SqlDbType.VarChar, parametros.opciones.CODIGO_MOVIMIENTO));
                parametrosDeEntrada.Add(conexion.crearParametro("@ICodigoRequerimiento", SqlDbType.VarChar, parametros.opciones.CODIGO_REQUERIMIENTO));
                dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteMovimientosRequerimientos", parametrosDeEntrada);


                reporte.DataSource = dt;

                XRLabel LabelEmpresa = (XRLabel)reporte.FindControl("labelEmpresa", false);
                LabelEmpresa.Text = NombreEmpresa;

                XRLabel labelUsuario = (XRLabel)reporte.FindControl("labelUsuario", false);
                labelUsuario.Text = parametros.session_id;

                XRLabel labelFecha = (XRLabel)reporte.FindControl("labelFecha", false);
                labelFecha.Text = FechaActual;

                GroupFooterBand totalizar = (GroupFooterBand)reporte.FindControl("GroupFooter1", false);
                totalizar.Visible = parametros.opciones.VerCostoPromedio == "S";

                if (dt.Rows.Count > 0)
                {
                    string CodigoMovimiento = dt.Rows[0]["Codigo"].ToString().Trim();
                    string CodigoSolicitud = dt.Rows[0]["CodigoSolicitud"].ToString().Trim();
                    string FechaMov = dt.Rows[0]["Fecha"].ToString().Trim();
                    string TipoMovimiento = dt.Rows[0]["TipoMovimiento"].ToString().Trim();
                    //string DeBodega = dt.Rows[0]["BodegaFuente"].ToString().Trim();
                    //string ABodega = dt.Rows[0]["BodegaDestino"].ToString().Trim();
                    string FechaRegistro = dt.Rows[0]["FechaRegistro"].ToString().Trim();
                    string FechaDespacho = dt.Rows[0]["FechaDespacho"].ToString().Trim();
                    string FechaAceptada = dt.Rows[0]["FechaAceptado"].ToString().Trim();
                    string EstadoMovimiento = dt.Rows[0]["StatusInventario"].ToString().Trim();
                    string Observaciones = dt.Rows[0]["Comentario"].ToString().Trim();
                    string NombreMovimiento = dt.Rows[0]["NombreTipo"].ToString().Trim();
                    string Usuario = dt.Rows[0]["Usuario"].ToString().Trim();

                    XRLabel tableCodigoMovimiento = (XRLabel)reporte.FindControl("labelMovimiento", false);
                    tableCodigoMovimiento.Text = CodigoMovimiento + (CodigoSolicitud == "0" || CodigoSolicitud == "" ? "" : " - Solicitud:" + CodigoSolicitud);

                    XRLabel tableFechaMov = (XRLabel)reporte.FindControl("labelFechaMovimiento", false);
                    tableFechaMov.Text = FechaMov;

                    XRLabel tableTipoMovimiento = (XRLabel)reporte.FindControl("labelTipoMovimiento", false);
                    tableTipoMovimiento.Text = TipoMovimiento;

                    //XRLabel tableDeBodega = (XRLabel)reporte.FindControl("labelDeBodega", false);
                    //tableDeBodega.Text = DeBodega;

                    //XRLabel tableABodega = (XRLabel)reporte.FindControl("labelABodega", false);
                    //tableABodega.Text = ABodega;

                    XRLabel tableFechaRegistro = (XRLabel)reporte.FindControl("labelFechaRegistro", false);
                    tableFechaRegistro.Text = FechaRegistro;

                    XRLabel tableFechaDespacho = (XRLabel)reporte.FindControl("labelFechaDespacho", false);
                    tableFechaDespacho.Text = FechaDespacho;

                    XRLabel tableFechaAceptado = (XRLabel)reporte.FindControl("labelFechaAceptado", false);
                    tableFechaAceptado.Text = FechaAceptada;

                    XRLabel tableEstadoMovimiento = (XRLabel)reporte.FindControl("labelEstadoMovimiento", false);
                    tableEstadoMovimiento.Text = EstadoMovimiento;

                    XRLabel tableObservaciones = (XRLabel)reporte.FindControl("labelObservaciones", false);
                    tableObservaciones.Text = Observaciones;

                    XRLabel tableNombreMovimiento = (XRLabel)reporte.FindControl("labelNombeMovimiento", false);
                    tableNombreMovimiento.Text = NombreMovimiento;
                }


                return reporte;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }



        public PendienteAceptarTexto ReportePendienteAceptarTexto(Reporte parametros)
        {
            PendienteAceptarTexto reporte = new PendienteAceptarTexto();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            dt = conexion.getTableBySP("HOSPITAL", "SpHiConsultaDespachosPendientesAceptar", parametrosDeEntrada);

            reporte.DataSource = dt;

            return reporte;

        }

        public PendienteAceptarExcel ReportePendienteAceptarExcel(Reporte parametros)
        {
            PendienteAceptarExcel reporte = new PendienteAceptarExcel();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            dt = conexion.getTableBySP("HOSPITAL", "SpHiConsultaDespachosPendientesAceptar", parametrosDeEntrada);

            reporte.DataSource = dt;

            return reporte;

        }

        public DetalleInventario ReporteDetalleInventario(Reporte parametros)
        {
            DetalleInventario reporte = new DetalleInventario();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
            string NombreBodega = "";

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "3"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@IBodega", SqlDbType.VarChar, parametros.opciones.CodigoBodega));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReportesInventario", parametrosDeEntrada);
            if (dt.Rows.Count > 0)
            {
                NombreBodega = dt.Rows[0]["NombreBodega"].ToString();

            }


            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "2"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFecha", SqlDbType.VarChar, parametros.opciones.Fecha));
            parametrosDeEntrada.Add(conexion.crearParametro("@IBodega", SqlDbType.VarChar, parametros.opciones.CodigoBodega));
            parametrosDeEntrada.Add(conexion.crearParametro("@IExistencia", SqlDbType.VarChar, parametros.opciones.Existencia));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReportesInventario", parametrosDeEntrada);

            reporte.DataSource = dt;

            XRLabel labelFecha = (XRLabel)reporte.FindControl("labelFecha", false);
            labelFecha.Text = "Reporte de Existencias de Inventario al " + parametros.opciones.Fecha;

            XRLabel labelRevisadoOperado = (XRLabel)reporte.FindControl("labelBodega", false);
            labelRevisadoOperado.Text = parametros.opciones.CodigoBodega != "" ? "Bodega: " + NombreBodega : "Todas las Bodegas";

            return reporte;

        }

        public PsicotropicoUnProducto ReportePsicotropicoUnProducto(Reporte parametros)
        {
            PsicotropicoUnProducto reporte = new PsicotropicoUnProducto();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "7"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaInicial", SqlDbType.VarChar, parametros.opciones.FechaInicial));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaFinal", SqlDbType.VarChar, parametros.opciones.FechaFinal));
            parametrosDeEntrada.Add(conexion.crearParametro("@IBodega", SqlDbType.VarChar, parametros.opciones.CodigoBodega));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFamiliaProducto", SqlDbType.VarChar, parametros.opciones.CodigoFamilia));
            parametrosDeEntrada.Add(conexion.crearParametro("@IProducto", SqlDbType.VarChar, parametros.opciones.CodigoProducto));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaUnificadora", SqlDbType.VarChar, parametros.session_empresa_unificadora));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReportesInventario", parametrosDeEntrada);

            reporte.DataSource = dt;

            XRLabel labelRangoFechas = (XRLabel)reporte.FindControl("labelRangoFechas", false);
            labelRangoFechas.Text = "Del: " + parametros.opciones.FechaInicial + " Al: " + parametros.opciones.FechaFinal;

            XRLabel labelSucursalFamilia = (XRLabel)reporte.FindControl("labelSucursalFamilia", false);
            labelSucursalFamilia.Text = "Sucursal: " + parametros.opciones.NombreBodega + " Familia: " + parametros.opciones.NombreFamilia;

            XRLabel labelProducto = (XRLabel)reporte.FindControl("labelProducto", false);
            labelProducto.Text = parametros.opciones.CodigoFamilia == "0" ? "" : "Producto: " + parametros.opciones.NombreProducto;

            return reporte;

        }

        public PsicotropicoUnProducto ReportePsicotropicoUnaFamilia(Reporte parametros)
        {
            PsicotropicoUnProducto reporte = new PsicotropicoUnProducto();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "8"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaInicial", SqlDbType.VarChar, parametros.opciones.FechaInicial));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaFinal", SqlDbType.VarChar, parametros.opciones.FechaFinal));
            parametrosDeEntrada.Add(conexion.crearParametro("@IBodega", SqlDbType.VarChar, parametros.opciones.CodigoBodega));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFamiliaProducto", SqlDbType.VarChar, parametros.opciones.CodigoFamilia));
            parametrosDeEntrada.Add(conexion.crearParametro("@IProducto", SqlDbType.VarChar, parametros.opciones.CodigoProducto));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaUnificadora", SqlDbType.VarChar, parametros.session_empresa_unificadora));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReportesInventario", parametrosDeEntrada);

            reporte.DataSource = dt;

            XRLabel labelRangoFechas = (XRLabel)reporte.FindControl("labelRangoFechas", false);
            labelRangoFechas.Text = "Del: " + parametros.opciones.FechaInicial + " Al: " + parametros.opciones.FechaFinal;

            XRLabel labelSucursalFamilia = (XRLabel)reporte.FindControl("labelSucursalFamilia", false);
            labelSucursalFamilia.Text = "Sucursal: " + parametros.opciones.NombreBodega + " Familia: " + parametros.opciones.NombreFamilia;

            XRLabel labelProducto = (XRLabel)reporte.FindControl("labelProducto", false);
            labelProducto.Text = "";

            return reporte;

        }

        public PsicotropicoUnProductoIntegrado ReportePsicotropicoUnProductoIntegrado(Reporte parametros)
        {
            PsicotropicoUnProductoIntegrado reporte = new PsicotropicoUnProductoIntegrado();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "9"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaInicial", SqlDbType.VarChar, parametros.opciones.FechaInicial));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaFinal", SqlDbType.VarChar, parametros.opciones.FechaFinal));
            parametrosDeEntrada.Add(conexion.crearParametro("@IBodega", SqlDbType.VarChar, parametros.opciones.CodigoBodega));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFamiliaProducto", SqlDbType.VarChar, parametros.opciones.CodigoFamilia));
            parametrosDeEntrada.Add(conexion.crearParametro("@IProducto", SqlDbType.VarChar, parametros.opciones.CodigoProducto));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaUnificadora", SqlDbType.VarChar, parametros.session_empresa_unificadora));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReportesInventario", parametrosDeEntrada);

            reporte.DataSource = dt;

            XRLabel labelFechas = (XRLabel)reporte.FindControl("labelFechas", false);
            labelFechas.Text = "Del: " + parametros.opciones.FechaInicial + " Al: " + parametros.opciones.FechaFinal;

            XRLabel labelSucursalFamilia = (XRLabel)reporte.FindControl("labelSucursalFamilia", false);
            labelSucursalFamilia.Text = "Sucursal: " + parametros.opciones.NombreBodega + " Familia: " + parametros.opciones.NombreFamilia;

            XRLabel labelProducto = (XRLabel)reporte.FindControl("labelProducto", false);
            labelProducto.Text = "Producto: " + parametros.opciones.NombreProducto;

            return reporte;

        }

        public PsicotropicoUnaFamiliaIntegrada ReportePsicotropicoUnaFamiliaIntegrada(Reporte parametros)
        {
            PsicotropicoUnaFamiliaIntegrada reporte = new PsicotropicoUnaFamiliaIntegrada();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "10"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaInicial", SqlDbType.VarChar, parametros.opciones.FechaInicial));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaFinal", SqlDbType.VarChar, parametros.opciones.FechaFinal));
            parametrosDeEntrada.Add(conexion.crearParametro("@IBodega", SqlDbType.VarChar, parametros.opciones.CodigoBodega));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFamiliaProducto", SqlDbType.VarChar, parametros.opciones.CodigoFamilia));
            parametrosDeEntrada.Add(conexion.crearParametro("@IProducto", SqlDbType.VarChar, parametros.opciones.CodigoProducto));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaUnificadora", SqlDbType.VarChar, parametros.session_empresa_unificadora));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReportesInventario", parametrosDeEntrada);

            reporte.DataSource = dt;

            XRLabel labelRangoFechas = (XRLabel)reporte.FindControl("labelRangoFechas", false);
            labelRangoFechas.Text = "Del: " + parametros.opciones.FechaInicial + " Al: " + parametros.opciones.FechaFinal;

            XRLabel labelSucursalFamilia = (XRLabel)reporte.FindControl("labelSucursalFamilia", false);
            labelSucursalFamilia.Text = "Sucursal: " + parametros.opciones.NombreBodega + " Familia: " + parametros.opciones.NombreFamilia;

            XRLabel labelProducto = (XRLabel)reporte.FindControl("labelProducto", false);
            labelProducto.Text = "";

            return reporte;

        }

        public BitacoraIngresosBodega ReporteBitacoraIngresosBodega(Reporte parametros)
        {
            BitacoraIngresosBodega reporte = new BitacoraIngresosBodega();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "11"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaInicial", SqlDbType.VarChar, parametros.opciones.FechaInicial));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaFinal", SqlDbType.VarChar, parametros.opciones.FechaFinal));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReportesInventario", parametrosDeEntrada);

            reporte.DataSource = dt;

            XRLabel labelFechas = (XRLabel)reporte.FindControl("labelFechas", false);
            labelFechas.Text = "Periodo: " + parametros.opciones.FechaInicial + " Al " + parametros.opciones.FechaFinal;

            return reporte;

        }

        public ExistenciaPorProducto ReporteExistenciaPorProducto(Reporte parametros)
        {
            ExistenciaPorProducto reporte = new ExistenciaPorProducto();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            string NombreEmpresa = "";
            string FechaActual = "";

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "13"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReportesInventario", parametrosDeEntrada);
            if (dt.Rows.Count > 0)
            {
                NombreEmpresa = dt.Rows[0]["Nombre"].ToString();
                FechaActual = dt.Rows[0]["FechaActual"].ToString();

            }

            dt.Clear();
            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "12"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@IBodega", SqlDbType.VarChar, parametros.opciones.CodigoBodega));
            parametrosDeEntrada.Add(conexion.crearParametro("@IProductoDel", SqlDbType.VarChar, parametros.opciones.ProductoDel));
            parametrosDeEntrada.Add(conexion.crearParametro("@IProductoAl", SqlDbType.VarChar, parametros.opciones.ProductoAl));
            parametrosDeEntrada.Add(conexion.crearParametro("@IDiferenteCero", SqlDbType.VarChar, parametros.opciones.Existencia));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReportesInventario", parametrosDeEntrada);

            reporte.DataSource = dt;

            XRLabel LabelEmpresa = (XRLabel)reporte.FindControl("labelEmpresa", false);
            LabelEmpresa.Text = NombreEmpresa;

            XRLabel labelUsuario = (XRLabel)reporte.FindControl("labelUsuario", false);
            labelUsuario.Text = parametros.session_id;

            XRLabel labelFecha = (XRLabel)reporte.FindControl("labelFecha", false);
            labelFecha.Text = FechaActual;

            if (parametros.opciones.Existencia.Equals("0"))
            {
                XRLabel labelBodegaExistencia = (XRLabel)reporte.FindControl("labelBodegaExistencia", false);
                labelBodegaExistencia.Text = "de la Bodega " + parametros.opciones.CodigoBodega;
            }
            else
            {
                XRLabel labelBodegaExistencia = (XRLabel)reporte.FindControl("labelBodegaExistencia", false);
                labelBodegaExistencia.Text = "de la Bodega " + parametros.opciones.CodigoBodega + ", diferentes a 0";
            }


            return reporte;

        }

        public MovimientosPorBodega ReporteMovimientosPorBodega(Reporte parametros)
        {
            MovimientosPorBodega reporte = new MovimientosPorBodega();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            string NombreEmpresa = "";
            string FechaActual = "";

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "13"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReportesInventario", parametrosDeEntrada);
            if (dt.Rows.Count > 0)
            {
                NombreEmpresa = dt.Rows[0]["Nombre"].ToString();
                FechaActual = dt.Rows[0]["FechaActual"].ToString();

            }

            dt.Clear();
            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "15"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaUnificadora", SqlDbType.VarChar, parametros.session_empresa_unificadora));
            parametrosDeEntrada.Add(conexion.crearParametro("@ICodigoMovimiento", SqlDbType.VarChar, parametros.opciones.CodigoMovimiento));
            parametrosDeEntrada.Add(conexion.crearParametro("@ITipoMovimiento", SqlDbType.VarChar, parametros.opciones.TipoMovimiento));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaInicial", SqlDbType.VarChar, parametros.opciones.FechaInicial));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaFinal", SqlDbType.VarChar, parametros.opciones.FechaFinal));
            parametrosDeEntrada.Add(conexion.crearParametro("@IBodegaFuente", SqlDbType.VarChar, parametros.opciones.BodegaFuente));
            parametrosDeEntrada.Add(conexion.crearParametro("@IBodegaDestino", SqlDbType.VarChar, parametros.opciones.BodegaDestino));
            parametrosDeEntrada.Add(conexion.crearParametro("@IOrdenar", SqlDbType.VarChar, parametros.opciones.OrdenarPor));
            parametrosDeEntrada.Add(conexion.crearParametro("@ICodigoSolicitud", SqlDbType.VarChar, parametros.opciones.CODIGO_SOLICITUD));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReportesInventario", parametrosDeEntrada);

            reporte.DataSource = dt;

            XRLabel LabelEmpresa = (XRLabel)reporte.FindControl("labelEmpresa", false);
            LabelEmpresa.Text = NombreEmpresa;

            XRLabel labelUsuario = (XRLabel)reporte.FindControl("labelUsuario", false);
            labelUsuario.Text = parametros.session_id;

            XRLabel labelFecha = (XRLabel)reporte.FindControl("labelFecha", false);
            labelFecha.Text = FechaActual;

            XRLabel labelGroupHeader = (XRLabel)reporte.FindControl("labelGroupHeader", false);
            labelGroupHeader.Text = "";

            if (parametros.opciones.CodigoMovimiento.Equals("") || parametros.opciones.CodigoMovimiento.Equals(null))
            {
                labelGroupHeader.Text = "Del " + parametros.opciones.FechaInicial + " al " + parametros.opciones.FechaFinal;

            }
            else
            {
                labelGroupHeader.Text = "";
            }
            if (!string.IsNullOrEmpty(parametros.opciones.BodegaFuente))
            {
                labelGroupHeader.Text = labelGroupHeader.Text + " Bodega Fuente " + parametros.opciones.BodegaFuente;
            }
            if (!string.IsNullOrEmpty(parametros.opciones.BodegaDestino))
            {
                labelGroupHeader.Text = labelGroupHeader.Text + " Bodega Destino " + parametros.opciones.BodegaDestino;
            }


            return reporte;

        }

        public MovimientoNotaCredito ReporteMovimientoNota(Reporte parametros)
        {
            try
            {
                MovimientoNotaCredito reporte = new MovimientoNotaCredito();
                DataTable dt = new DataTable();
                List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
                string NombreEmpresa = "";
                string FechaActual = "";
                string NombreSucursal = "";
                string Direccion = "";
                string Nit = "";

                parametrosDeEntrada.Clear();
                parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
                dt = conexion.getTableBySP("HOSPITAL", "SpHisParametrosReporteInventario", parametrosDeEntrada);
                if (dt.Rows.Count > 0)
                {
                    NombreEmpresa = dt.Rows[0]["Nombre"].ToString();
                    FechaActual = dt.Rows[0]["FechaActual"].ToString();

                }

                parametrosDeEntrada.Clear();
                parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "7"));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaSucursal", SqlDbType.VarChar, parametros.session_empresa_sucursal));
                dt = conexion.getTableBySP("HOSPITAL", "SpHisParametrosReporteInventario", parametrosDeEntrada);
                if (dt.Rows.Count > 0)
                {
                    NombreSucursal = dt.Rows[0]["Nombre"].ToString();
                    Direccion = dt.Rows[0]["Direccion"].ToString();
                    Nit = dt.Rows[0]["Nit"].ToString();
                }

                XRLabel LabelEmpresa = (XRLabel)reporte.FindControl("labelEmpresa", false);
                LabelEmpresa.Text = NombreEmpresa;

                XRLabel labelFecha = (XRLabel)reporte.FindControl("labelFecha", false);
                labelFecha.Text = FechaActual;

                XRLabel labelEmprsaSucursal = (XRLabel)reporte.FindControl("labelEmprsaSucursal", false);
                labelEmprsaSucursal.Text = NombreSucursal;

                XRLabel labelNitEmpresa = (XRLabel)reporte.FindControl("labelNitEmpresa", false);
                labelNitEmpresa.Text = Nit;

                parametrosDeEntrada.Clear();
                parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "8"));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
                parametrosDeEntrada.Add(conexion.crearParametro("@INoNota", SqlDbType.VarChar, parametros.opciones.CodigoNota));
                dt = conexion.getTableBySP("HOSPITAL", "SpHisParametrosReporteInventario", parametrosDeEntrada);

                if (dt.Rows.Count > 0)
                {
                    string CodigoNota = dt.Rows[0]["Codigo"].ToString().Trim();
                    string FechaNC = dt.Rows[0]["FechaNC"].ToString().Trim();
                    string Fecha = dt.Rows[0]["Fecha"].ToString().Trim();
                    string Documento = dt.Rows[0]["Documento"].ToString().Trim();
                    string NCFase = dt.Rows[0]["NCFase"].ToString().Trim();
                    string Bodega = dt.Rows[0]["Bodega"].ToString().Trim();
                    string CodigoProveedor = dt.Rows[0]["Proveedor"].ToString().Trim();
                    string NombreProveedor = dt.Rows[0]["Nombre"].ToString().Trim();
                    string Total = dt.Rows[0]["Total"].ToString().Trim();
                    string Factura = dt.Rows[0]["Factura"].ToString().Trim();
                    string RebajaExistencias = dt.Rows[0]["RebajaExistencias"].ToString().Trim();
                    string ExentoIVA = dt.Rows[0]["ExentoIVA"].ToString().Trim();
                    string Observaciones = dt.Rows[0]["Observaciones"].ToString().Trim();
                    string Validacion = dt.Rows[0]["Validacion"].ToString().Trim();
                    string NombreEmpleado = dt.Rows[0]["NombreEmpleado"].ToString().Trim();
                    string TipoMovimiento = dt.Rows[0]["TipoMovimiento"].ToString().Trim();
                    string NombreTipo = dt.Rows[0]["NombreTipo"].ToString().Trim();

                    XRLabel labelCorrelativo = (XRLabel)reporte.FindControl("labelCorrelativo", false);
                    labelCorrelativo.Text = CodigoNota;

                    XRLabel labelFechaRegistro = (XRLabel)reporte.FindControl("labelFechaRegistro", false);
                    labelFechaRegistro.Text = FechaNC;

                    XRLabel labelNumeroNota = (XRLabel)reporte.FindControl("labelNumeroNota", false);
                    labelNumeroNota.Text = Documento;

                    XRLabel labelBodega = (XRLabel)reporte.FindControl("labelBodega", false);
                    labelBodega.Text = Bodega;

                    XRLabel labelProveedor = (XRLabel)reporte.FindControl("labelProveedor", false);
                    labelProveedor.Text = CodigoProveedor + " - " + NombreProveedor;

                    XRLabel labelValorNota = (XRLabel)reporte.FindControl("labelValorNota", false);
                    labelValorNota.Text = Convert.ToDouble(Total).ToString("c2");

                    XRLabel labelFactura = (XRLabel)reporte.FindControl("labelFactura", false);
                    labelFactura.Text = Factura;

                    XRLabel labelRebaja = (XRLabel)reporte.FindControl("labelRebaja", false);
                    labelRebaja.Text = RebajaExistencias;

                    if (TipoMovimiento.Equals("23"))
                    {
                        XRLabel labelFechaIngreso = (XRLabel)reporte.FindControl("labelFechaIngreso", false);
                        labelFechaIngreso.Text = Fecha;

                        XRLabel labelTextNota = (XRLabel)reporte.FindControl("labelTextNota", false);
                        labelTextNota.Text = "No. NC:";

                        XRLabel labelNFace = (XRLabel)reporte.FindControl("labelNFace", false);
                        labelNFace.Text = NCFase;

                        XRLabel labelExento = (XRLabel)reporte.FindControl("labelExento", false);
                        labelExento.Text = ExentoIVA;
                    }
                    else
                    {
                        XRLabel labelTextFechaIngreso = (XRLabel)reporte.FindControl("labelTextFechaIngreso", false);
                        labelTextFechaIngreso.Text = "";

                        XRLabel labelTextNota = (XRLabel)reporte.FindControl("labelTextNota", false);
                        labelTextNota.Text = "No. Nabono:";

                        XRLabel labelTextNface = (XRLabel)reporte.FindControl("labelTextNface", false);
                        labelTextNface.Text = "";

                        XRLabel labelTextoExento = (XRLabel)reporte.FindControl("labelTextoExento", false);
                        labelTextoExento.Text = "";

                    }
                    XRLabel labelMotivo = (XRLabel)reporte.FindControl("labelMotivo", false);
                    labelMotivo.Text = Observaciones;

                    XRLabel labelValidacion = (XRLabel)reporte.FindControl("labelValidacion", false);
                    labelValidacion.Text = Validacion;

                    XRLabel labelNombreEmpleado = (XRLabel)reporte.FindControl("labelNombreEmpleado", false);
                    labelNombreEmpleado.Text = NombreEmpleado;

                    XRLabel labelNombeMovimiento = (XRLabel)reporte.FindControl("labelNombeMovimiento", false);
                    labelNombeMovimiento.Text = NombreTipo;
                }

                parametrosDeEntrada.Clear();
                parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
                parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "9"));
                parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
                parametrosDeEntrada.Add(conexion.crearParametro("@INoNota", SqlDbType.VarChar, parametros.opciones.CodigoNota));
                dt = conexion.getTableBySP("HOSPITAL", "SpHisParametrosReporteInventario", parametrosDeEntrada);

                reporte.DataSource = dt;
                
                return reporte;
            }
            catch (Exception ex)
            {
                throw ex;
            }


        }

        public PedidoConsignacion ReportePedidoConsignacion(Reporte parametros)
        {
            PedidoConsignacion reporte = new PedidoConsignacion();
            DataTable dt = new DataTable();
            DataTable dtDetalle = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
            List<SqlParameter> parametrosDeEntradaDetalle = new List<SqlParameter>();
            string NombreEmpresa = "";
            string FechaActual = "";
            string NitEmpresa = "";

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaSucursal", SqlDbType.VarChar, parametros.session_empresa_sucursal));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);

            if (dt.Rows.Count > 0)
            {
                NombreEmpresa = dt.Rows[0]["Nombre"].ToString();
                FechaActual = dt.Rows[0]["FechaActual"].ToString();
                NitEmpresa = dt.Rows[0]["Nit"].ToString();

                XRLabel labelFecha = (XRLabel)reporte.FindControl("labelFecha", false);
                labelFecha.Text = FechaActual;

                XRLabel labelEmpresa = (XRLabel)reporte.FindControl("labelNombeEmpresa", false);
                labelEmpresa.Text = NombreEmpresa;

                XRLabel labelNit = (XRLabel)reporte.FindControl("labelNitEmpresa", false);
                labelNit.Text = NitEmpresa;

            }

            dt.Clear();
            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "2"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@IPedido", SqlDbType.VarChar, parametros.opciones.Pedido));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);

            if (dt.Rows.Count > 0)
            {
                string CodigoPedido = dt.Rows[0]["Codigo"].ToString().Trim();
                string Proveedor = dt.Rows[0]["Nombre"].ToString().Trim();
                string SolicitadoPor = dt.Rows[0]["NombreEmpleado"].ToString().Trim();
                string FechaEntrega = dt.Rows[0]["FechaEntrega"].ToString().Trim();
                string FechaRecepcion = dt.Rows[0]["Fecha"].ToString().Trim();
                string LugarEntrega = dt.Rows[0]["LugarEntrega"].ToString().Trim();
                string Contacto = dt.Rows[0]["ContactoProveedor"].ToString().Trim();
                string TelContacto = dt.Rows[0]["TelContacto"].ToString().Trim();
                string Observacion = dt.Rows[0]["Descripcion"].ToString().Trim();
                string Tipo = dt.Rows[0]["Tipo"].ToString().Trim();

                XRLabel labelPedido = (XRLabel)reporte.FindControl("labelCodigo", false);
                labelPedido.Text = CodigoPedido;

                XRLabel labelProveedor = (XRLabel)reporte.FindControl("labelNombre", false);
                labelProveedor.Text = Proveedor;

                XRLabel labelSolicitadoPor = (XRLabel)reporte.FindControl("labelNombreEmpleado", false);
                labelSolicitadoPor.Text = SolicitadoPor;

                XRLabel labelFechaEntrega = (XRLabel)reporte.FindControl("labelFechaEntrega", false);
                labelFechaEntrega.Text = FechaEntrega;

                XRLabel labelFechaRecepcion = (XRLabel)reporte.FindControl("labelFechaRecepcion", false);
                labelFechaRecepcion.Text = FechaRecepcion;

                XRLabel labelLugarEntrega = (XRLabel)reporte.FindControl("labelLugarEntrega", false);
                labelLugarEntrega.Text = LugarEntrega;

                XRLabel labelContacto = (XRLabel)reporte.FindControl("labelContactoProveedor", false);
                labelContacto.Text = Contacto;

                XRLabel labelTelContacto = (XRLabel)reporte.FindControl("labelTelContacto", false);
                labelTelContacto.Text = TelContacto;

                XRLabel labelObservacion = (XRLabel)reporte.FindControl("labelDescripcion", false);
                labelObservacion.Text = Observacion;

                XRLabel labelTituloReporte = (XRLabel)reporte.FindControl("labelTituloReporte", false);
                labelTituloReporte.Text = Tipo == "O" ? "Pedido en Consignación Ortopedia" : "Pedido de Consignación";
            }

            dtDetalle.Clear();
            parametrosDeEntradaDetalle.Clear();
            parametrosDeEntradaDetalle.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntradaDetalle.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "3"));
            parametrosDeEntradaDetalle.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntradaDetalle.Add(conexion.crearParametro("@IPedido", SqlDbType.VarChar, parametros.opciones.Pedido));
            dtDetalle = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntradaDetalle);

            reporte.DataSource = dtDetalle;

            return reporte;
        }

        public EnvioConsignacion ReporteEnvioConsignacion(Reporte parametros)
        {
            EnvioConsignacion reporte = new EnvioConsignacion();
            DataTable dt = new DataTable();
            DataTable dtDetalle = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
            List<SqlParameter> parametrosDeEntradaDetalle = new List<SqlParameter>();
            string NombreEmpresa = "";
            string FechaActual = "";
            string NitEmpresa = "";

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaSucursal", SqlDbType.VarChar, parametros.session_empresa_sucursal));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);

            if (dt.Rows.Count > 0)
            {
                NombreEmpresa = dt.Rows[0]["Nombre"].ToString();
                FechaActual = dt.Rows[0]["FechaActual"].ToString();
                NitEmpresa = dt.Rows[0]["Nit"].ToString();

                XRLabel labelFecha = (XRLabel)reporte.FindControl("labelFecha", false);
                labelFecha.Text = FechaActual;

                XRLabel labelEmpresa = (XRLabel)reporte.FindControl("labelNombeEmpresa", false);
                labelEmpresa.Text = NombreEmpresa;

                XRLabel labelNit = (XRLabel)reporte.FindControl("labelNitEmpresa", false);
                labelNit.Text = NitEmpresa;

            }

            dt.Clear();
            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "4"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@INoEnvio", SqlDbType.VarChar, parametros.opciones.Envio));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);

            if (dt.Rows.Count > 0)
            {
                string Correlativo = dt.Rows[0]["Codigo"].ToString().Trim();
                string Proveedor = dt.Rows[0]["Proveedor"].ToString().Trim();
                string NombreProveedor = dt.Rows[0]["NombreProveedor"].ToString().Trim();
                string Direccion = dt.Rows[0]["Direccion"].ToString().Trim();
                string Nit = dt.Rows[0]["Nit"].ToString().Trim();
                string Telefonos = dt.Rows[0]["Telefonos"].ToString().Trim();
                string Documento = dt.Rows[0]["Documento"].ToString().Trim();
                string FechaEnvio = dt.Rows[0]["FechaEnvio"].ToString().Trim();
                string BodegaDestino = dt.Rows[0]["BodegaDestino"].ToString().Trim();
                string NombreDestino = dt.Rows[0]["Nombre"].ToString().Trim();
                string Validacion = dt.Rows[0]["Validacion"].ToString().Trim();
                string Fecha = dt.Rows[0]["Fecha"].ToString().Trim();
                string Usuario = dt.Rows[0]["Usuario"].ToString().Trim();
                string Empleado = dt.Rows[0]["Empleado"].ToString().Trim();
                string Motivo = dt.Rows[0]["Descripcion"].ToString().Trim();
                string Pedido = dt.Rows[0]["Pedido"].ToString().Trim();
                string RecibeEmpresa = dt.Rows[0]["RecibeEmpresa"].ToString().Trim();
                string Estado = dt.Rows[0]["Estado"].ToString().Trim();
                string Tipo = dt.Rows[0]["Tipo"].ToString().Trim();

                XRLabel labelCorrelativo = (XRLabel)reporte.FindControl("labelCorrelativo", false);
                labelCorrelativo.Text = "Correlativo No. " + Correlativo;

                XRLabel labelProveedor = (XRLabel)reporte.FindControl("labelProveedor", false);
                labelProveedor.Text = Proveedor + " - " + NombreProveedor;

                XRLabel labelDireccion = (XRLabel)reporte.FindControl("labelDireccion", false);
                labelDireccion.Text = Direccion;

                XRLabel labelNitProveedor = (XRLabel)reporte.FindControl("labelNitProveedor", false);
                labelNitProveedor.Text = Nit;

                XRLabel labelTelProveedor = (XRLabel)reporte.FindControl("labelTelProveedor", false);
                labelTelProveedor.Text = Telefonos;

                XRLabel labelNoEnvio = (XRLabel)reporte.FindControl("labelNoEnvio", false);
                labelNoEnvio.Text = Tipo == "O" ? "OR-" + Documento : Documento;

                XRLabel labelFechaEnvio = (XRLabel)reporte.FindControl("labelFechaEnvio", false);
                labelFechaEnvio.Text = FechaEnvio;

                XRLabel labelLugarEntrega = (XRLabel)reporte.FindControl("labelLugarEntrega", false);
                labelLugarEntrega.Text = BodegaDestino + " - " + NombreDestino;

                XRLabel labelNoMovimiento = (XRLabel)reporte.FindControl("labelNoMovimiento", false);
                labelNoMovimiento.Text = Validacion;

                XRLabel labelFechaIngreso = (XRLabel)reporte.FindControl("labelFechaIngreso", false);
                labelFechaIngreso.Text = Fecha;

                XRLabel labelRecibidoPor = (XRLabel)reporte.FindControl("labelRecibidoPor", false);
                labelRecibidoPor.Text = Usuario + " " + Empleado;

                XRLabel labelMotivo = (XRLabel)reporte.FindControl("labelMotivo", false);
                labelMotivo.Text = Motivo;

                XRLabel labelNoPedido = (XRLabel)reporte.FindControl("labelNoPedido", false);
                labelNoPedido.Text = Pedido;

                XRLabel labelSucursal = (XRLabel)reporte.FindControl("labelSucursal", false);
                labelSucursal.Text = RecibeEmpresa;

                XRLabel labelEstado = (XRLabel)reporte.FindControl("labelEstado", false);
                labelEstado.Text = Estado;

                XRLabel labelRecibido = (XRLabel)reporte.FindControl("labelRecibido", false);
                labelRecibido.Text = Empleado;

                XRLabel labelTituloReporte = (XRLabel)reporte.FindControl("labelTituloReporte", false);
                labelTituloReporte.Text = Tipo == "O" ? "Envío en Consignación Ortopedia" : "Envío de Consignación";
            }

            dtDetalle.Clear();
            parametrosDeEntradaDetalle.Clear();
            parametrosDeEntradaDetalle.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntradaDetalle.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "5"));
            parametrosDeEntradaDetalle.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntradaDetalle.Add(conexion.crearParametro("@INoEnvio", SqlDbType.VarChar, parametros.opciones.Envio));
            dtDetalle = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntradaDetalle);

            reporte.DataSource = dtDetalle;

            return reporte;
        }

        public DevolucionConsignacion ReporteDevolucionConsignacion(Reporte parametros)
        {
            DevolucionConsignacion reporte = new DevolucionConsignacion();
            DataTable dt = new DataTable();
            DataTable dtDetalle = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
            List<SqlParameter> parametrosDeEntradaDetalle = new List<SqlParameter>();
            string NombreEmpresa = "";
            string FechaActual = "";
            string NitEmpresa = "";

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaSucursal", SqlDbType.VarChar, parametros.session_empresa_sucursal));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);

            if (dt.Rows.Count > 0)
            {
                NombreEmpresa = dt.Rows[0]["Nombre"].ToString();
                FechaActual = dt.Rows[0]["FechaActual"].ToString();
                NitEmpresa = dt.Rows[0]["Nit"].ToString();

                XRLabel labelFecha = (XRLabel)reporte.FindControl("labelFecha", false);
                labelFecha.Text = FechaActual;

                XRLabel labelEmpresa = (XRLabel)reporte.FindControl("labelNombeEmpresa", false);
                labelEmpresa.Text = NombreEmpresa;

                XRLabel labelNit = (XRLabel)reporte.FindControl("labelNitEmpresa", false);
                labelNit.Text = NitEmpresa;

            }

            dt.Clear();
            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "6"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@INoDevolucion", SqlDbType.VarChar, parametros.opciones.Devolucion));
            parametrosDeEntrada.Add(conexion.crearParametro("@IProveedor", SqlDbType.VarChar, parametros.opciones.Proveedor));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);

            if (dt.Rows.Count > 0)
            {
                string CodigoDevolucion = dt.Rows[0]["Codigo"].ToString().Trim();
                string CodProveedor = dt.Rows[0]["Proveedor"].ToString().Trim();
                string Proveedor = dt.Rows[0]["Nombre"].ToString().Trim();
                string Direccion = dt.Rows[0]["Direccion"].ToString().Trim();
                string NitProveedor = dt.Rows[0]["Nit"].ToString().Trim();
                string TelProveedor = dt.Rows[0]["Telefonos"].ToString().Trim();
                string NombreBodega = dt.Rows[0]["NombreBodega"].ToString().Trim();
                string Validacion = dt.Rows[0]["Validacion"].ToString().Trim();
                string EntregadoA = dt.Rows[0]["Recibe"].ToString().Trim();
                string Observacion = dt.Rows[0]["Observacion"].ToString().Trim();
                string Usuario = dt.Rows[0]["Usuario"].ToString().Trim();
                string Fecha = dt.Rows[0]["Fecha"].ToString().Trim();
                string Pedido = dt.Rows[0]["Pedido"].ToString().Trim();
                string Descripcion = dt.Rows[0]["Descripcion"].ToString().Trim();
                string Entrega = dt.Rows[0]["Entrega"].ToString().Trim();
                string Estado = dt.Rows[0]["Estado"].ToString().Trim();

                XRLabel labelCodigoDevolucion = (XRLabel)reporte.FindControl("labelCodigoDevolucion", false);
                labelCodigoDevolucion.Text = "Correlativo No. " + CodigoDevolucion;

                XRLabel labelProveedor = (XRLabel)reporte.FindControl("labelProveedor", false);
                labelProveedor.Text = CodProveedor + " - " + Proveedor;

                XRLabel labelDireccion = (XRLabel)reporte.FindControl("labelDireccion", false);
                labelDireccion.Text = Direccion;

                XRLabel labelNitProveedor = (XRLabel)reporte.FindControl("labelNitProveedor", false);
                labelNitProveedor.Text = NitProveedor;

                XRLabel labelTelProveedor = (XRLabel)reporte.FindControl("labelTelProveedor", false);
                labelTelProveedor.Text = TelProveedor;

                XRLabel labelNombreBodega = (XRLabel)reporte.FindControl("labelNombreBodega", false);
                labelNombreBodega.Text = NombreBodega;

                XRLabel labelValidacion = (XRLabel)reporte.FindControl("labelValidacion", false);
                labelValidacion.Text = Validacion;

                XRLabel labelEntreadoA = (XRLabel)reporte.FindControl("labelEntreadoA", false);
                labelEntreadoA.Text = EntregadoA;

                XRLabel labelObservacion = (XRLabel)reporte.FindControl("labelObservacion", false);
                labelObservacion.Text = Observacion;

                XRLabel labelUsuario = (XRLabel)reporte.FindControl("labelUsuario", false);
                labelUsuario.Text = Usuario;

                XRLabel labelFechaEntrega = (XRLabel)reporte.FindControl("labelFechaEntrega", false);
                labelFechaEntrega.Text = Fecha;

                XRLabel labelPedido = (XRLabel)reporte.FindControl("labelPedido", false);
                labelPedido.Text = Pedido;

                XRLabel labelDescripcion = (XRLabel)reporte.FindControl("labelDescripcion", false);
                labelDescripcion.Text = Descripcion;

                XRLabel labelEntrega = (XRLabel)reporte.FindControl("labelEntrega", false);
                labelEntrega.Text = Entrega;

                XRLabel labelRecibe = (XRLabel)reporte.FindControl("labelRecibe", false);
                labelRecibe.Text = EntregadoA;

                XRLabel labelEstado = (XRLabel)reporte.FindControl("labelEstado", false);
                labelEstado.Text = Estado;

                XRBarCode barCodeCorrelativo = (XRBarCode)reporte.FindControl("barCodeCorrelativo", false);
                barCodeCorrelativo.Text = parametros.opciones.Devolucion;
            }

            dtDetalle.Clear();
            parametrosDeEntradaDetalle.Clear();
            parametrosDeEntradaDetalle.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntradaDetalle.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "7"));
            parametrosDeEntradaDetalle.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntradaDetalle.Add(conexion.crearParametro("@INoDevolucion", SqlDbType.VarChar, parametros.opciones.Devolucion));
            dtDetalle = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntradaDetalle);

            reporte.DataSource = dtDetalle;

            return reporte;
        }

        public DevolucionProveedorConsignacion ReporteDevolucionProveedorConsignacion(Reporte parametros)
        {
            DevolucionProveedorConsignacion reporte = new DevolucionProveedorConsignacion();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "8"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaInicio", SqlDbType.VarChar, parametros.opciones.FechaInicial));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaFin", SqlDbType.VarChar, parametros.opciones.FechaFinal));
            parametrosDeEntrada.Add(conexion.crearParametro("@IProveedor", SqlDbType.VarChar, parametros.opciones.Proveedor));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEstado", SqlDbType.VarChar, parametros.opciones.Estado));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);

            reporte.DataSource = dt;

            XRLabel labelFechas = (XRLabel)reporte.FindControl("labelFechas", false);
            labelFechas.Text = "Periodo: " + parametros.opciones.FechaInicial + " al " + parametros.opciones.FechaFinal;

            XRLabel labelProveedor = (XRLabel)reporte.FindControl("labelProveedor", false);
            labelProveedor.Text = parametros.opciones.Proveedor != "" ? "Provedor: " + parametros.opciones.NombreProveedor : "Proveedor: Todos los Proveedores";

            return reporte;

        }

        public CargosProductosConsignacionExcel ReporteCargoProductoConsignacionExcel(Reporte parametros)
        {
            CargosProductosConsignacionExcel reporte = new CargosProductosConsignacionExcel();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "9"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaUnificadora", System.Data.SqlDbType.VarChar, parametros.session_empresa_unificadora));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaInicio", SqlDbType.VarChar, parametros.opciones.FechaInicial));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaFin", SqlDbType.VarChar, parametros.opciones.FechaFinal));
            parametrosDeEntrada.Add(conexion.crearParametro("@IProveedor", System.Data.SqlDbType.VarChar, parametros.opciones.Proveedor));
            parametrosDeEntrada.Add(conexion.crearParametro("@IProducto", System.Data.SqlDbType.VarChar, parametros.opciones.Producto));
            parametrosDeEntrada.Add(conexion.crearParametro("@IBodega", System.Data.SqlDbType.VarChar, parametros.opciones.CodigoBodega));
            parametrosDeEntrada.Add(conexion.crearParametro("@ILiquidacion", System.Data.SqlDbType.VarChar, parametros.opciones.Liquidacion));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEstado", System.Data.SqlDbType.VarChar, parametros.opciones.Estado));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);

            reporte.DataSource = dt;

            XRLabel labelFechas = (XRLabel)reporte.FindControl("labelFechas", false);
            labelFechas.Text = "Periodo: " + parametros.opciones.FechaInicial + " al " + parametros.opciones.FechaFinal;

            XRLabel labelBodega = (XRLabel)reporte.FindControl("labelBodega", false);
            labelBodega.Text = parametros.opciones.NombreBodega != "" ? parametros.opciones.NombreBodega : "Todas las Bodegas";

            XRLabel labelProveedor = (XRLabel)reporte.FindControl("labelProveedor", false);
            labelProveedor.Text = parametros.opciones.Proveedor != "" ? "Provedor: " + parametros.opciones.NombreProveedor : "Proveedor: Todos los Proveedores";

            XRLabel labelTipo = (XRLabel)reporte.FindControl("labelTipo", false);
            labelTipo.Text = parametros.opciones.Estado == "0" ? "Medicamentos" : parametros.opciones.Estado == "1" ? "Suministros": "Medicamentos y Suministros";

            return reporte;

        }

        public CargosProductosConsignacionPDF ReporteCargoProductoConsignacionPDF(Reporte parametros)
        {
            CargosProductosConsignacionPDF reporte = new CargosProductosConsignacionPDF();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
            string NombreEmpresa = "";
            string FechaActual = "";
            string NitEmpresa = "";

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaSucursal", SqlDbType.VarChar, parametros.session_empresa_sucursal));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);

            if (dt.Rows.Count > 0)
            {
                NombreEmpresa = dt.Rows[0]["Nombre"].ToString();
                FechaActual = dt.Rows[0]["FechaActual"].ToString();
                NitEmpresa = dt.Rows[0]["Nit"].ToString();

                XRLabel labelEmpresa = (XRLabel)reporte.FindControl("labelNombeEmpresa", false);
                labelEmpresa.Text = NombreEmpresa;

                XRLabel labelNit = (XRLabel)reporte.FindControl("labelNitEmpresa", false);
                labelNit.Text = NitEmpresa;

            }

            dt.Clear();
            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "9"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaUnificadora", System.Data.SqlDbType.VarChar, parametros.session_empresa_unificadora));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaInicio", SqlDbType.VarChar, parametros.opciones.FechaInicial));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaFin", SqlDbType.VarChar, parametros.opciones.FechaFinal));
            parametrosDeEntrada.Add(conexion.crearParametro("@IProveedor", System.Data.SqlDbType.VarChar, parametros.opciones.Proveedor));
            parametrosDeEntrada.Add(conexion.crearParametro("@IProducto", System.Data.SqlDbType.VarChar, parametros.opciones.Producto));
            parametrosDeEntrada.Add(conexion.crearParametro("@IBodega", System.Data.SqlDbType.VarChar, parametros.opciones.CodigoBodega));
            parametrosDeEntrada.Add(conexion.crearParametro("@ILiquidacion", System.Data.SqlDbType.VarChar, parametros.opciones.Liquidacion));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEstado", System.Data.SqlDbType.VarChar, parametros.opciones.Estado));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);

            reporte.DataSource = dt;

            XRLabel labelPeriodo = (XRLabel)reporte.FindControl("labelPeriodo", false);
            labelPeriodo.Text = "Periodo: Del " + parametros.opciones.FechaInicial + " al " + parametros.opciones.FechaFinal;

            XRLabel labelEstado = (XRLabel)reporte.FindControl("labelEstado", false);
            labelEstado.Text = parametros.opciones.Estado == "0" ? "Sin Liquidacion": parametros.opciones.Estado == "1" ? "Con Liquidacion" : "Ambos";

            XRLabel labelProveedor = (XRLabel)reporte.FindControl("labelProveedor", false);
            labelProveedor.Text = parametros.opciones.Proveedor != "" ? "Provedor: " + parametros.opciones.NombreProveedor : "Proveedor: Todos los Proveedores";

            XRLabel labelTipo = (XRLabel)reporte.FindControl("labelTipo", false);
            labelTipo.Text = parametros.opciones.Estado == "0" ? "Medicamentos" : parametros.opciones.Estado == "1" ? "Suministros" : "Medicamentos y Suministros";

            return reporte;

        }

        public LiquidacionesExcel ReporteLiquidacionesExcel(Reporte parametros)
        {
            LiquidacionesExcel reporte = new LiquidacionesExcel();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "10"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaInicio", SqlDbType.VarChar, parametros.opciones.FechaInicial));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaFin", SqlDbType.VarChar, parametros.opciones.FechaFinal));

            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);

            reporte.DataSource = dt;

            XRLabel labelFechas = (XRLabel)reporte.FindControl("labelFechas", false);
            labelFechas.Text = "Del " + parametros.opciones.FechaInicial + " al " + parametros.opciones.FechaFinal;

            return reporte;

        }

        public LiquidacionDetalle ReporteLiquidacionDetalle(Reporte parametros)
        {
            LiquidacionDetalle reporte = new LiquidacionDetalle();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
            string NombreEmpresa = "";
            string FechaActual = "";
            string NitEmpresa = "";

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaSucursal", SqlDbType.VarChar, parametros.session_empresa_sucursal));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);

            if (dt.Rows.Count > 0)
            {
                NombreEmpresa = dt.Rows[0]["Nombre"].ToString();
                FechaActual = dt.Rows[0]["FechaActual"].ToString();
                NitEmpresa = dt.Rows[0]["Nit"].ToString();

                XRLabel labelEmpresa = (XRLabel)reporte.FindControl("labelNombeEmpresa", false);
                labelEmpresa.Text = NombreEmpresa;

                XRLabel labelNit = (XRLabel)reporte.FindControl("labelNitEmpresa", false);
                labelNit.Text = NitEmpresa;

            }

            dt.Clear();
            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "11"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaUnificadora", System.Data.SqlDbType.VarChar, parametros.session_empresa_unificadora));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@CodLiquidacion", SqlDbType.VarChar, parametros.opciones.CodigoLiquidacion));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);


            reporte.DataSource = dt;

            XRLabel labelTitulo = (XRLabel)reporte.FindControl("labelEmpresa", false);
            labelTitulo.Text = parametros.opciones.TipoLiquidacion == "O" ? "Reporte Detallado de Consumos de Productos en Consignación Ortopedia"
                                                                                : "Reporte Detallado de Consumos de Productos en Consignación";

            XRLabel labelNoLiquidacion = (XRLabel)reporte.FindControl("labelNoLiquidacion", false);
            labelNoLiquidacion.Text = parametros.opciones.TipoLiquidacion == "O" ? "Liquidación Ortopedia No. " + parametros.opciones.CodigoLiquidacion
                                                                                : "Liquidación No. " + parametros.opciones.CodigoLiquidacion;

            XRLabel labelProveedor = (XRLabel)reporte.FindControl("labelProveedor", false);
            labelProveedor.Text = parametros.opciones.Proveedor + " - " + parametros.opciones.NombreProveedor;

            XRLabel labelOrdenCompra = (XRLabel)reporte.FindControl("labelOrdenCompra", false);
            labelOrdenCompra.Text = parametros.opciones.OrdenCompra;

            XRLabel labelFactura = (XRLabel)reporte.FindControl("labelFactura", false);
            labelFactura.Text = parametros.opciones.Factura;

            XRLabel labelEstado = (XRLabel)reporte.FindControl("labelEstado", false);
            labelEstado.Text = parametros.opciones.Estado;

            XRLabel labelNitProveedor = (XRLabel)reporte.FindControl("labelNitProveedor", false);
            labelNitProveedor.Text = parametros.opciones.NitProveedor;

            XRLabel labelFechaLiquidacion = (XRLabel)reporte.FindControl("labelFechaLiquidacion", false);
            labelFechaLiquidacion.Text = parametros.opciones.FechaLiquidacion;

            return reporte;

        }

        public LiquidacionCostoUltimo ReporteLiquidacionCostoUltimo(Reporte parametros)
        {
            LiquidacionCostoUltimo reporte = new LiquidacionCostoUltimo();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
            string NombreEmpresa = "";
            string FechaActual = "";
            string NitEmpresa = "";

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaSucursal", SqlDbType.VarChar, parametros.session_empresa_sucursal));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);

            if (dt.Rows.Count > 0)
            {
                NombreEmpresa = dt.Rows[0]["Nombre"].ToString();
                FechaActual = dt.Rows[0]["FechaActual"].ToString();
                NitEmpresa = dt.Rows[0]["Nit"].ToString();

                XRLabel labelEmpresa = (XRLabel)reporte.FindControl("labelNombeEmpresa", false);
                labelEmpresa.Text = NombreEmpresa;

                XRLabel labelNit = (XRLabel)reporte.FindControl("labelNitEmpresa", false);
                labelNit.Text = NitEmpresa;

            }

            dt.Clear();
            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "12"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaUnificadora", System.Data.SqlDbType.VarChar, parametros.session_empresa_unificadora));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@CodLiquidacion", SqlDbType.VarChar, parametros.opciones.CodigoLiquidacion));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);


            reporte.DataSource = dt;

            XRLabel labelNoLiquidacion = (XRLabel)reporte.FindControl("labelNoLiquidacion", false);
            labelNoLiquidacion.Text = "Liquidación No. " + parametros.opciones.CodigoLiquidacion;

            XRLabel labelProveedor = (XRLabel)reporte.FindControl("labelProveedor", false);
            labelProveedor.Text = parametros.opciones.Proveedor + " - " + parametros.opciones.NombreProveedor;

            XRLabel labelOrdenCompra = (XRLabel)reporte.FindControl("labelOrdenCompra", false);
            labelOrdenCompra.Text = parametros.opciones.OrdenCompra;

            XRLabel labelFactura = (XRLabel)reporte.FindControl("labelFactura", false);
            labelFactura.Text = parametros.opciones.Factura;

            XRLabel labelEstado = (XRLabel)reporte.FindControl("labelEstado", false);
            labelEstado.Text = parametros.opciones.Estado;

            XRLabel labelNitProveedor = (XRLabel)reporte.FindControl("labelNitProveedor", false);
            labelNitProveedor.Text = parametros.opciones.NitProveedor;

            XRLabel labelFechaLiquidacion = (XRLabel)reporte.FindControl("labelFechaLiquidacion", false);
            labelFechaLiquidacion.Text = parametros.opciones.FechaLiquidacion;

            return reporte;

        }

        public LiquidacionDetalleExcel ReporteLiquidacionDetalleExcel(Reporte parametros)
        {
            LiquidacionDetalleExcel reporte = new LiquidacionDetalleExcel();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "11"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaUnificadora", System.Data.SqlDbType.VarChar, parametros.session_empresa_unificadora));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@CodLiquidacion", SqlDbType.VarChar, parametros.opciones.CodigoLiquidacion));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);


            reporte.DataSource = dt;

            XRLabel labelLiquidacion = (XRLabel)reporte.FindControl("labelLiquidacion", false);
            labelLiquidacion.Text = "Liquidación: " + parametros.opciones.CodigoLiquidacion;

            XRLabel labelProveedor = (XRLabel)reporte.FindControl("labelProveedor", false);
            labelProveedor.Text = "Proveedor: " + parametros.opciones.NombreProveedor;

            return reporte;

        }

        public ConveniosProveedores ReporteConveniosProveedores(Reporte parametros)
        {
            ConveniosProveedores reporte = new ConveniosProveedores();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "13"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", System.Data.SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@IBusqueda", SqlDbType.VarChar, parametros.opciones.Tipo));
            parametrosDeEntrada.Add(conexion.crearParametro("@IParametro", SqlDbType.VarChar, parametros.opciones.Parametro));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);


            reporte.DataSource = dt;

            return reporte;

        }

        public ProductosSinConvenioProveedor ReporteProductosSinConvenioProveedor(Reporte parametros)
        {
            ProductosSinConvenioProveedor reporte = new ProductosSinConvenioProveedor();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "14"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", System.Data.SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@IProveedor", SqlDbType.VarChar, parametros.opciones.Proveedor));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);

            XRLabel labelProveedor = (XRLabel)reporte.FindControl("labelProveedor", false);
            labelProveedor.Text = "Proveedor: " + parametros.opciones.NombreProveedor;

            reporte.DataSource = dt;

            return reporte;

        }

        public ConveniosInactivos ReporteConveniosInactivos(Reporte parametros)
        {
            ConveniosInactivos reporte = new ConveniosInactivos();
            DataTable dt = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "15"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", System.Data.SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@IProveedor", SqlDbType.VarChar, parametros.opciones.Proveedor));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);

            XRLabel labelProveedor = (XRLabel)reporte.FindControl("labelProveedor", false);
            labelProveedor.Text = "Proveedor: " + parametros.opciones.NombreProveedor;

            reporte.DataSource = dt;

            return reporte;

        }        
        public String FormatFecha(DateTime? fecha, string uso)
        {
            if (fecha != null)
                switch (uso)
                {
                    case "sp": return ((DateTime)fecha).ToString("yyyyMMdd HH:mm:ss");//para store procedure
                    case "fe": return ((DateTime)fecha).ToString("dd/MM/yyyy");//uso convencional Front end sin hora
                    case "feT": return ((DateTime)fecha).ToString("dd/MM/yyyy HH:mm:ss");//uso convencional Front end con horas
                    default: return ((DateTime)fecha).ToString("yyyyMMdd HH:mm:ss");
                }
            return null;
        }        
        

        public ControlIncidentes ReporteControlIncidentes(Reporte parametros)
        {
            ControlIncidentes reporte = new ControlIncidentes();
            DataTable dt = new DataTable();
            DataTable dtEncabezado = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
            string FechaActual = "";

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresaSucursal", SqlDbType.VarChar, parametros.session_empresa_sucursal));
            dtEncabezado = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);

            if (dtEncabezado.Rows.Count > 0)
            {
                FechaActual = dtEncabezado.Rows[0]["FechaActual"].ToString();

                XRLabel labelFecha = (XRLabel)reporte.FindControl("labelFecha", false);
                labelFecha.Text = "Fecha: " + FechaActual;

            }

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "16"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IProveedor", SqlDbType.VarChar, parametros.opciones.Proveedor));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaInicio", SqlDbType.VarChar, parametros.opciones.FechaInicial));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaFin", SqlDbType.VarChar, parametros.opciones.FechaFinal));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteConsignacion", parametrosDeEntrada);

            reporte.DataSource = dt;

            return reporte;

        }

        public EnvioConsignacionOrtopediaPdf EnvioConsignacionOrtopedia(Reporte parametros)
        {
            EnvioConsignacionOrtopediaPdf reporte = new EnvioConsignacionOrtopediaPdf();
            DataTable dt = new DataTable();
            DataTable dtEncabezado = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();
            string NombreEmpresa = "";
            string FechaActual = "";

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "3"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ICodigoBodega", SqlDbType.VarChar, parametros.opciones.CodigoBodega));
            dtEncabezado = conexion.getTableBySP("HOSPITAL", "SpHisConsignacionOrtopedia", parametrosDeEntrada);

            if (dtEncabezado.Rows.Count > 0)
            {
                NombreEmpresa = dtEncabezado.Rows[0]["Nombre"].ToString();
                FechaActual = dtEncabezado.Rows[0]["FechaActual"].ToString();

                XRLabel labelFecha = (XRLabel)reporte.FindControl("labelFecha", false);
                labelFecha.Text = FechaActual;

                XRLabel labelSucursal = (XRLabel)reporte.FindControl("labelSucursal", false);
                labelSucursal.Text = NombreEmpresa;

                XRLabel labelNoBodega = (XRLabel)reporte.FindControl("labelNoBodega", false);
                labelNoBodega.Text = parametros.opciones.CodigoBodega;

                XRLabel labelMes = (XRLabel)reporte.FindControl("labelMes", false);
                labelMes.Text = parametros.opciones.Mes;

                XRLabel labelPara = (XRLabel)reporte.FindControl("labelPara", false);
                labelPara.Text = parametros.opciones.ParaNombre != "" ? parametros.opciones.ParaNombre + " / Contabilidad" : "";

                XRLabel labelDe = (XRLabel)reporte.FindControl("labelDe", false);
                labelDe.Text = parametros.opciones.DeNombre;

            }

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "2"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", System.Data.SqlDbType.VarChar, parametros.session_empresa_real));
            parametrosDeEntrada.Add(conexion.crearParametro("@ITipoEnvio", System.Data.SqlDbType.VarChar, parametros.opciones.Tipo));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaInicio", System.Data.SqlDbType.VarChar, parametros.opciones.FechaInicial));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaFin", System.Data.SqlDbType.VarChar, parametros.opciones.FechaFinal));
            parametrosDeEntrada.Add(conexion.crearParametro("@ICodigoBodega", System.Data.SqlDbType.VarChar, parametros.opciones.CodigoBodega));
            dt = conexion.getTableBySP("HOSPITAL", "SpHisConsignacionOrtopedia", parametrosDeEntrada);

            reporte.DataSource = dt;

            return reporte;
        }

        public BitacoraMovimientoProducto ReporteBitacoraMovimientoProducto(Reporte parametros)
        {
            BitacoraMovimientoProducto reporte = new BitacoraMovimientoProducto();
            DataTable dt = new DataTable();
            DataTable dtEncabezado = new DataTable();
            List<SqlParameter> parametrosDeEntrada = new List<SqlParameter>();

            parametrosDeEntrada.Clear();
            parametrosDeEntrada.Add(conexion.crearParametro("@IOpcion", SqlDbType.VarChar, "C"));
            parametrosDeEntrada.Add(conexion.crearParametro("@ISubOpcion", SqlDbType.VarChar, "1"));
            parametrosDeEntrada.Add(conexion.crearParametro("@IEmpresa", SqlDbType.VarChar, parametros.opciones.Empresa));
            parametrosDeEntrada.Add(conexion.crearParametro("@IProducto", SqlDbType.VarChar, parametros.opciones.CodigoProducto));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaInicial", SqlDbType.VarChar, parametros.opciones.FechaInicial));
            parametrosDeEntrada.Add(conexion.crearParametro("@IFechaFinal", SqlDbType.VarChar, parametros.opciones.FechaFinal));
            parametrosDeEntrada.Add(conexion.crearParametro("@ILlevaFecha", SqlDbType.VarChar, parametros.opciones.Estado));

            dt = conexion.getTableBySP("HOSPITAL", "SpHisReporteBitacoraMovimientos", parametrosDeEntrada);

            reporte.DataSource = dt;

            return reporte;

        }

    }

    public class Objeto
    {
        [JsonProperty("Etiqueta")]
        public string Etiqueta { get; set; }
        [JsonProperty("Parametro")]
        public string Parametro { get; set; }
        [JsonProperty("value")]
        public string value { get; set; }
        [JsonProperty("Eval")]
        public string Eval { get; set; }
    }
}
