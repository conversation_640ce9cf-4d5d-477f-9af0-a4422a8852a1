﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="DetalleLote" Landscape="true" Margins="47, 54, 50, 0" PageWidth="1100" PageHeight="850" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" HeightF="50" />
    <Item2 Ref="2" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" />
    <Item3 Ref="3" ControlType="DetailBand" Name="Detail" HeightF="52.0833321">
      <Controls>
        <Item1 Ref="4" ControlType="XRTable" Name="table1" SizeF="999,52.0833321" LocationFloat="0,0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="5" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="6" ControlType="XRTableCell" Name="tableCell1" Weight="0.65384616456173661" Multiline="true" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="7" EventName="BeforePrint" PropertyName="Text" Expression="[Proveedor]" />
                  </ExpressionBindings>
                  <StylePriority Ref="8" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="9" ControlType="XRTableCell" Name="tableCell2" Weight="1.896450256461615" Multiline="true" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="10" EventName="BeforePrint" PropertyName="Text" Expression="[Nombre]" />
                  </ExpressionBindings>
                  <StylePriority Ref="11" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="12" ControlType="XRTableCell" Name="tableCell3" Weight="0.87573875046522209" Multiline="true" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="13" EventName="BeforePrint" PropertyName="Text" Expression="[Documento]" />
                  </ExpressionBindings>
                  <StylePriority Ref="14" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="15" ControlType="XRTableCell" Name="tableCell4" Weight="0.31656882583768647" Multiline="true" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="16" EventName="BeforePrint" PropertyName="Text" Expression="[Tipo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="17" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="18" ControlType="XRTableCell" Name="tableCell5" Weight="0.571695317083735" Multiline="true" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="19" EventName="BeforePrint" PropertyName="Text" Expression="[Periodo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="20" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="21" ControlType="XRTableCell" Name="tableCell6" Weight="0.65045861916250658" TextFormatString="{0:d/MM/yyyy}" Multiline="true" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="22" EventName="BeforePrint" PropertyName="Text" Expression="[Fecha]" />
                  </ExpressionBindings>
                  <StylePriority Ref="23" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item6>
                <Item7 Ref="24" ControlType="XRTableCell" Name="tableCell7" Weight="0.7004933538600252" TextFormatString="{0:C2}" Multiline="true" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="25" EventName="BeforePrint" PropertyName="Text" Expression="[Valor]" />
                  </ExpressionBindings>
                  <StylePriority Ref="26" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item7>
                <Item8 Ref="27" ControlType="XRTableCell" Name="tableCell16" Weight="0.71091820499870451" TextFormatString="{0:C2}" Multiline="true" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="28" EventName="BeforePrint" PropertyName="Text" Expression="[Saldo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="29" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item8>
                <Item9 Ref="30" ControlType="XRTableCell" Name="tableCell18" Weight="1.9385621221837641" Multiline="true" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="31" EventName="BeforePrint" PropertyName="Text" Expression="[Observaciones]" />
                  </ExpressionBindings>
                  <StylePriority Ref="32" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item9>
                <Item10 Ref="33" ControlType="XRTableCell" Name="tableCell20" Weight="0.65045840427938573" TextFormatString="{0:d/MM/yyyy}" Multiline="true" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="34" EventName="BeforePrint" PropertyName="Text" Expression="[Registro]" />
                  </ExpressionBindings>
                  <StylePriority Ref="35" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item10>
                <Item11 Ref="36" ControlType="XRTableCell" Name="tableCell22" Weight="1.0318553703468623" Multiline="true" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, charSet=0" Borders="All">
                  <ExpressionBindings>
                    <Item1 Ref="37" EventName="BeforePrint" PropertyName="Text" Expression="[CuentaContable]" />
                  </ExpressionBindings>
                  <StylePriority Ref="38" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item11>
              </Cells>
            </Item1>
          </Rows>
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="39" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="52.0833321">
      <Controls>
        <Item1 Ref="40" ControlType="XRTable" Name="table2" SizeF="999,52.0833321" LocationFloat="0,0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="41" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="42" ControlType="XRTableCell" Name="tableCell8" Weight="0.65384616456173661" Multiline="true" Text="Proveedor" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, style=Bold, charSet=0" Borders="All">
                  <StylePriority Ref="43" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="44" ControlType="XRTableCell" Name="tableCell9" Weight="1.8964499964301393" Multiline="true" Text="Nombre" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, style=Bold, charSet=0" Borders="All">
                  <StylePriority Ref="45" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="46" ControlType="XRTableCell" Name="tableCell10" Weight="0.87573901049669456" Multiline="true" Text="Documento" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, style=Bold, charSet=0" Borders="All">
                  <StylePriority Ref="47" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="48" ControlType="XRTableCell" Name="tableCell11" Weight="0.31656882583768547" Multiline="true" Text="Tipo" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, style=Bold, charSet=0" Borders="All">
                  <StylePriority Ref="49" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="50" ControlType="XRTableCell" Name="tableCell12" Weight="0.57169522446776522" Multiline="true" Text="Periodo" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, style=Bold, charSet=0" Borders="All">
                  <StylePriority Ref="51" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="52" ControlType="XRTableCell" Name="tableCell13" Weight="0.65045800198319048" Multiline="true" Text="Fecha" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, style=Bold, charSet=0" Borders="All">
                  <StylePriority Ref="53" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item6>
                <Item7 Ref="54" ControlType="XRTableCell" Name="tableCell14" Weight="0.70049386162162475" Multiline="true" Text="Valor" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, style=Bold, charSet=0" Borders="All">
                  <StylePriority Ref="55" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item7>
                <Item8 Ref="56" ControlType="XRTableCell" Name="tableCell15" Weight="0.710917489569603" Multiline="true" Text="Saldo" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, style=Bold, charSet=0" Borders="All">
                  <StylePriority Ref="57" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item8>
                <Item9 Ref="58" ControlType="XRTableCell" Name="tableCell17" Weight="1.9385617428655906" Multiline="true" Text="Observaciones" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, style=Bold, charSet=0" Borders="All">
                  <StylePriority Ref="59" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item9>
                <Item10 Ref="60" ControlType="XRTableCell" Name="tableCell19" Weight="0.65045830233684165" Multiline="true" Text="Registro" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, style=Bold, charSet=0" Borders="All">
                  <StylePriority Ref="61" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item10>
                <Item11 Ref="62" ControlType="XRTableCell" Name="tableCell21" Weight="1.0318552088816058" Multiline="true" Text="Cuenta Contable" TextAlignment="MiddleLeft" Font="Arial, 8.25pt, style=Bold, charSet=0" Borders="All">
                  <StylePriority Ref="63" UseFont="false" UseBorders="false" UseTextAlignment="false" />
                </Item11>
              </Cells>
            </Item1>
          </Rows>
        </Item1>
      </Controls>
    </Item4>
  </Bands>
</XtraReportsLayoutSerializer>