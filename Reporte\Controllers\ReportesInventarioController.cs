﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Radiologia;
using Reporte.Estructura;
using Reporte.Models;
using Reporte.Reporte;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;


namespace Reporte.Controllers
{
    public class ReportesInventarioController: Controller
    {

      


        [Authorize]
        [HttpPost]
        [Route("ReporteCierreExistenciasInventario/Reporte")]
        public IActionResult ReporteCierreExistencias([FromBody] EstructuraSesion param)
        {
            //Se agregaron valores por defecto cuando el reporte sea generado desde la pagina web.
            try
            {
                if (!ModelState.IsValid) return Content(JsonConvert.SerializeObject(new { codigo = -1, descripcion = "Parámetros no válidos." }), "application/json");
                ReporteCierreExistenciasInventarioModel reporteCierreExistencias = new ReporteCierreExistenciasInventarioModel();
                byte[] result = reporteCierreExistencias.GeneraCierreExistencias(param).ToArray();
                return File(result, param.opciones.tiporeporte);
            }
            catch (Exception ex)
            {
                return BadRequest(JsonConvert.SerializeObject(ex));
            }

        }

        [Authorize]
        [HttpPost]
        [Route("ReporteDespachoPorBodega/Reporte")]
        public IActionResult ReporteDespachoPorBodega([FromBody] EstructuraSesion param)
        {
            //Se agregaron valores por defecto cuando el reporte sea generado desde la pagina web.
            try
            {
                if (!ModelState.IsValid) return Content(JsonConvert.SerializeObject(new { codigo = -1, descripcion = "Parámetros no válidos." }), "application/json");
                ReporteDespachosReqBodegasModel reporteDespachosPorBodega = new ReporteDespachosReqBodegasModel();
                byte[] result = reporteDespachosPorBodega.GeneraReporteDespachosPorBodega(param).ToArray();
                return File(result, param.opciones.tiporeporte);
            }
            catch (Exception ex)
            {
                return BadRequest(JsonConvert.SerializeObject(ex));
            }

        }



        [Authorize]
        [HttpPost]
        [Route("ReporteConsumosPorDepartamento/Reporte")]
        public IActionResult ReporteConsumosPorDepartamento([FromBody] EstructuraSesion param)
        {
            //Se agregaron valores por defecto cuando el reporte sea generado desde la pagina web.
            try
            {
                if (!ModelState.IsValid) return Content(JsonConvert.SerializeObject(new { codigo = -1, descripcion = "Parámetros no válidos." }), "application/json");
                ReporteConsumosPorDepartamentoModel reporteConsumosPorDepartamento = new ReporteConsumosPorDepartamentoModel();
                byte[] result = reporteConsumosPorDepartamento.GeneraReporteConsumosPorDepartamento(param).ToArray();
                return File(result, param.opciones.tiporeporte);
            }
            catch (Exception ex)
            {
                return BadRequest(JsonConvert.SerializeObject(ex));
            }

        }


        [Authorize]
        [HttpPost]
        [Route("ReporteKardexProductoBodega/Reporte")]
        public IActionResult ReporteKardex([FromBody] EstructuraSesion param)
        {   
            //Se agregaron valores por defecto cuando el reporte sea generado desde la pagina web.
            try
            {
                if (!ModelState.IsValid) return Content(JsonConvert.SerializeObject(new { codigo = -1, descripcion = "Parámetros no válidos." }), "application/json");
                ReporteKardexBodegaProducto reporteKardexBodegaProducto = new ReporteKardexBodegaProducto();
                byte[] result = reporteKardexBodegaProducto.GeneraReporteKardexBodegaProducto(param).ToArray();
                return File(result, param.opciones.tiporeporte);
            }
            catch (Exception ex)
            {
                return BadRequest(JsonConvert.SerializeObject(ex));
            }

        }

    }
}
