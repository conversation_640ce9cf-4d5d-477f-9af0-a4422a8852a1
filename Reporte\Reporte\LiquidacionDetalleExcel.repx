﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="LiquidacionDetalleExcel" Margins="100, 100, 154, 28" PaperKind="Custom" PageWidth="1700" PageHeight="1100" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" HeightF="154.166672">
      <Controls>
        <Item1 Ref="2" ControlType="XRTable" Name="table2" TextAlignment="BottomLeft" SizeF="1475.27026,28.125" LocationFloat="0,126.041664" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="SkyBlue" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="3" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="4" ControlType="XRTableCell" Name="tableCell15" Weight="0.690721750485437" Multiline="true" Text="Codigo" />
                <Item2 Ref="5" ControlType="XRTableCell" Name="tableCell16" Weight="2.9793824575420325" Multiline="true" Text="Producto" />
                <Item3 Ref="6" ControlType="XRTableCell" Name="tableCell17" Weight="0.63037973930461555" Multiline="true" Text="Hospital" />
                <Item4 Ref="7" ControlType="XRTableCell" Name="tableCell18" Weight="0.7850187818142228" Multiline="true" Text="Serie Admision" />
                <Item5 Ref="8" ControlType="XRTableCell" Name="tableCell19" Weight="0.7850187818142228" Multiline="true" Text="Admision" />
                <Item6 Ref="9" ControlType="XRTableCell" Name="tableCell20" Weight="0.7850187818142228" Multiline="true" Text="Cantidad" />
                <Item7 Ref="10" ControlType="XRTableCell" Name="tableCell21" Weight="0.62264959032935852" Multiline="true" Text="Tipo Cargo" />
                <Item8 Ref="11" ControlType="XRTableCell" Name="tableCell22" Weight="0.66903741917863013" Multiline="true" Text="Cargo" />
                <Item9 Ref="12" ControlType="XRTableCell" Name="tableCell23" Weight="0.78501786676222618" Multiline="true" Text="Fecha" />
                <Item10 Ref="13" ControlType="XRTableCell" Name="tableCell24" Weight="0.47909517338561924" Multiline="true" Text="Bodega" />
                <Item11 Ref="14" ControlType="XRTableCell" Name="tableCell1" Weight="0.78837342215314343" Multiline="true" Text="Id Envio" />
                <Item12 Ref="15" ControlType="XRTableCell" Name="tableCell2" Weight="0.95074499544190094" Multiline="true" Text="Costo Envio" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="16" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="17" ControlType="XRLabel" Name="labelProveedor" Multiline="true" TextAlignment="BottomLeft" SizeF="790.8898,27.0833282" LocationFloat="0,81.25" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="18" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="19" ControlType="XRLabel" Name="label1" Multiline="true" Text="SERVICIOS MEDICOS Y HOSPITALARIOS CENTROAMERICANOS, S.A." TextAlignment="BottomLeft" SizeF="790.889648,27.0833321" LocationFloat="0,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="20" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="21" ControlType="XRLabel" Name="label2" Multiline="true" Text="Reporte de detallado de consumos de productos en Consignación" TextAlignment="BottomLeft" SizeF="790.889648,27.08333" LocationFloat="0,27.083334" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="22" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="23" ControlType="XRLabel" Name="labelLiquidacion" Multiline="true" TextAlignment="BottomLeft" SizeF="790.8898,27.0833321" LocationFloat="0,54.1666679" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="24" UseFont="false" UseTextAlignment="false" />
        </Item5>
      </Controls>
    </Item1>
    <Item2 Ref="25" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="28.125" />
    <Item3 Ref="26" ControlType="DetailBand" Name="Detail" HeightF="25">
      <Controls>
        <Item1 Ref="27" ControlType="XRTable" Name="table1" TextAlignment="BottomLeft" SizeF="1475.27026,25" LocationFloat="0,0" Font="Arial, 9.75pt, charSet=0" BackColor="Transparent" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="28" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="29" ControlType="XRTableCell" Name="tableCell3" Weight="0.690721750485437" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="30" EventName="BeforePrint" PropertyName="Text" Expression="[Codigo]" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="31" ControlType="XRTableCell" Name="tableCell4" Weight="2.9793824575420325" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="32" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
                  </ExpressionBindings>
                </Item2>
                <Item3 Ref="33" ControlType="XRTableCell" Name="tableCell5" Weight="0.63037973930461555" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="34" EventName="BeforePrint" PropertyName="Text" Expression="[Hospital]" />
                  </ExpressionBindings>
                </Item3>
                <Item4 Ref="35" ControlType="XRTableCell" Name="tableCell6" Weight="0.7850187818142228" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="36" EventName="BeforePrint" PropertyName="Text" Expression="[SerieAdmision]" />
                  </ExpressionBindings>
                </Item4>
                <Item5 Ref="37" ControlType="XRTableCell" Name="tableCell7" Weight="0.7850187818142228" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="38" EventName="BeforePrint" PropertyName="Text" Expression="[Admision]" />
                  </ExpressionBindings>
                </Item5>
                <Item6 Ref="39" ControlType="XRTableCell" Name="tableCell8" Weight="0.7850187818142228" TextFormatString="{0:N0}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="40" EventName="BeforePrint" PropertyName="Text" Expression="[Cantidad]" />
                  </ExpressionBindings>
                </Item6>
                <Item7 Ref="41" ControlType="XRTableCell" Name="tableCell9" Weight="0.62264930717659583" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="42" EventName="BeforePrint" PropertyName="Text" Expression="[Tipo_Cargo]" />
                  </ExpressionBindings>
                </Item7>
                <Item8 Ref="43" ControlType="XRTableCell" Name="tableCell10" Weight="0.66903770233139281" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="44" EventName="BeforePrint" PropertyName="Text" Expression="[Cargo]" />
                  </ExpressionBindings>
                </Item8>
                <Item9 Ref="45" ControlType="XRTableCell" Name="tableCell11" Weight="0.78501786676222618" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="46" EventName="BeforePrint" PropertyName="Text" Expression="[Fecha]" />
                  </ExpressionBindings>
                </Item9>
                <Item10 Ref="47" ControlType="XRTableCell" Name="tableCell12" Weight="0.47909517338561924" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="48" EventName="BeforePrint" PropertyName="Text" Expression="[Bodega]" />
                  </ExpressionBindings>
                </Item10>
                <Item11 Ref="49" ControlType="XRTableCell" Name="tableCell13" Weight="0.78837342215314343" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="50" EventName="BeforePrint" PropertyName="Text" Expression="[IdEnvio]" />
                  </ExpressionBindings>
                </Item11>
                <Item12 Ref="51" ControlType="XRTableCell" Name="tableCell14" Weight="0.95074499544190094" TextFormatString="{0:C2}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="52" EventName="BeforePrint" PropertyName="Text" Expression="[CostoConvenio]" />
                  </ExpressionBindings>
                </Item12>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="53" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item3>
  </Bands>
</XtraReportsLayoutSerializer>