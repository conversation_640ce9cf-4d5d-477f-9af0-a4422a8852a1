﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="PendienteAceptarTexto" Landscape="true" Margins="47, 30, 100, 100" PageWidth="1100" PageHeight="850" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" />
    <Item2 Ref="2" ControlType="BottomMarginBand" Name="BottomMargin" />
    <Item3 Ref="3" ControlType="DetailBand" Name="Detail" HeightF="47.9166679">
      <Controls>
        <Item1 Ref="4" ControlType="XRTable" Name="table1" SizeF="1022.99994,47.9166679" LocationFloat="0,0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="5" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="6" ControlType="XRTableCell" Name="tableCell1" Weight="0.52452834617379107" Multiline="true" Font="Arial, 6.75pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="7" EventName="BeforePrint" PropertyName="Text" Expression="[BodegaFuente]" />
                  </ExpressionBindings>
                  <StylePriority Ref="8" UseFont="false" />
                </Item1>
                <Item2 Ref="9" ControlType="XRTableCell" Name="tableCell2" Weight="0.50943368113372123" Multiline="true" Font="Arial, 6.75pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="10" EventName="BeforePrint" PropertyName="Text" Expression="[BodegaDestino]" />
                  </ExpressionBindings>
                  <StylePriority Ref="11" UseFont="false" />
                </Item2>
                <Item3 Ref="12" ControlType="XRTableCell" Name="tableCell3" Weight="0.75471722227802318" Multiline="true" Font="Arial, 6.75pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="13" EventName="BeforePrint" PropertyName="Text" Expression="[Movimiento]" />
                  </ExpressionBindings>
                  <StylePriority Ref="14" UseFont="false" />
                </Item3>
                <Item4 Ref="15" ControlType="XRTableCell" Name="tableCell4" Weight="0.6981131003939578" TextFormatString="{0:d/MM/yyyy}" Multiline="true" Font="Arial, 6.75pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="16" EventName="BeforePrint" PropertyName="Text" Expression="[Fecha]" />
                  </ExpressionBindings>
                  <StylePriority Ref="17" UseFont="false" />
                </Item4>
                <Item5 Ref="18" ControlType="XRTableCell" Name="tableCell5" Weight="1.1320755703999486" Multiline="true" Font="Arial, 6.75pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="19" EventName="BeforePrint" PropertyName="Text" Expression="[FechaDespacho]" />
                  </ExpressionBindings>
                  <StylePriority Ref="20" UseFont="false" />
                </Item5>
                <Item6 Ref="21" ControlType="XRTableCell" Name="tableCell6" Weight="0.73584912987985573" Multiline="true" Font="Arial, 6.75pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="22" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="23" UseFont="false" />
                </Item6>
                <Item7 Ref="24" ControlType="XRTableCell" Name="tableCell7" Weight="3.3150947672239606" Multiline="true" Font="Arial, 6.75pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="25" EventName="BeforePrint" PropertyName="Text" Expression="[Nombre]" />
                  </ExpressionBindings>
                  <StylePriority Ref="26" UseFont="false" />
                </Item7>
                <Item8 Ref="27" ControlType="XRTableCell" Name="tableCell8" Weight="0.69245271135994324" Multiline="true" Font="Arial, 6.75pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="28" EventName="BeforePrint" PropertyName="Text" Expression="[Existencia]" />
                  </ExpressionBindings>
                  <StylePriority Ref="29" UseFont="false" />
                </Item8>
                <Item9 Ref="30" ControlType="XRTableCell" Name="tableCell9" Weight="0.90264122091638044" TextFormatString="{0:C2}" Multiline="true" Font="Arial, 6.75pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="31" EventName="BeforePrint" PropertyName="Text" Expression="[Valor]" />
                  </ExpressionBindings>
                  <StylePriority Ref="32" UseFont="false" />
                </Item9>
              </Cells>
            </Item1>
          </Rows>
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="33" ControlType="PageHeaderBand" Name="PageHeader" HeightF="0">
      <SubBands>
        <Item1 Ref="34" ControlType="SubBand" Name="SubBand1" HeightF="47.9166679">
          <Controls>
            <Item1 Ref="35" ControlType="XRTable" Name="table2" SizeF="1022.99994,47.9166679" LocationFloat="0,0" Padding="2,2,0,0,96">
              <Rows>
                <Item1 Ref="36" ControlType="XRTableRow" Name="tableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="37" ControlType="XRTableCell" Name="tableCell10" Weight="0.52452834617379107" Multiline="true" Text="Bodega Fuente" TextAlignment="BottomLeft">
                      <StylePriority Ref="38" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="39" ControlType="XRTableCell" Name="tableCell11" Weight="0.50943368113372123" Multiline="true" Text="Bodega Destino" TextAlignment="BottomLeft">
                      <StylePriority Ref="40" UseTextAlignment="false" />
                    </Item2>
                    <Item3 Ref="41" ControlType="XRTableCell" Name="tableCell12" Weight="0.75471722227802318" Multiline="true" Text="Movimiento" TextAlignment="BottomLeft">
                      <StylePriority Ref="42" UseTextAlignment="false" />
                    </Item3>
                    <Item4 Ref="43" ControlType="XRTableCell" Name="tableCell13" Weight="0.6981131003939578" Multiline="true" Text="Fecha" TextAlignment="BottomLeft">
                      <StylePriority Ref="44" UseTextAlignment="false" />
                    </Item4>
                    <Item5 Ref="45" ControlType="XRTableCell" Name="tableCell14" Weight="1.1320755703999486" Multiline="true" Text="Fecha Despacho" TextAlignment="BottomLeft">
                      <StylePriority Ref="46" UseTextAlignment="false" />
                    </Item5>
                    <Item6 Ref="47" ControlType="XRTableCell" Name="tableCell15" Weight="0.73584912987985573" Multiline="true" Text="Producto" TextAlignment="BottomLeft">
                      <StylePriority Ref="48" UseTextAlignment="false" />
                    </Item6>
                    <Item7 Ref="49" ControlType="XRTableCell" Name="tableCell16" Weight="3.3150947672239606" Multiline="true" Text="Nombre Producto" TextAlignment="BottomLeft">
                      <StylePriority Ref="50" UseTextAlignment="false" />
                    </Item7>
                    <Item8 Ref="51" ControlType="XRTableCell" Name="tableCell17" Weight="0.69245271135994324" Multiline="true" Text="Cantidad" TextAlignment="BottomLeft">
                      <StylePriority Ref="52" UseTextAlignment="false" />
                    </Item8>
                    <Item9 Ref="53" ControlType="XRTableCell" Name="tableCell18" Weight="0.90264122091638044" Multiline="true" Text="Valor" TextAlignment="BottomLeft">
                      <StylePriority Ref="54" UseTextAlignment="false" />
                    </Item9>
                  </Cells>
                </Item1>
              </Rows>
            </Item1>
          </Controls>
        </Item1>
      </SubBands>
    </Item4>
  </Bands>
</XtraReportsLayoutSerializer>