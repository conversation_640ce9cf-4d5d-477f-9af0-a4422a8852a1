﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="ConveniosProveedores" Landscape="true" Margins="100, 100, 100, 1" PaperKind="Custom" PageWidth="2200" PageHeight="850" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin">
      <Controls>
        <Item1 Ref="2" ControlType="XRTable" Name="table2" TextAlignment="BottomLeft" SizeF="2000,32.2916679" LocationFloat="0,67.7083359" Font="Arial, 8.25pt, style=Bold, charSet=0" BackColor="SkyBlue" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="3" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="4" ControlType="XRTableCell" Name="tableCell13" Weight="0.31958760550749565" Multiline="true" Text="No" />
                <Item2 Ref="5" ControlType="XRTableCell" Name="tableCell14" Weight="0.54381443823460662" Multiline="true" Text="IdConvenio" />
                <Item3 Ref="6" ControlType="XRTableCell" Name="tableCell1" Weight="0.3737114734440185" Multiline="true" Text="Linea" />
                <Item4 Ref="7" ControlType="XRTableCell" Name="tableCell15" Weight="0.5515459741057358" Multiline="true" Text="Producto" />
                <Item5 Ref="8" ControlType="XRTableCell" Name="tableCell16" Weight="2.3144345888995317" Multiline="true" Text="Nombre" />
                <Item6 Ref="9" ControlType="XRTableCell" Name="tableCell17" Weight="0.41388473710892271" Multiline="true" Text="Status" />
                <Item7 Ref="10" ControlType="XRTableCell" Name="tableCell18" Weight="0.74635868493084856" Multiline="true" Text="Costo Convenio" />
                <Item8 Ref="11" ControlType="XRTableCell" Name="tableCell20" Weight="0.74635959101979088" Multiline="true" Text="Precio Convenio" />
                <Item9 Ref="12" ControlType="XRTableCell" Name="tableCell21" Weight="0.52986439637628946" Multiline="true" Text="Proveedor" />
                <Item10 Ref="13" ControlType="XRTableCell" Name="tableCell22" Weight="2.022130713772524" Multiline="true" Text="Nombre Proveedor" />
                <Item11 Ref="14" ControlType="XRTableCell" Name="tableCell23" Weight="0.58398708471868188" Multiline="true" Text="Convenio" />
                <Item12 Ref="15" ControlType="XRTableCell" Name="tableCell24" Weight="0.61053814168265674" Multiline="true" Text="Fecha" />
                <Item13 Ref="16" ControlType="XRTableCell" Name="tableCell2" Weight="0.77291018615725049" Multiline="true" Text="Fecha Convenio" />
                <Item14 Ref="17" ControlType="XRTableCell" Name="tableCell3" Weight="0.65693080159607131" Multiline="true" Text="Fecha Aplicar" />
                <Item15 Ref="18" ControlType="XRTableCell" Name="tableCell4" Weight="3.6593027168488992" Multiline="true" Text="Informacion" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="19" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="20" ControlType="XRLabel" Name="label1" Multiline="true" Text="Grupo Sermesa" TextAlignment="BottomLeft" SizeF="552.777954,27.0833321" LocationFloat="0,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="21" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="22" ControlType="XRLabel" Name="label2" Multiline="true" Text="Convenios de Consignación(Resumen)" TextAlignment="BottomLeft" SizeF="552.777954,27.08333" LocationFloat="0,27.083334" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="23" UseFont="false" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item1>
    <Item2 Ref="24" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="1" />
    <Item3 Ref="25" ControlType="DetailBand" Name="Detail" HeightF="32.2916679">
      <Controls>
        <Item1 Ref="26" ControlType="XRTable" Name="table1" TextAlignment="BottomLeft" SizeF="2000,32.2916679" LocationFloat="0,0" Font="Arial, 8.25pt, charSet=0" BackColor="Transparent" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="27" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="28" ControlType="XRTableCell" Name="tableCell5" Weight="0.31958760550749565" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="29" EventName="BeforePrint" PropertyName="Text" Expression="[IdCorrelativo]" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="30" ControlType="XRTableCell" Name="tableCell6" Weight="0.54381443823460662" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="31" EventName="BeforePrint" PropertyName="Text" Expression="[IdConvenio]" />
                  </ExpressionBindings>
                </Item2>
                <Item3 Ref="32" ControlType="XRTableCell" Name="tableCell7" Weight="0.3737114734440185" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="33" EventName="BeforePrint" PropertyName="Text" Expression="[Linea]" />
                  </ExpressionBindings>
                </Item3>
                <Item4 Ref="34" ControlType="XRTableCell" Name="tableCell8" Weight="0.5515459741057358" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="35" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
                  </ExpressionBindings>
                </Item4>
                <Item5 Ref="36" ControlType="XRTableCell" Name="tableCell9" Weight="2.3144345888995317" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="37" EventName="BeforePrint" PropertyName="Text" Expression="[Nombre]" />
                  </ExpressionBindings>
                </Item5>
                <Item6 Ref="38" ControlType="XRTableCell" Name="tableCell10" Weight="0.41388473710892271" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="39" EventName="BeforePrint" PropertyName="Text" Expression="[Status]" />
                  </ExpressionBindings>
                </Item6>
                <Item7 Ref="40" ControlType="XRTableCell" Name="tableCell11" Weight="0.74635868493084856" TextFormatString="{0:C2}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="41" EventName="BeforePrint" PropertyName="Text" Expression="[CostoConvenio]" />
                  </ExpressionBindings>
                </Item7>
                <Item8 Ref="42" ControlType="XRTableCell" Name="tableCell25" Weight="0.74635959101979088" TextFormatString="{0:C2}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="43" EventName="BeforePrint" PropertyName="Text" Expression="[PrecioConvenio]" />
                  </ExpressionBindings>
                </Item8>
                <Item9 Ref="44" ControlType="XRTableCell" Name="tableCell26" Weight="0.52986439637628946" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="45" EventName="BeforePrint" PropertyName="Text" Expression="[Proveedor]" />
                  </ExpressionBindings>
                </Item9>
                <Item10 Ref="46" ControlType="XRTableCell" Name="tableCell27" Weight="2.022130713772524" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="47" EventName="BeforePrint" PropertyName="Text" Expression="[NbreProv]" />
                  </ExpressionBindings>
                </Item10>
                <Item11 Ref="48" ControlType="XRTableCell" Name="tableCell28" Weight="0.58398708471868188" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="49" EventName="BeforePrint" PropertyName="Text" Expression="[Convenio]" />
                  </ExpressionBindings>
                </Item11>
                <Item12 Ref="50" ControlType="XRTableCell" Name="tableCell29" Weight="0.61053814168265674" TextFormatString="{0:d/MM/yyyy HH:mm:ss}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="51" EventName="BeforePrint" PropertyName="Text" Expression="[Fecha]" />
                  </ExpressionBindings>
                </Item12>
                <Item13 Ref="52" ControlType="XRTableCell" Name="tableCell30" Weight="0.77291018615725049" TextFormatString="{0:d/MM/yyyy}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="53" EventName="BeforePrint" PropertyName="Text" Expression="[FechaConvenio]" />
                  </ExpressionBindings>
                </Item13>
                <Item14 Ref="54" ControlType="XRTableCell" Name="tableCell31" Weight="0.65693080159607131" TextFormatString="{0:d/MM/yyyy HH:mm:ss}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="55" EventName="BeforePrint" PropertyName="Text" Expression="[FechaAplicar]" />
                  </ExpressionBindings>
                </Item14>
                <Item15 Ref="56" ControlType="XRTableCell" Name="tableCell32" Weight="3.6593027168488992" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="57" EventName="BeforePrint" PropertyName="Text" Expression="[Informacion]" />
                  </ExpressionBindings>
                </Item15>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="58" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item3>
  </Bands>
</XtraReportsLayoutSerializer>