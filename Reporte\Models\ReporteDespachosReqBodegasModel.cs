﻿using DevExpress.XtraPrinting;
using Modelo.Conexion;
using Radiologia;
using Reporte.Estructura;
using Reporte.Reporte;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Reporte.Models
{
    public class ReporteDespachosReqBodegasModel
    {
        private IConexion conexion;

        public ReporteDespachosReqBodegasModel()
        {
            String Usuario = Startup.Conexion["Tag_Usuario"].ToString();
            String Password = Startup.Conexion["Tag_Password"].ToString();

            String Instancia = Startup.Conexion["Instancia"].ToString();
            DBManager db = new DBManager(Usuario, Password, Instancia);
            conexion = db.ObtenerConexion();
        }


        public MemoryStream GeneraReporteDespachosPorBodega(EstructuraSesion cierreExistencias)
        {

            ReporteDespachosPorBodega reporteDespachosPorBodega = new ReporteDespachosPorBodega();
            DataTable resultados = new DataTable();
            DataTable admisionesList = new DataTable();
            DataSet datosResumen = new DataSet();
            List<SqlParameter> parametrosEntrada = new List<SqlParameter>();


            try
            {
                parametrosEntrada.Add(conexion.crearParametro("@EmpresaBodega", SqlDbType.VarChar, cierreExistencias.session_empresa_bodega));
                parametrosEntrada.Add(conexion.crearParametro("@FechaInicial", SqlDbType.DateTime, cierreExistencias.opciones.FechaInicio));
                parametrosEntrada.Add(conexion.crearParametro("@FechaFinal", SqlDbType.DateTime, cierreExistencias.opciones.FechaFin));
                parametrosEntrada.Add(conexion.crearParametro("@Bodega", SqlDbType.Int, cierreExistencias.opciones.Bodega));
                

                resultados = conexion.getTableBySP("HOSPITAL", "spINVDespachoRequerimientoxBodega", parametrosEntrada);


                DataTable inventarioExistencias = resultados;//.ToDataTable();

                admisionesList.TableName = "resumen";

                datosResumen.Tables.Add(inventarioExistencias);
                reporteDespachosPorBodega.DataSource = datosResumen;


                using (MemoryStream ms2 = new MemoryStream())
                {
                    PdfExportOptions pdfOptions = reporteDespachosPorBodega.ExportOptions.Pdf;
                    pdfOptions.ConvertImagesToJpeg = false;
                    pdfOptions.ImageQuality = PdfJpegImageQuality.High;
                    pdfOptions.PdfACompatibility = PdfACompatibility.PdfA3b;
                    pdfOptions.DocumentOptions.Author = "Sighos";
                    pdfOptions.DocumentOptions.Keywords = "Sighos, Reporte, PDF";
                    //pdfOptions.DocumentOptions.Producer = Environment.UserName.ToString();
                    pdfOptions.DocumentOptions.Subject = "Reporte";
                    pdfOptions.DocumentOptions.Title = "Reporte";

                    if (cierreExistencias.opciones.tiporeporte == "text/csv") reporteDespachosPorBodega.ExportToCsv(ms2);
                    if (cierreExistencias.opciones.tiporeporte == "application/pdf") reporteDespachosPorBodega.ExportToPdf(ms2, pdfOptions);
                    if (cierreExistencias.opciones.tiporeporte == "application/vnd.ms-excel") reporteDespachosPorBodega.ExportToXlsx(ms2);

                    ms2.Seek(0, System.IO.SeekOrigin.Begin);

                    admisionesList = null;
                    conexion.closeConexion();
                    conexion = null;
                    reporteDespachosPorBodega.DataSource = null;
                    reporteDespachosPorBodega.Dispose();
                    reporteDespachosPorBodega = null;
                    System.GC.Collect(2);
                    System.GC.WaitForFullGCComplete();

                    return ms2;
                }

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex);
            }

        }
    }
}
