﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="*********" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="MovimientosRequerimientosExcel" Margins="49, 50, 191, 116" PageWidth="850" PageHeight="1100" Version="20.1">
  <Parameters>
    <Item1 Ref="2" Description="VerCostoPromedio" ValueInfo="False" Name="VerCostoPromedio" Type="#Ref-1" />
  </Parameters>
  <Bands>
    <Item1 Ref="3" ControlType="TopMarginBand" Name="topMarginBand1" HeightF="191">
      <Controls>
        <Item1 Ref="4" ControlType="XRLabel" Name="labelFecha" Multiline="true" Text="labelFecha" TextAlignment="MiddleLeft" WordWrap="false" SizeF="469.682648,21.8749962" LocationFloat="0,0" Padding="2,2,0,0,100">
          <StylePriority Ref="5" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="6" ControlType="XRLabel" Name="labelEmpresa" Multiline="true" Text="labelEmpresa" TextAlignment="MiddleCenter" WordWrap="false" SizeF="749.999939,34.375" LocationFloat="3.17891427E-05,54.79167" Font="Arial, 14.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="7" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="8" ControlType="XRLabel" Name="labelUsuario" Multiline="true" Text="labelUsuario&#xD;&#xA;" TextAlignment="MiddleRight" WordWrap="false" SizeF="140.171326,21.8749962" LocationFloat="609.8287,0" Padding="2,2,0,0,100">
          <StylePriority Ref="9" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="10" ControlType="XRLabel" Name="labelNombeMovimiento" Multiline="true" Text="MOVIMIENTOS" TextAlignment="MiddleCenter" WordWrap="false" SizeF="750,30.2083282" LocationFloat="0,110.833328" Font="Arial, 14.25pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="11" UseFont="false" UseTextAlignment="false" />
        </Item4>
      </Controls>
    </Item1>
    <Item2 Ref="12" ControlType="DetailBand" Name="detailBand1" HeightF="23.958334">
      <Controls>
        <Item1 Ref="13" ControlType="XRTable" Name="table1" SizeF="750,23.958334" LocationFloat="0,0" Padding="2,2,0,0,96" BorderColor="Black">
          <Rows>
            <Item1 Ref="14" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="15" ControlType="XRTableCell" Name="tableCell1" Weight="0.50167203640769664" TextFormatString="{0:#}" Multiline="true" TextAlignment="MiddleLeft" WordWrap="false" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="16" EventName="BeforePrint" PropertyName="Text" Expression="ToInt([Producto])" />
                  </ExpressionBindings>
                  <StylePriority Ref="17" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="18" ControlType="XRTableCell" Name="tableCell2" Weight="1.2460540711984929" Multiline="true" TextAlignment="MiddleLeft" WordWrap="false" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="19" EventName="BeforePrint" PropertyName="Text" Expression="[NombreProducto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="20" UseBorders="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="21" ControlType="XRTableCell" Name="tableCell3" Weight="1.0151309734073273" Multiline="true" TextAlignment="MiddleLeft" WordWrap="false" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="22" EventName="BeforePrint" PropertyName="Text" Expression="[Descripcion]" />
                  </ExpressionBindings>
                  <StylePriority Ref="23" UseBorders="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="24" ControlType="XRTableCell" Name="tableCell22" Weight="0.49492136257783981" Multiline="true" TextAlignment="MiddleLeft" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="25" EventName="BeforePrint" PropertyName="Text" Expression="[CantidadPedida]" />
                  </ExpressionBindings>
                  <StylePriority Ref="26" UseBorders="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="27" ControlType="XRTableCell" Name="tableCell19" Weight="0.53393242773896477" Multiline="true" TextAlignment="MiddleLeft" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="28" EventName="BeforePrint" PropertyName="Text" Expression="[CostoUnitario]" />
                  </ExpressionBindings>
                  <StylePriority Ref="29" UseBorders="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="30" ControlType="XRTableCell" Name="tableCell4" Weight="0.43813891295667917" Multiline="true" TextAlignment="MiddleLeft" WordWrap="false" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="31" EventName="BeforePrint" PropertyName="Text" Expression="[Costo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="32" UseBorders="false" UseTextAlignment="false" />
                </Item6>
                <Item7 Ref="33" ControlType="XRTableCell" Name="tableCell17" Weight="0.54896423093352509" Multiline="true" TextAlignment="MiddleRight" WordWrap="false" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="34" EventName="BeforePrint" PropertyName="Text" Expression="[Cantidad]" />
                  </ExpressionBindings>
                  <StylePriority Ref="35" UseBorders="false" UseTextAlignment="false" />
                </Item7>
                <Item8 Ref="36" ControlType="XRTableCell" Name="tableCell18" Weight="0.42328209307435027" Multiline="true" TextAlignment="MiddleRight" WordWrap="false" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="37" EventName="BeforePrint" PropertyName="Text" Expression="[Cantidad]" />
                  </ExpressionBindings>
                  <StylePriority Ref="38" UseBorders="false" UseTextAlignment="false" />
                </Item8>
              </Cells>
            </Item1>
          </Rows>
          <ExpressionBindings>
            <Item1 Ref="39" EventName="BeforePrint" PropertyName="Visible" Expression="?VerCostoPromedio" />
          </ExpressionBindings>
          <StylePriority Ref="40" UseBorderColor="false" />
        </Item1>
        <Item2 Ref="41" ControlType="XRTable" Name="table3" SizeF="750.000061,23.958334" LocationFloat="0,0" Padding="2,2,0,0,96" BorderColor="Black">
          <Rows>
            <Item1 Ref="42" ControlType="XRTableRow" Name="tableRow3" Weight="1">
              <Cells>
                <Item1 Ref="43" ControlType="XRTableCell" Name="tableCell11" Weight="0.50167203640769664" TextFormatString="{0:#}" Multiline="true" TextAlignment="MiddleLeft" WordWrap="false" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="44" EventName="BeforePrint" PropertyName="Text" Expression="ToInt([Producto])" />
                  </ExpressionBindings>
                  <StylePriority Ref="45" UseBorders="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="46" ControlType="XRTableCell" Name="tableCell12" Weight="2.7561074619327948" Multiline="true" TextAlignment="MiddleLeft" WordWrap="false" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="47" EventName="BeforePrint" PropertyName="Text" Expression="[NombreProducto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="48" UseBorders="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="49" ControlType="XRTableCell" Name="tableCell13" Weight="0.53393136860241386" Multiline="true" TextAlignment="MiddleLeft" WordWrap="false" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="50" EventName="BeforePrint" PropertyName="Text" Expression="[Descripcion]" />
                  </ExpressionBindings>
                  <StylePriority Ref="51" UseBorders="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="52" ControlType="XRTableCell" Name="tableCell16" Weight="0.43813901879359518" Multiline="true" TextAlignment="MiddleLeft" WordWrap="false" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="53" EventName="BeforePrint" PropertyName="Text" Expression="[CantidadPedida]" />
                  </ExpressionBindings>
                  <StylePriority Ref="54" UseBorders="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="55" ControlType="XRTableCell" Name="tableCell14" Weight="0.54896412503567316" Multiline="true" TextAlignment="MiddleRight" WordWrap="false" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="56" EventName="BeforePrint" PropertyName="Text" Expression="[Cantidad]" />
                  </ExpressionBindings>
                  <StylePriority Ref="57" UseBorders="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="58" ControlType="XRTableCell" Name="tableCell15" Weight="0.42328251648295057" Multiline="true" TextAlignment="MiddleRight" WordWrap="false" Borders="None">
                  <ExpressionBindings>
                    <Item1 Ref="59" EventName="BeforePrint" PropertyName="Text" Expression="[Cantidad]" />
                  </ExpressionBindings>
                  <StylePriority Ref="60" UseBorders="false" UseTextAlignment="false" />
                </Item6>
              </Cells>
            </Item1>
          </Rows>
          <ExpressionBindings>
            <Item1 Ref="61" EventName="BeforePrint" PropertyName="Visible" Expression="Not ?VerCostoPromedio" />
          </ExpressionBindings>
          <StylePriority Ref="62" UseBorderColor="false" />
        </Item2>
      </Controls>
    </Item2>
    <Item3 Ref="63" ControlType="BottomMarginBand" Name="bottomMarginBand1" HeightF="116" />
    <Item4 Ref="64" ControlType="PageHeaderBand" Name="PageHeader" HeightF="236.458328">
      <SubBands>
        <Item1 Ref="65" ControlType="SubBand" Name="SubBand1" HeightF="34.79169">
          <Controls>
            <Item1 Ref="66" ControlType="XRTable" Name="table4" SizeF="750,34.79169" LocationFloat="0,0" Padding="2,2,0,0,96">
              <Rows>
                <Item1 Ref="67" ControlType="XRTableRow" Name="tableRow4" Weight="1">
                  <Cells>
                    <Item1 Ref="68" ControlType="XRTableCell" Name="tableCell20" Weight="0.58202045534590741" Multiline="true" Text="Producto" TextAlignment="BottomLeft" WordWrap="false" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="69" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="70" ControlType="XRTableCell" Name="tableCell21" Weight="1.4456234754976689" Multiline="true" Text="Descripción" TextAlignment="BottomLeft" WordWrap="false" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="71" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item2>
                    <Item3 Ref="72" ControlType="XRTableCell" Name="tableCell23" Weight="1.1777153204603366" Multiline="true" Text="Presentación" TextAlignment="BottomLeft" WordWrap="false" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="73" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item3>
                    <Item4 Ref="74" ControlType="XRTableCell" Name="tableCell27" Weight="0.57419003343980535" Multiline="true" Text="Cantidad&#xD;&#xA;Pedida" TextAlignment="BottomLeft" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="75" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item4>
                    <Item5 Ref="76" ControlType="XRTableCell" Name="tableCell28" Weight="0.61944645532780673" Multiline="true" Text="Costo Promedio" TextAlignment="BottomLeft" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="77" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item5>
                    <Item6 Ref="78" ControlType="XRTableCell" Name="tableCell24" Weight="0.50831189152723111" Multiline="true" Text="&#xD;&#xA;Costo Promedio Total" TextAlignment="BottomLeft" WordWrap="false" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="79" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item6>
                    <Item7 Ref="80" ControlType="XRTableCell" Name="tableCell25" Weight="0.63688701456216756" Multiline="true" Text="Cantidad&#xD;&#xA;Despachada" TextAlignment="BottomLeft" WordWrap="false" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="81" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item7>
                    <Item8 Ref="82" ControlType="XRTableCell" Name="tableCell26" Weight="0.49107547229302428" Multiline="true" Text="Cantidad&#xD;&#xA;Recibida" TextAlignment="BottomLeft" WordWrap="false" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="83" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item8>
                  </Cells>
                </Item1>
              </Rows>
              <ExpressionBindings>
                <Item1 Ref="84" EventName="BeforePrint" PropertyName="Visible" Expression="?VerCostoPromedio" />
              </ExpressionBindings>
            </Item1>
            <Item2 Ref="85" ControlType="XRTable" Name="table2" SizeF="750,34.79169" LocationFloat="0,0" Padding="2,2,0,0,96">
              <Rows>
                <Item1 Ref="86" ControlType="XRTableRow" Name="tableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="87" ControlType="XRTableCell" Name="tableCell6" Weight="0.58202045534590741" Multiline="true" Text="Producto" TextAlignment="BottomLeft" WordWrap="false" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="88" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="89" ControlType="XRTableCell" Name="tableCell7" Weight="3.1975288467812364" Multiline="true" Text="Descripción" TextAlignment="BottomLeft" WordWrap="false" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="90" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item2>
                    <Item3 Ref="91" ControlType="XRTableCell" Name="tableCell8" Weight="0.61944643794438148" Multiline="true" Text="Presentación" TextAlignment="BottomLeft" WordWrap="false" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="92" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item3>
                    <Item4 Ref="93" ControlType="XRTableCell" Name="tableCell5" Weight="0.50831189152723111" Multiline="true" Text="Cantidad&#xD;&#xA;Pedida" TextAlignment="BottomLeft" WordWrap="false" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="94" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item4>
                    <Item5 Ref="95" ControlType="XRTableCell" Name="tableCell9" Weight="0.63688701456216756" Multiline="true" Text="Cantidad&#xD;&#xA;Despachada" TextAlignment="BottomLeft" WordWrap="false" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="96" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item5>
                    <Item6 Ref="97" ControlType="XRTableCell" Name="tableCell10" Weight="0.49107547229302428" Multiline="true" Text="Cantidad&#xD;&#xA;Recibida" TextAlignment="BottomLeft" WordWrap="false" Font="Arial, 6.75pt, style=Bold, charSet=0" BackColor="Transparent" Borders="None">
                      <StylePriority Ref="98" UseFont="false" UseBackColor="false" UseBorders="false" UseTextAlignment="false" />
                    </Item6>
                  </Cells>
                </Item1>
              </Rows>
              <ExpressionBindings>
                <Item1 Ref="99" EventName="BeforePrint" PropertyName="Visible" Expression=" Not ?VerCostoPromedio" />
              </ExpressionBindings>
            </Item2>
          </Controls>
        </Item1>
      </SubBands>
      <Controls>
        <Item1 Ref="100" ControlType="XRLabel" Name="label5" CanGrow="false" Text="label5" WordWrap="false" SizeF="397.355347,27.166687" LocationFloat="72.32739,168.75" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="101" EventName="BeforePrint" PropertyName="Text" Expression="Iif(Lower([TipoSolicitud])=='requerimiento',[CorporativoDespacho]+' '+[NombreDespacho],'')&#xA;" />
          </ExpressionBindings>
          <StylePriority Ref="102" UseFont="false" />
        </Item1>
        <Item2 Ref="103" ControlType="XRLabel" Name="label3" CanGrow="false" Text="label3" WordWrap="false" SizeF="72.32739,27.166687" LocationFloat="0,168.75" Font="Arial, 12pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="104" EventName="BeforePrint" PropertyName="Text" Expression="Iif(Lower([TipoSolicitud])=='requerimiento','Usuario Recepción','')" />
          </ExpressionBindings>
          <StylePriority Ref="105" UseFont="false" />
        </Item2>
        <Item3 Ref="106" ControlType="XRLabel" Name="label10" CanGrow="false" TextAlignment="BottomLeft" WordWrap="false" SizeF="397.355347,28.125" LocationFloat="72.32739,140.625" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="107" EventName="BeforePrint" PropertyName="Text" Expression="[Departamento]+ ' '+[DepartamentoNombre]&#xA;" />
          </ExpressionBindings>
          <StylePriority Ref="108" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="109" ControlType="XRLabel" Name="label9" CanGrow="false" Text="Departamento" TextAlignment="BottomLeft" WordWrap="false" SizeF="72.32739,28.125" LocationFloat="0,140.625" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="110" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="111" ControlType="XRLabel" Name="labelObservaciones" CanGrow="false" TextAlignment="TopLeft" WordWrap="false" SizeF="280.317444,28.125" LocationFloat="469.682556,140.625" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="112" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="113" ControlType="XRLabel" Name="label19" CanGrow="false" Text="Observaciones" TextAlignment="BottomLeft" WordWrap="false" SizeF="280.317444,28.125" LocationFloat="469.682556,112.5" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="114" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="115" ControlType="XRLabel" Name="labelEstadoMovimiento" CanGrow="false" TextAlignment="BottomLeft" WordWrap="false" SizeF="203.339111,28.125" LocationFloat="546.6609,84.375" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="116" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="117" ControlType="XRLabel" Name="label17" CanGrow="false" Text="Estado" TextAlignment="BottomLeft" WordWrap="false" SizeF="76.97836,28.125" LocationFloat="469.682648,84.375" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="118" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item8>
        <Item9 Ref="119" ControlType="XRLabel" Name="labelFechaAceptado" CanGrow="false" TextAlignment="BottomLeft" WordWrap="false" SizeF="203.338989,28.125" LocationFloat="546.661,56.25" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="120" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="121" ControlType="XRLabel" Name="label15" CanGrow="false" Text="Acep." TextAlignment="BottomLeft" WordWrap="false" SizeF="76.97836,28.125" LocationFloat="469.682648,56.25" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="122" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item10>
        <Item11 Ref="123" ControlType="XRLabel" Name="labelFechaDespacho" CanGrow="false" TextAlignment="BottomLeft" WordWrap="false" SizeF="203.338989,28.125" LocationFloat="546.661,28.125" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="124" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="125" ControlType="XRLabel" Name="label13" CanGrow="false" Text="Desp." TextAlignment="BottomLeft" WordWrap="false" SizeF="76.97836,28.125" LocationFloat="469.682648,28.125" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="126" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="127" ControlType="XRLabel" Name="labelFechaRegistro" CanGrow="false" TextAlignment="BottomLeft" WordWrap="false" SizeF="203.339111,28.125" LocationFloat="546.6609,0" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="128" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="129" ControlType="XRLabel" Name="label11" CanGrow="false" Text="Reg." TextAlignment="BottomLeft" WordWrap="false" SizeF="76.97836,28.125" LocationFloat="469.682648,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="130" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="131" ControlType="XRLabel" Name="labelABodega" CanGrow="false" TextAlignment="BottomLeft" WordWrap="false" SizeF="397.355347,28.125" LocationFloat="72.32739,112.5" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="132" EventName="BeforePrint" PropertyName="Text" Expression="[BodegaDestino]+ ' '+[DestinoNombre]&#xA;" />
          </ExpressionBindings>
          <StylePriority Ref="133" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item15>
        <Item16 Ref="134" ControlType="XRLabel" Name="labelDeBodega" CanGrow="false" TextAlignment="BottomLeft" WordWrap="false" SizeF="397.355347,28.125" LocationFloat="72.32739,84.375" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="135" EventName="BeforePrint" PropertyName="Text" Expression="[BodegaFuente]+ ' '+[FuenteNombre]" />
          </ExpressionBindings>
          <StylePriority Ref="136" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item16>
        <Item17 Ref="137" ControlType="XRLabel" Name="label8" CanGrow="false" Text="A Bodega" TextAlignment="BottomLeft" WordWrap="false" SizeF="72.32739,28.125" LocationFloat="0,112.5" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="138" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item17>
        <Item18 Ref="139" ControlType="XRLabel" Name="label6" CanGrow="false" Text="De Bodega" TextAlignment="BottomLeft" WordWrap="false" SizeF="72.32739,28.125" LocationFloat="0,84.375" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="140" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item18>
        <Item19 Ref="141" ControlType="XRLabel" Name="labelTipoMovimiento" CanGrow="false" TextAlignment="BottomLeft" WordWrap="false" SizeF="397.355347,28.125" LocationFloat="72.32739,56.25" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="142" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item19>
        <Item20 Ref="143" ControlType="XRLabel" Name="label4" CanGrow="false" Text="Tipo" TextAlignment="BottomLeft" WordWrap="false" SizeF="72.32739,28.125" LocationFloat="0,56.25" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="144" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item20>
        <Item21 Ref="145" ControlType="XRLabel" Name="labelFechaMovimiento" CanGrow="false" TextAlignment="BottomLeft" WordWrap="false" SizeF="397.355347,28.125" LocationFloat="72.32739,28.125" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="146" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item21>
        <Item22 Ref="147" ControlType="XRLabel" Name="label1" CanGrow="false" Text="Fecha" TextAlignment="BottomLeft" WordWrap="false" SizeF="72.32742,28.125" LocationFloat="0,28.125" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <StylePriority Ref="148" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item22>
        <Item23 Ref="149" ControlType="XRLabel" Name="labelMovimiento" CanGrow="false" TextAlignment="BottomLeft" WordWrap="false" SizeF="397.355347,28.125" LocationFloat="72.32739,0" Font="Arial, 12pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="150" UseFont="false" UsePadding="false" UseTextAlignment="false" />
        </Item23>
        <Item24 Ref="151" ControlType="XRLabel" Name="label7" CanGrow="false" TextAlignment="BottomLeft" WordWrap="false" SizeF="72.32739,28.125" LocationFloat="0,0" Font="Arial, 12pt, style=Bold, charSet=0" BackColor="Transparent" Padding="2,2,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="152" EventName="BeforePrint" PropertyName="Text" Expression="[TipoSolicitud]" />
          </ExpressionBindings>
          <StylePriority Ref="153" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item24>
      </Controls>
    </Item4>
    <Item5 Ref="154" ControlType="GroupFooterBand" Name="GroupFooter1" HeightF="23">
      <Controls>
        <Item1 Ref="155" ControlType="XRLabel" Name="label12" Multiline="true" Text="Total del Movimiento" TextAlignment="MiddleRight" SizeF="76.9783,23" LocationFloat="469.682831,0" Font="Times New Roman, 9.75pt, style=Bold" Padding="2,2,0,0,100">
          <StylePriority Ref="156" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="157" ControlType="XRLabel" Name="label14" TextFormatString="{0:c2}" Multiline="true" Text="label14" TextAlignment="MiddleRight" SizeF="63.16754,23" LocationFloat="546.661133,0" Font="Times New Roman, 9.75pt, style=Bold" Padding="2,2,0,0,100">
          <Summary Ref="158" Running="Report" />
          <ExpressionBindings>
            <Item1 Ref="159" EventName="BeforePrint" PropertyName="Text" Expression="sumSum([Costo])" />
          </ExpressionBindings>
          <StylePriority Ref="160" UseFont="false" UseTextAlignment="false" />
        </Item2>
      </Controls>
    </Item5>
  </Bands>
  <ObjectStorage>
    <Item1 ObjectType="DevExpress.XtraReports.Serialization.ObjectStorageInfo, DevExpress.XtraReports.v20.1" Ref="1" Content="System.Boolean" Type="System.Type" />
  </ObjectStorage>
</XtraReportsLayoutSerializer>