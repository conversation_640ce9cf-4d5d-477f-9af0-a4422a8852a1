﻿using Modelo.Conexion;
using Radiologia;
using Reporte.Reporte;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ReporteCons.Models
{
    public class ReporteConsignacion
    {
        private IConexion conexion;

        public ReporteConsignacion()
        {
            String Usuario = Startup.Conexion["Tag_Usuario"].ToString();
            String Password = Startup.Conexion["Tag_Password"].ToString();

            String Instancia = Startup.Conexion["Instancia"].ToString();
            DBManager db = new DBManager(Usuario, Password, Instancia);
            conexion = db.ObtenerConexion();
        }

        public string prueba()
        {
            return "";
        }
        public RptExistenciasProdutosConsignacion RptExistenciasProdutosConsignacion()
        {
            RptExistenciasProdutosConsignacion reporte = new RptExistenciasProdutosConsignacion();
            return reporte;
        }
    }
}
