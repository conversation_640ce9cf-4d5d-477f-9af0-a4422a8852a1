﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="ReporteConsumosPorDepartamento" Margins="71, 86, 100, 100" PaperKind="Custom" PageWidth="1693" PageHeight="1276" Version="20.1" EventsInfo="|Detail,BeforePrint,Detail_BeforePrint" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin">
      <Controls>
        <Item1 Ref="2" ControlType="XRLabel" Name="CodDepartamento" Multiline="true" TextAlignment="TopLeft" SizeF="187.195,14.4999619" LocationFloat="358.468872,24.66669" Font="Arial, 6.5pt" ForeColor="255,64,64,64" Padding="2,2,0,0,100" BorderWidth="0">
          <StylePriority Ref="3" UseFont="false" UseForeColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="4" ControlType="XRLabel" Name="FechaHasta" Multiline="true" Text="(dd/mm/yyyy)" TextAlignment="TopLeft" SizeF="103.779236,14.4999619" LocationFloat="441.884644,39.1666527" Font="Arial, 6.5pt" ForeColor="255,64,64,64" Padding="2,2,0,0,100" BorderWidth="0">
          <StylePriority Ref="5" UseFont="false" UseForeColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="6" ControlType="XRLabel" Name="FechaDesde" Multiline="true" Text="(dd/mm/yyyy)" TextAlignment="TopLeft" SizeF="83.41577,14.4999619" LocationFloat="358.468872,39.1666451" Font="Arial, 6.5pt" ForeColor="255,64,64,64" Padding="2,2,0,0,100" BorderWidth="0">
          <StylePriority Ref="7" UseFont="false" UseForeColor="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="8" ControlType="XRLabel" Name="label1" Multiline="true" CanGrow="false" Text="Periodo:" TextAlignment="TopRight" SizeF="358.468872,14.4999695" LocationFloat="0,39.1666451" Font="Arial, 6.5pt" Padding="2,2,1,0,100" BorderWidth="0">
          <StylePriority Ref="9" UseFont="false" UsePadding="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="10" ControlType="XRLabel" Name="label25" Multiline="true" CanGrow="false" Text="Departamento:" TextAlignment="TopRight" SizeF="358.468781,14.4999695" LocationFloat="0,24.6666832" Font="Arial, 6.5pt" Padding="2,2,1,0,100" BorderWidth="0">
          <StylePriority Ref="11" UseFont="false" UsePadding="false" UseBorderWidth="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="12" ControlType="XRLabel" Name="uiTitulo" Multiline="true" CanShrink="true" Text="Reporte de Consumos por Departamento" TextAlignment="MiddleLeft" SizeF="545.6639,14.6666727" LocationFloat="0,10.0000105" Font="Arial, 10pt, style=Bold" ForeColor="255,0,96,151" Padding="2,2,0,0,100">
          <StylePriority Ref="13" UseFont="false" UseForeColor="false" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="14" ControlType="XRTable" Name="table2" KeepTogether="true" SizeF="1484.38049,19.3340759" LocationFloat="0,80.66592" Padding="0,0,2,2,100" BorderColor="255,0,96,151">
          <Rows>
            <Item1 Ref="15" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="16" ControlType="XRTableCell" Name="tableCell6" Weight="0.29416712276473528" Multiline="true" Text="Requerimiento" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="17" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item1>
                <Item2 Ref="18" ControlType="XRTableCell" Name="tableCell2" Weight="0.27405905607622327" Multiline="true" Text="Producto" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="19" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item2>
                <Item3 Ref="20" ControlType="XRTableCell" Name="tableCell3" Weight="1.6321980299127152" Multiline="true" Text="Nombre Producto" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="21" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item3>
                <Item4 Ref="22" ControlType="XRTableCell" Name="tableCell4" Weight="0.51203994055228275" Multiline="true" Text="Cantidad Aceptada" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="23" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item4>
                <Item5 Ref="24" ControlType="XRTableCell" Name="tableCell5" Weight="0.23087851542651117" Multiline="true" Text="Costo" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="25" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item5>
                <Item6 Ref="26" ControlType="XRTableCell" Name="tableCell7" Weight="0.40615928614493935" Multiline="true" Text="Fecha" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="27" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item6>
                <Item7 Ref="28" ControlType="XRTableCell" Name="tableCell8" Weight="0.26813413873784769" Multiline="true" Text="Bodega" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="29" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item7>
                <Item8 Ref="30" ControlType="XRTableCell" Name="tableCell9" Weight="0.92035162473108156" Multiline="true" Text="Bodega Despacho" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="31" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item8>
                <Item9 Ref="32" ControlType="XRTableCell" Name="tableCell10" Weight="0.22989654874850315" Multiline="true" Text="Depto." Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="33" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item9>
                <Item10 Ref="34" ControlType="XRTableCell" Name="tableCell11" Weight="1.5382796060483519" Multiline="true" Text="Nombre Depto" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="35" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item10>
                <Item11 Ref="36" ControlType="XRTableCell" Name="tableCell12" Weight="0.28760101082132983" Multiline="true" Text="Mes" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="37" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item11>
                <Item12 Ref="38" ControlType="XRTableCell" Name="tableCell13" Weight="0.25235916934704794" Multiline="true" Text="Año" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="39" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item12>
                <Item13 Ref="40" ControlType="XRTableCell" Name="tableCell14" Weight="0.48566095781058582" Multiline="true" Text="Fecha Despacho" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="41" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item13>
                <Item14 Ref="42" ControlType="XRTableCell" Name="tableCell15" Weight="1.3460249192811429" Multiline="true" Text="Recibe Empleado" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="43" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item14>
                <Item15 Ref="44" ControlType="XRTableCell" Name="tableCell16" Weight="0.43390727482005831" Multiline="true" Text="Corporativo" Font="Arial, 6pt, style=Bold, charSet=0" ForeColor="WhiteSmoke" BackColor="255,0,96,151" Padding="4,0,2,2,100" BorderColor="Black" Borders="None">
                  <StylePriority Ref="45" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" />
                </Item15>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="46" UsePadding="false" UseBorderColor="false" />
        </Item7>
      </Controls>
    </Item1>
    <Item2 Ref="47" ControlType="BottomMarginBand" Name="BottomMargin" />
    <Item3 Ref="48" ControlType="DetailBand" Name="Detail" HeightF="18.21">
      <Controls>
        <Item1 Ref="49" ControlType="XRTable" Name="table1" SizeF="1483.94885,18.21" LocationFloat="7.62939453E-06,0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="50" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="51" ControlType="XRTableCell" Name="Producto" Weight="0.14952644038278964" Multiline="true" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="52" EventName="BeforePrint" PropertyName="Text" Expression="[Requerimiento]" />
                  </ExpressionBindings>
                  <StylePriority Ref="53" UseFont="false" />
                </Item1>
                <Item2 Ref="54" ControlType="XRTableCell" Name="NumeroSerie" Weight="0.13930532447025429" Multiline="true" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="55" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="56" UseFont="false" />
                </Item2>
                <Item3 Ref="57" ControlType="XRTableCell" Name="Hospital" Weight="0.82965341227893152" Multiline="true" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="58" EventName="BeforePrint" PropertyName="Text" Expression="[NombreProd]" />
                  </ExpressionBindings>
                  <StylePriority Ref="59" UseFont="false" />
                </Item3>
                <Item4 Ref="60" ControlType="XRTableCell" Name="tableCell17" Weight="0.26027193505139884" Multiline="true" Text="tableCell16" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="61" EventName="BeforePrint" PropertyName="Text" Expression="[CantidadAceptada]" />
                  </ExpressionBindings>
                  <StylePriority Ref="62" UseFont="false" />
                </Item4>
                <Item5 Ref="63" ControlType="XRTableCell" Name="tableCell18" Weight="0.11735657035275665" Multiline="true" Text="tableCell17" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="64" EventName="BeforePrint" PropertyName="Text" Expression="[Costo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="65" UseFont="false" />
                </Item5>
                <Item6 Ref="66" ControlType="XRTableCell" Name="tableCell20" Weight="0.20645274412043832" Multiline="true" Text="tableCell19" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="67" EventName="BeforePrint" PropertyName="Text" Expression="[Fecha]" />
                  </ExpressionBindings>
                  <StylePriority Ref="68" UseFont="false" />
                </Item6>
                <Item7 Ref="69" ControlType="XRTableCell" Name="tableCell21" Weight="0.13629376759006345" Multiline="true" Text="tableCell20" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="70" EventName="BeforePrint" PropertyName="Text" Expression="[Bodega]" />
                  </ExpressionBindings>
                  <StylePriority Ref="71" UseFont="false" />
                </Item7>
                <Item8 Ref="72" ControlType="XRTableCell" Name="tableCell22" Weight="0.46781857788915265" Multiline="true" Text="tableCell21" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="73" EventName="BeforePrint" PropertyName="Text" Expression="[BodegaDespacho]" />
                  </ExpressionBindings>
                  <StylePriority Ref="74" UseFont="false" />
                </Item8>
                <Item9 Ref="75" ControlType="XRTableCell" Name="tableCell23" Weight="0.11685837999676221" Multiline="true" Text="tableCell22" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="76" EventName="BeforePrint" PropertyName="Text" Expression="[Departamento]" />
                  </ExpressionBindings>
                  <StylePriority Ref="77" UseFont="false" />
                </Item9>
                <Item10 Ref="78" ControlType="XRTableCell" Name="tableCell24" Weight="0.78191335773146176" Multiline="true" Text="tableCell24" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="79" EventName="BeforePrint" PropertyName="Text" Expression="[NombreDepto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="80" UseFont="false" />
                </Item10>
                <Item11 Ref="81" ControlType="XRTableCell" Name="tableCell25" Weight="0.14618962224264143" Multiline="true" Text="tableCell25" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="82" EventName="BeforePrint" PropertyName="Text" Expression="[Mes]" />
                  </ExpressionBindings>
                  <StylePriority Ref="83" UseFont="false" />
                </Item11>
                <Item12 Ref="84" ControlType="XRTableCell" Name="tableCell26" Weight="0.12827527856348364" Multiline="true" Text="tableCell26" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="85" EventName="BeforePrint" PropertyName="Text" Expression="[Anio]" />
                  </ExpressionBindings>
                  <StylePriority Ref="86" UseFont="false" />
                </Item12>
                <Item13 Ref="87" ControlType="XRTableCell" Name="tableCell27" Weight="0.24686323917619224" Multiline="true" Text="tableCell27" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="88" EventName="BeforePrint" PropertyName="Text" Expression="[FechaDespacho]" />
                  </ExpressionBindings>
                  <StylePriority Ref="89" UseFont="false" />
                </Item13>
                <Item14 Ref="90" ControlType="XRTableCell" Name="tableCell28" Weight="0.6841900417833775" CanGrow="false" Multiline="true" Text="tableCell28" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="91" EventName="BeforePrint" PropertyName="Text" Expression="[Nombre_Empleado]" />
                  </ExpressionBindings>
                  <StylePriority Ref="92" UseFont="false" />
                </Item14>
                <Item15 Ref="93" ControlType="XRTableCell" Name="tableCell31" Weight="0.21921056515843707" Multiline="true" Text="tableCell31" Font="Arial, 5.25pt, charSet=0">
                  <ExpressionBindings>
                    <Item1 Ref="94" EventName="BeforePrint" PropertyName="Text" Expression="[CorporativoBI]" />
                  </ExpressionBindings>
                  <StylePriority Ref="95" UseFont="false" />
                </Item15>
              </Cells>
            </Item1>
          </Rows>
        </Item1>
      </Controls>
    </Item3>
  </Bands>
</XtraReportsLayoutSerializer>