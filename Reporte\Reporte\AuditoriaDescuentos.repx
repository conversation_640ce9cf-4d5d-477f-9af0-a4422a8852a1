﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="AuditoriaDescuentos" Margins="100, 100, 212, 100" PageWidth="850" PageHeight="1100" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" HeightF="212">
      <Controls>
        <Item1 Ref="2" ControlType="XRLabel" Name="labelEmpresa" Multiline="true" Text="labelEmpresa" TextAlignment="MiddleCenter" SizeF="298.9583,34.3750038" LocationFloat="178.125,48.9583321" Font="Arial, 14.25pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="3" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="4" ControlType="XRLabel" Name="labelUsuario" Multiline="true" Text="labelUsuario&#xD;&#xA;" TextAlignment="MiddleRight" SizeF="153.125,21.8749962" LocationFloat="496.875,48.9583321" Padding="2,2,0,0,100">
          <StylePriority Ref="5" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="6" ControlType="XRLabel" Name="labelFecha" Multiline="true" Text="labelFecha" TextAlignment="MiddleLeft" SizeF="153.125,21.8749981" LocationFloat="0,48.9583321" Padding="2,2,0,0,100">
          <StylePriority Ref="7" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="8" ControlType="XRLabel" Name="label2" Multiline="true" Text="Descuentos" TextAlignment="MiddleCenter" SizeF="300,30.2083359" LocationFloat="178.125,98.9583359" Font="Arial, 14.25pt, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="9" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="10" ControlType="XRLabel" Name="label1" Multiline="true" Text="R=RCupón, E=Empleado, A=Arbitrario,S=Seguro, Q=Paquetes * Porcentaje variable" SizeF="650,25" LocationFloat="0,180.625031" Padding="2,2,0,0,100" />
        <Item6 Ref="11" ControlType="XRLabel" Name="labelFechasParametros" Multiline="true" Text="del [FECHA_INI] al [FECHA_FIN]" TextAlignment="TopLeft" SizeF="214.583267,23" LocationFloat="0,140.708344" Padding="2,2,0,0,100">
          <StylePriority Ref="12" UseTextAlignment="false" />
        </Item6>
      </Controls>
    </Item1>
    <Item2 Ref="13" ControlType="BottomMarginBand" Name="BottomMargin" />
    <Item3 Ref="14" ControlType="DetailBand" Name="Detail" HeightF="40.625">
      <Controls>
        <Item1 Ref="15" ControlType="XRTable" Name="table1" SizeF="650,37.5" LocationFloat="0,1.04166663" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="16" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="17" ControlType="XRTableCell" Name="tableCell1" Weight="0.36057684516967325" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="18" EventName="BeforePrint" PropertyName="Text" Expression="[FechaRegistro]" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="19" ControlType="XRTableCell" Name="tableCell2" Weight="0.39423102333104953" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="20" EventName="BeforePrint" PropertyName="Text" Expression="Concat([Serie],' ',[Codigo])" />
                  </ExpressionBindings>
                </Item2>
                <Item3 Ref="21" ControlType="XRTableCell" Name="tableCell3" Weight="0.35096125899138608" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="22" EventName="BeforePrint" PropertyName="Text" Expression="Concat([SerieAdmision],' ',[Admision])" />
                  </ExpressionBindings>
                </Item3>
                <Item4 Ref="23" ControlType="XRTableCell" Name="tableCell4" Weight="0.19711553672293081" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="24" EventName="BeforePrint" PropertyName="Text" Expression="[TipoDescuento]" />
                  </ExpressionBindings>
                </Item4>
                <Item5 Ref="25" ControlType="XRTableCell" Name="tableCell5" Weight="0.16826916021500321" Multiline="true" TextAlignment="TopCenter">
                  <ExpressionBindings>
                    <Item1 Ref="26" EventName="BeforePrint" PropertyName="Text" Expression="[Seguro]" />
                  </ExpressionBindings>
                  <StylePriority Ref="27" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="28" ControlType="XRTableCell" Name="tableCell6" Weight="0.35576933104795555" TextFormatString="{0:C2}" Multiline="true" TextAlignment="TopCenter">
                  <ExpressionBindings>
                    <Item1 Ref="29" EventName="BeforePrint" PropertyName="Text" Expression="[Descuento]" />
                  </ExpressionBindings>
                  <StylePriority Ref="30" UseTextAlignment="false" />
                </Item6>
                <Item7 Ref="31" ControlType="XRTableCell" Name="tableCell7" Weight="0.23076952820391661" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="32" EventName="BeforePrint" PropertyName="Text" Expression="[UsuarioFactura]" />
                  </ExpressionBindings>
                </Item7>
                <Item8 Ref="33" ControlType="XRTableCell" Name="tableCell16" Weight="0.24519235594753647" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="34" EventName="BeforePrint" PropertyName="Text" Expression="[UsuarioAdmision]" />
                  </ExpressionBindings>
                </Item8>
                <Item9 Ref="35" ControlType="XRTableCell" Name="tableCell18" Weight="0.69711554431107836" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="36" EventName="BeforePrint" PropertyName="Text" Expression="[Nombre]" />
                  </ExpressionBindings>
                </Item9>
              </Cells>
            </Item1>
          </Rows>
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="37" ControlType="PageHeaderBand" Name="PageHeader" HeightF="0">
      <SubBands>
        <Item1 Ref="38" ControlType="SubBand" Name="SubBand1" HeightF="41.6666679">
          <Controls>
            <Item1 Ref="39" ControlType="XRTable" Name="table2" TextAlignment="MiddleLeft" SizeF="650,37.5" LocationFloat="0,1.04166663" Padding="2,2,0,0,96" Borders="Top, Bottom">
              <Rows>
                <Item1 Ref="40" ControlType="XRTableRow" Name="tableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="41" ControlType="XRTableCell" Name="tableCell8" Weight="0.36057684516967325" Multiline="true" Text="Fecha" TextAlignment="MiddleLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" ForeColor="DarkBlue" BackColor="SkyBlue">
                      <StylePriority Ref="42" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item1>
                    <Item2 Ref="43" ControlType="XRTableCell" Name="tableCell9" Weight="0.39423098811845375" Multiline="true" Text="Documento" TextAlignment="MiddleLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" ForeColor="DarkBlue" BackColor="SkyBlue">
                      <StylePriority Ref="44" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item2>
                    <Item3 Ref="45" ControlType="XRTableCell" Name="tableCell10" Weight="0.35096125899138608" Multiline="true" Text="Admisión" TextAlignment="MiddleLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" ForeColor="DarkBlue" BackColor="SkyBlue">
                      <StylePriority Ref="46" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item3>
                    <Item4 Ref="47" ControlType="XRTableCell" Name="tableCell11" Weight="0.19711553672293083" Multiline="true" Text="Tipo" TextAlignment="MiddleLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" ForeColor="DarkBlue" BackColor="SkyBlue">
                      <StylePriority Ref="48" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item4>
                    <Item5 Ref="49" ControlType="XRTableCell" Name="tableCell12" Weight="0.16826912500240748" Multiline="true" Text="Ref" TextAlignment="MiddleLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" ForeColor="DarkBlue" BackColor="SkyBlue">
                      <StylePriority Ref="50" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item5>
                    <Item6 Ref="51" ControlType="XRTableCell" Name="tableCell13" Weight="0.35576933104795561" Multiline="true" Text="Descuento" TextAlignment="MiddleLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" ForeColor="DarkBlue" BackColor="SkyBlue">
                      <StylePriority Ref="52" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item6>
                    <Item7 Ref="53" ControlType="XRTableCell" Name="tableCell14" Weight="0.23076959862910818" Multiline="true" Text="Usr. Caja" TextAlignment="MiddleLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" ForeColor="DarkBlue" BackColor="SkyBlue">
                      <StylePriority Ref="54" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item7>
                    <Item8 Ref="55" ControlType="XRTableCell" Name="tableCell15" Weight="0.24519263556362764" Multiline="true" Text="Usr. Adm " TextAlignment="MiddleLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" ForeColor="DarkBlue" BackColor="SkyBlue">
                      <StylePriority Ref="56" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item8>
                    <Item9 Ref="57" ControlType="XRTableCell" Name="tableCell17" Weight="0.69711542561751938" Multiline="true" Text="A Nombre De" TextAlignment="MiddleLeft" Font="Arial, 9.75pt, style=Bold, charSet=0" ForeColor="DarkBlue" BackColor="SkyBlue">
                      <StylePriority Ref="58" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                    </Item9>
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="59" UseBorders="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </SubBands>
    </Item4>
  </Bands>
</XtraReportsLayoutSerializer>