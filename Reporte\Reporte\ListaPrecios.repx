﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="ListaPrecios" Landscape="true" Margins="20, 0, 0, 0" PaperKind="Custom" PageWidth="2400" PageHeight="850" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" HeightF="0" />
    <Item2 Ref="2" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="0" />
    <Item3 Ref="3" ControlType="DetailBand" Name="Detail" HeightF="25">
      <Controls>
        <Item1 Ref="4" ControlType="XRTable" Name="table1" SizeF="2188.82764,25" LocationFloat="0,0" Font="Arial, 8.25pt, charSet=0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="5" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="6" ControlType="XRTableCell" Name="tableCell1" Weight="1.4489949882065656" Multiline="true" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="7" EventName="BeforePrint" PropertyName="Text" Expression="[DescripcionEmpresa]" />
                  </ExpressionBindings>
                  <StylePriority Ref="8" UseFont="false" />
                </Item1>
                <Item2 Ref="9" ControlType="XRTableCell" Name="tableCell28" Weight="1.2184923893817077" Multiline="true" Text="[Tipo]" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="10" EventName="BeforePrint" PropertyName="Text" Expression="[Tipo]" />
                  </ExpressionBindings>
                  <StylePriority Ref="11" UseFont="false" />
                </Item2>
                <Item3 Ref="12" ControlType="XRTableCell" Name="tableCell30" Weight="1.9663064048339325" Multiline="true" Text="tableCell30" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="13" EventName="BeforePrint" PropertyName="Text" Expression="[Proveedor]" />
                  </ExpressionBindings>
                  <StylePriority Ref="14" UseFont="false" />
                </Item3>
                <Item4 Ref="15" ControlType="XRTableCell" Name="tableCell32" Weight="1.9663064048339325" Multiline="true" Text="tableCell32" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="16" EventName="BeforePrint" PropertyName="Text" Expression="[Documento]" />
                  </ExpressionBindings>
                  <StylePriority Ref="17" UseFont="false" />
                </Item4>
                <Item5 Ref="18" ControlType="XRTableCell" Name="tableCell2" Weight="0.9300329548596119" Multiline="true" TextAlignment="TopCenter" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="19" EventName="BeforePrint" PropertyName="Text" Expression="[Producto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="20" UseFont="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="21" ControlType="XRTableCell" Name="tableCell3" Weight="2.585470433971234" Multiline="true" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="22" EventName="BeforePrint" PropertyName="Text" Expression="[Nombre]" />
                  </ExpressionBindings>
                  <StylePriority Ref="23" UseFont="false" />
                </Item6>
                <Item7 Ref="24" ControlType="XRTableCell" Name="tableCell26" Weight="1.8242156726421479" Multiline="true" TextAlignment="TopCenter" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="25" EventName="BeforePrint" PropertyName="Text" Expression="[Categoria]" />
                  </ExpressionBindings>
                  <StylePriority Ref="26" UseFont="false" UseTextAlignment="false" />
                </Item7>
                <Item8 Ref="27" ControlType="XRTableCell" Name="tableCell20" Weight="1.7229608028730521" Multiline="true" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="28" EventName="BeforePrint" PropertyName="Text" Expression="[SubCategoria]" />
                  </ExpressionBindings>
                  <StylePriority Ref="29" UseFont="false" />
                </Item8>
                <Item9 Ref="30" ControlType="XRTableCell" Name="tableCell4" Weight="1.6097868936149968" TextFormatString="{0:C2}" Multiline="true" Text="[CostoUltimoAnt]" TextAlignment="TopCenter" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="31" EventName="BeforePrint" PropertyName="Text" Expression="[CostoUltimoAnt]" />
                  </ExpressionBindings>
                  <StylePriority Ref="32" UseFont="false" UseTextAlignment="false" />
                </Item9>
                <Item10 Ref="33" ControlType="XRTableCell" Name="tableCell5" Weight="2.5220567043234547" TextFormatString="{0:C2}" Multiline="true" Text="[CostoPromedioAnt]" TextAlignment="TopCenter" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="34" EventName="BeforePrint" PropertyName="Text" Expression="[CostoPromedioAnt]" />
                  </ExpressionBindings>
                  <StylePriority Ref="35" UseFont="false" UseTextAlignment="false" />
                </Item10>
                <Item11 Ref="36" ControlType="XRTableCell" Name="tableCell6" Weight="1.408075981874219" Multiline="true" Text="[Fecha]" TextAlignment="TopCenter" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="37" EventName="BeforePrint" PropertyName="Text" Expression="[Fecha]" />
                  </ExpressionBindings>
                  <StylePriority Ref="38" UseFont="false" UseTextAlignment="false" />
                </Item11>
                <Item12 Ref="39" ControlType="XRTableCell" Name="tableCell7" Weight="2.1252385399480875" Multiline="true" Text="[NivelPrecios]" TextAlignment="TopCenter" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="40" EventName="BeforePrint" PropertyName="Text" Expression="[NivelPrecios]" />
                  </ExpressionBindings>
                  <StylePriority Ref="41" UseFont="false" UseTextAlignment="false" />
                </Item12>
                <Item13 Ref="42" ControlType="XRTableCell" Name="tableCell17" Weight="2.4888856993354027" TextFormatString="{0:C2}" Multiline="true" Text="[CostoUltimoNv]" TextAlignment="TopCenter" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="43" EventName="BeforePrint" PropertyName="Text" Expression="[CostoUltimoNv]" />
                  </ExpressionBindings>
                  <StylePriority Ref="44" UseFont="false" UseTextAlignment="false" />
                </Item13>
                <Item14 Ref="45" ControlType="XRTableCell" Name="tableCell18" Weight="2.8184420824276413" TextFormatString="{0:C2}" Multiline="true" Text="[CostoPromedioNv]" TextAlignment="TopCenter" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="46" EventName="BeforePrint" PropertyName="Text" Expression="[CostoPromedioNv]" />
                  </ExpressionBindings>
                  <StylePriority Ref="47" UseFont="false" UseTextAlignment="false" />
                </Item14>
                <Item15 Ref="48" ControlType="XRTableCell" Name="tableCell23" Weight="1.5609801092053521" Multiline="true" Text="tableCell23" TextAlignment="TopCenter" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="49" EventName="BeforePrint" PropertyName="Text" Expression="[cantidad]" />
                  </ExpressionBindings>
                  <StylePriority Ref="50" UseFont="false" UseTextAlignment="false" />
                </Item15>
                <Item16 Ref="51" ControlType="XRTableCell" Name="tableCell24" Weight="1.7728650313611127" TextFormatString="{0:C2}" Multiline="true" TextAlignment="TopRight" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="52" EventName="BeforePrint" PropertyName="Text" Expression="[Valor]" />
                  </ExpressionBindings>
                  <StylePriority Ref="53" UseFont="false" UseTextAlignment="false" />
                </Item16>
                <Item17 Ref="54" ControlType="XRTableCell" Name="tableCell34" Weight="1.7728650313611127" TextFormatString="{0:C2}" Multiline="true" TextAlignment="TopRight" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="55" EventName="BeforePrint" PropertyName="Text" Expression="[CostoInicial]" />
                  </ExpressionBindings>
                  <StylePriority Ref="56" UseFont="false" UseTextAlignment="false" />
                </Item17>
                <Item18 Ref="57" ControlType="XRTableCell" Name="tableCell38" Weight="1.7728650313611127" TextFormatString="{0:C2}" Multiline="true" TextAlignment="TopRight" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="58" EventName="BeforePrint" PropertyName="Text" Expression="[PrecioInicial]" />
                  </ExpressionBindings>
                  <StylePriority Ref="59" UseFont="false" UseTextAlignment="false" />
                </Item18>
                <Item19 Ref="60" ControlType="XRTableCell" Name="tableCell39" Weight="1.7728650313611127" TextFormatString="{0:N2}" Multiline="true" TextAlignment="TopRight" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="61" EventName="BeforePrint" PropertyName="Text" Expression="[AlertaCosto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="62" UseFont="false" UseTextAlignment="false" />
                </Item19>
                <Item20 Ref="63" ControlType="XRTableCell" Name="tableCell40" Weight="1.7728650313611127" TextFormatString="{0:N2}" Multiline="true" TextAlignment="TopRight" Font="Arial, 8.25pt">
                  <ExpressionBindings>
                    <Item1 Ref="64" EventName="BeforePrint" PropertyName="Text" Expression="[AlertaMargen]" />
                  </ExpressionBindings>
                  <StylePriority Ref="65" UseFont="false" UseTextAlignment="false" />
                </Item20>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="66" UseFont="false" />
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="67" ControlType="ReportHeaderBand" Name="ReportHeader" HeightF="111.004929">
      <Controls>
        <Item1 Ref="68" ControlType="XRLabel" Name="label1" Multiline="true" Text="Reporte Listado de Precios&#xD;&#xA;" TextAlignment="TopCenter" SizeF="2188.827,56.22547" LocationFloat="0,23.95835" Font="Arial, 20pt, style=Bold" ForeColor="255,0,0,192">
          <StylePriority Ref="69" UseFont="false" UseForeColor="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item4>
    <Item5 Ref="70" ControlType="PageHeaderBand" Name="PageHeader" HeightF="35.8700752">
      <Controls>
        <Item1 Ref="71" ControlType="XRTable" Name="table2" TextAlignment="MiddleCenter" SizeF="2188.82739,35.41667" LocationFloat="0.000246365875,0" Font="Arial Black, 10pt" ForeColor="White" BackColor="Gray">
          <Rows>
            <Item1 Ref="72" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="73" ControlType="XRTableCell" Name="tableCell9" Weight="1.4088410556938984" Multiline="true" Text="Empresa" TextAlignment="MiddleLeft" Font="Arial, 10pt, style=Bold">
                  <StylePriority Ref="74" UseFont="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="75" ControlType="XRTableCell" Name="tableCell27" Weight="1.1847220127683773" Multiline="true" Text="Tipo" TextAlignment="MiddleLeft" Font="Arial, 10pt, style=Bold">
                  <StylePriority Ref="76" UseFont="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="77" ControlType="XRTableCell" Name="tableCell29" Weight="1.9118144469735148" Multiline="true" Text="Proveedor" TextAlignment="MiddleLeft" Font="Arial, 10pt, style=Bold">
                  <StylePriority Ref="78" UseFont="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="79" ControlType="XRTableCell" Name="tableCell31" Weight="1.9118144469735148" Multiline="true" Text="Documento" TextAlignment="MiddleLeft" Font="Arial, 10pt, style=Bold">
                  <StylePriority Ref="80" UseFont="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="81" ControlType="XRTableCell" Name="tableCell10" Weight="0.9042558643255344" Multiline="true" Text="Código" TextAlignment="MiddleCenter" Font="Arial Black, 10pt, style=Bold">
                  <StylePriority Ref="82" UseFont="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="83" ControlType="XRTableCell" Name="tableCell11" Weight="2.513819649524581" Multiline="true" Text="Producto" />
                <Item7 Ref="84" ControlType="XRTableCell" Name="tableCell25" Weight="1.7736620994656056" Multiline="true" Text="Categoría" />
                <Item8 Ref="85" ControlType="XRTableCell" Name="tableCell19" Weight="1.6752100524765112" Multiline="true" Text=" Sub Categoría" />
                <Item9 Ref="86" ControlType="XRTableCell" Name="tableCell12" Weight="1.565176495986518" Multiline="true" Text="Costo Ultimo Anterior" />
                <Item10 Ref="87" ControlType="XRTableCell" Name="tableCell13" Weight="2.4521625181630475" Multiline="true" Text="Costo Promedio Anterior" />
                <Item11 Ref="88" ControlType="XRTableCell" Name="tableCell14" Weight="1.3690538576653337" Multiline="true" Text="Fecha" />
                <Item12 Ref="89" ControlType="XRTableCell" Name="tableCell16" Weight="2.0663415121194992" Multiline="true" Text="Nivel de Precios" />
                <Item13 Ref="90" ControlType="XRTableCell" Name="tableCell8" Weight="2.4199107779570266" Multiline="true" Text="Costo Ultimo Nuevo" />
                <Item14 Ref="91" ControlType="XRTableCell" Name="tableCell15" Weight="2.7403341812677806" Multiline="true" Text="Costo Promedio Nuevo" />
                <Item15 Ref="92" ControlType="XRTableCell" Name="tableCell21" Weight="1.5177204344467332" Multiline="true" Text="Cantidad" />
                <Item16 Ref="93" ControlType="XRTableCell" Name="tableCell22" Weight="1.7237339145173913" Multiline="true" Text="Valor" />
                <Item17 Ref="94" ControlType="XRTableCell" Name="tableCell33" Weight="1.7237339145173913" Multiline="true" Text="Costo Inicial" />
                <Item18 Ref="95" ControlType="XRTableCell" Name="tableCell35" Weight="1.7237339145173913" Multiline="true" Text="Precio Inicial" />
                <Item19 Ref="96" ControlType="XRTableCell" Name="tableCell36" Weight="1.7237339145173913" Multiline="true" Text="(CostoUltimoAnt / CostoInicial -1) * 100" />
                <Item20 Ref="97" ControlType="XRTableCell" Name="tableCell37" Weight="1.7237339145173913" Multiline="true" Text="(PrecioInicial / CostoPromedioNv - 1) * 100" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="98" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item5>
  </Bands>
</XtraReportsLayoutSerializer>