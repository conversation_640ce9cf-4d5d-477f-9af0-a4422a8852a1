﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="DevolucionProveedorConsignacion" Margins="100, 100, 156, 100" PaperKind="Custom" PageWidth="2000" PageHeight="1269" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" HeightF="156.25">
      <Controls>
        <Item1 Ref="2" ControlType="XRTable" Name="table2" TextAlignment="BottomLeft" SizeF="1800,34.375" LocationFloat="0,121.875" Font="Arial, 8.25pt, charSet=0" BackColor="SkyBlue" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="3" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="4" ControlType="XRTableCell" Name="tableCell17" Weight="0.60068255583047869" Multiline="true" Text="Codigo" />
                <Item2 Ref="5" ControlType="XRTableCell" Name="tableCell18" Weight="0.64163824112458878" Multiline="true" Text="Fecha" />
                <Item3 Ref="6" ControlType="XRTableCell" Name="tableCell19" Weight="0.71331050290926123" Multiline="true" Text="Proveedor" />
                <Item4 Ref="7" ControlType="XRTableCell" Name="tableCell20" Weight="0.75426618820337166" Multiline="true" Text="Documento" />
                <Item5 Ref="8" ControlType="XRTableCell" Name="tableCell21" Weight="0.713310202941229" Multiline="true" Text="Validacion" />
                <Item6 Ref="9" ControlType="XRTableCell" Name="tableCell22" Weight="0.64163854109262086" Multiline="true" Text="Usuario" />
                <Item7 Ref="10" ControlType="XRTableCell" Name="tableCell23" Weight="3.805461517958229" Multiline="true" Text="Observacion" />
                <Item8 Ref="11" ControlType="XRTableCell" Name="tableCell24" Weight="0.6109222002258563" Multiline="true" Text="Bodega Fuente" />
                <Item9 Ref="12" ControlType="XRTableCell" Name="tableCell25" Weight="0.56996636012952406" Multiline="true" Text="Empresa Real" />
                <Item10 Ref="13" ControlType="XRTableCell" Name="tableCell26" Weight="0.56996696006565872" Multiline="true" Text="Excento" />
                <Item11 Ref="14" ControlType="XRTableCell" Name="tableCell27" Weight="1.5119446829838206" Multiline="true" Text="Recibe" />
                <Item12 Ref="15" ControlType="XRTableCell" Name="tableCell28" Weight="1.8498303637785616" Multiline="true" Text="Nombre" />
                <Item13 Ref="16" ControlType="XRTableCell" Name="tableCell29" Weight="0.63140034937573075" Multiline="true" Text="Nit" />
                <Item14 Ref="17" ControlType="XRTableCell" Name="tableCell30" Weight="2.0634882306186548" Multiline="true" Text="Nombre Bodega" />
                <Item15 Ref="18" ControlType="XRTableCell" Name="tableCell31" Weight="2.1492234099925058" Multiline="true" Text="Responsable" />
                <Item16 Ref="19" ControlType="XRTableCell" Name="tableCell32" Weight="0.69172583159386691" Multiline="true" Text="Estado" />
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="20" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="21" ControlType="XRLabel" Name="labelProveedor" Multiline="true" TextAlignment="BottomLeft" SizeF="764.9833,27.0833282" LocationFloat="0,81.25" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="22" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="23" ControlType="XRLabel" Name="labelFechas" Multiline="true" TextAlignment="BottomLeft" SizeF="764.9833,27.0833321" LocationFloat="0,54.1666679" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="24" UseFont="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="25" ControlType="XRLabel" Name="label2" Multiline="true" Text="Reporte de Devoluciones en Consignación" TextAlignment="BottomLeft" SizeF="764.9833,27.08333" LocationFloat="0,27.083334" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="26" UseFont="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="27" ControlType="XRLabel" Name="label1" Multiline="true" Text="SERVICIOS MEDICOS Y HOSPITALARIOS CENTROAMERICANOS, S.A." TextAlignment="BottomLeft" SizeF="764.9833,27.0833321" LocationFloat="0,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,96">
          <StylePriority Ref="28" UseFont="false" UseTextAlignment="false" />
        </Item5>
      </Controls>
    </Item1>
    <Item2 Ref="29" ControlType="BottomMarginBand" Name="BottomMargin" />
    <Item3 Ref="30" ControlType="DetailBand" Name="Detail" HeightF="34.375">
      <Controls>
        <Item1 Ref="31" ControlType="XRTable" Name="table1" TextAlignment="BottomLeft" SizeF="1800,34.375" LocationFloat="0,0" Font="Arial, 8.25pt, charSet=0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="32" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="33" ControlType="XRTableCell" Name="tableCell1" Weight="0.60068255583047869" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="34" EventName="BeforePrint" PropertyName="Text" Expression="[Codigo]" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="35" ControlType="XRTableCell" Name="tableCell2" Weight="0.64163824112458878" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="36" EventName="BeforePrint" PropertyName="Text" Expression="[Fecha]" />
                  </ExpressionBindings>
                </Item2>
                <Item3 Ref="37" ControlType="XRTableCell" Name="tableCell3" Weight="0.71331050290926123" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="38" EventName="BeforePrint" PropertyName="Text" Expression="[Proveedor]" />
                  </ExpressionBindings>
                </Item3>
                <Item4 Ref="39" ControlType="XRTableCell" Name="tableCell4" Weight="0.75426618820337166" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="40" EventName="BeforePrint" PropertyName="Text" Expression="[Documento]" />
                  </ExpressionBindings>
                </Item4>
                <Item5 Ref="41" ControlType="XRTableCell" Name="tableCell5" Weight="0.713310202941229" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="42" EventName="BeforePrint" PropertyName="Text" Expression="[Validacion]" />
                  </ExpressionBindings>
                </Item5>
                <Item6 Ref="43" ControlType="XRTableCell" Name="tableCell6" Weight="0.64163854109262086" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="44" EventName="BeforePrint" PropertyName="Text" Expression="[Usuario]" />
                  </ExpressionBindings>
                </Item6>
                <Item7 Ref="45" ControlType="XRTableCell" Name="tableCell7" Weight="3.805461517958229" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="46" EventName="BeforePrint" PropertyName="Text" Expression="[Observacion]" />
                  </ExpressionBindings>
                </Item7>
                <Item8 Ref="47" ControlType="XRTableCell" Name="tableCell8" Weight="0.6109222002258563" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="48" EventName="BeforePrint" PropertyName="Text" Expression="[BodegaFuente]" />
                  </ExpressionBindings>
                </Item8>
                <Item9 Ref="49" ControlType="XRTableCell" Name="tableCell9" Weight="0.56996636012952406" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="50" EventName="BeforePrint" PropertyName="Text" Expression="[EmpresaReal]" />
                  </ExpressionBindings>
                </Item9>
                <Item10 Ref="51" ControlType="XRTableCell" Name="tableCell10" Weight="0.56996696006565872" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="52" EventName="BeforePrint" PropertyName="Text" Expression="[Excento]" />
                  </ExpressionBindings>
                </Item10>
                <Item11 Ref="53" ControlType="XRTableCell" Name="tableCell11" Weight="1.5119446829838206" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="54" EventName="BeforePrint" PropertyName="Text" Expression="[Recibe]" />
                  </ExpressionBindings>
                </Item11>
                <Item12 Ref="55" ControlType="XRTableCell" Name="tableCell12" Weight="1.8498303637785616" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="56" EventName="BeforePrint" PropertyName="Text" Expression="[Nombre]" />
                  </ExpressionBindings>
                </Item12>
                <Item13 Ref="57" ControlType="XRTableCell" Name="tableCell13" Weight="0.63140034937573075" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="58" EventName="BeforePrint" PropertyName="Text" Expression="[Nit]" />
                  </ExpressionBindings>
                </Item13>
                <Item14 Ref="59" ControlType="XRTableCell" Name="tableCell14" Weight="2.0634882306186548" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="60" EventName="BeforePrint" PropertyName="Text" Expression="[NombreBodega]" />
                  </ExpressionBindings>
                </Item14>
                <Item15 Ref="61" ControlType="XRTableCell" Name="tableCell15" Weight="2.1492234099925058" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="62" EventName="BeforePrint" PropertyName="Text" Expression="[Responsable]" />
                  </ExpressionBindings>
                </Item15>
                <Item16 Ref="63" ControlType="XRTableCell" Name="tableCell16" Weight="0.69172583159386691" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="64" EventName="BeforePrint" PropertyName="Text" Expression="[Estado]" />
                  </ExpressionBindings>
                </Item16>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="65" UseFont="false" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item3>
  </Bands>
</XtraReportsLayoutSerializer>