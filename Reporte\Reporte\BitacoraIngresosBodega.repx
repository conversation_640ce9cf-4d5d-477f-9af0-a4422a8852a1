﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="20.1.14.0" Ref="0" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v20.1, Version=20.1.14.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="BitacoraIngresosBodega" Landscape="true" PageWidth="1100" PageHeight="850" Version="20.1" Font="Arial, 9.75pt">
  <Bands>
    <Item1 Ref="1" ControlType="TopMarginBand" Name="TopMargin" />
    <Item2 Ref="2" ControlType="BottomMarginBand" Name="BottomMargin" />
    <Item3 Ref="3" ControlType="DetailBand" Name="Detail" HeightF="31.25">
      <Controls>
        <Item1 Ref="4" ControlType="XRTable" Name="table1" TextAlignment="BottomLeft" SizeF="900,31.25" LocationFloat="0,0" Padding="2,2,0,0,96">
          <Rows>
            <Item1 Ref="5" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="6" ControlType="XRTableCell" Name="tableCell1" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="7" EventName="BeforePrint" PropertyName="Text" Expression="[Registro]" />
                  </ExpressionBindings>
                </Item1>
                <Item2 Ref="8" ControlType="XRTableCell" Name="tableCell2" Weight="1" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="9" EventName="BeforePrint" PropertyName="Text" Expression="[Usuario]" />
                  </ExpressionBindings>
                </Item2>
                <Item3 Ref="10" ControlType="XRTableCell" Name="tableCell3" Weight="1.9480001920535628" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="11" EventName="BeforePrint" PropertyName="Text" Expression="[Fecha]" />
                  </ExpressionBindings>
                </Item3>
                <Item4 Ref="12" ControlType="XRTableCell" Name="tableCell4" Weight="0.78399948229913019" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="13" EventName="BeforePrint" PropertyName="Text" Expression="[Status]" />
                  </ExpressionBindings>
                </Item4>
                <Item5 Ref="14" ControlType="XRTableCell" Name="tableCell5" Weight="0.97599988388393377" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="15" EventName="BeforePrint" PropertyName="Text" Expression="[Proveedor]" />
                  </ExpressionBindings>
                </Item5>
                <Item6 Ref="16" ControlType="XRTableCell" Name="tableCell6" Weight="1.6120003242410568" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="17" EventName="BeforePrint" PropertyName="Text" Expression="[Documento]" />
                  </ExpressionBindings>
                </Item6>
                <Item7 Ref="18" ControlType="XRTableCell" Name="tableCell7" Weight="1.588000033072795" TextFormatString="{0:C2}" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="19" EventName="BeforePrint" PropertyName="Text" Expression="[Valor]" />
                  </ExpressionBindings>
                </Item7>
                <Item8 Ref="20" ControlType="XRTableCell" Name="tableCell8" Weight="1.344798949564344" Multiline="true">
                  <ExpressionBindings>
                    <Item1 Ref="21" EventName="BeforePrint" PropertyName="Text" Expression="[Bodega]" />
                  </ExpressionBindings>
                </Item8>
              </Cells>
            </Item1>
          </Rows>
          <StylePriority Ref="22" UseTextAlignment="false" />
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="23" ControlType="PageHeaderBand" Name="PageHeader" HeightF="103.125">
      <SubBands>
        <Item1 Ref="24" ControlType="SubBand" Name="SubBand1" HeightF="48.9583321">
          <Controls>
            <Item1 Ref="25" ControlType="XRTable" Name="table2" TextAlignment="BottomLeft" SizeF="900,48.9583321" LocationFloat="0,0" Font="Arial, 9.75pt, style=Bold, charSet=0" BackColor="SkyBlue" Padding="2,2,0,0,96">
              <Rows>
                <Item1 Ref="26" ControlType="XRTableRow" Name="tableRow2" Weight="1">
                  <Cells>
                    <Item1 Ref="27" ControlType="XRTableCell" Name="tableCell9" Weight="1" Multiline="true" Text="Registro" />
                    <Item2 Ref="28" ControlType="XRTableCell" Name="tableCell10" Weight="1" Multiline="true" Text="Usuario" />
                    <Item3 Ref="29" ControlType="XRTableCell" Name="tableCell11" Weight="1.9923596433105222" Multiline="true" Text="Fecha" />
                    <Item4 Ref="30" ControlType="XRTableCell" Name="tableCell12" Weight="0.79280875927190886" Multiline="true" Text="Status" />
                    <Item5 Ref="31" ControlType="XRTableCell" Name="tableCell13" Weight="0.98696634826587" Multiline="true" Text="Proveedor" />
                    <Item6 Ref="32" ControlType="XRTableCell" Name="tableCell14" Weight="1.6301126476970707" Multiline="true" Text="Documento" />
                    <Item7 Ref="33" ControlType="XRTableCell" Name="tableCell15" Weight="1.6058418291964511" Multiline="true" Text="Valor" />
                    <Item8 Ref="34" ControlType="XRTableCell" Name="tableCell16" Weight="1.3599103356467317" Multiline="true" Text="Bodega" />
                  </Cells>
                </Item1>
              </Rows>
              <StylePriority Ref="35" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
            </Item1>
          </Controls>
        </Item1>
      </SubBands>
      <Controls>
        <Item1 Ref="36" ControlType="XRLabel" Name="labelFechas" Multiline="true" Text="Periodo: Al" TextAlignment="BottomLeft" SizeF="342.708374,34.375" LocationFloat="0,68.75" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="37" UseFont="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="38" ControlType="XRLabel" Name="label2" Multiline="true" Text="Bitácora de Ingresos Incompletos" TextAlignment="BottomLeft" SizeF="342.7084,34.375" LocationFloat="0,34.375" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="39" UseFont="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="40" ControlType="XRLabel" Name="label1" Multiline="true" Text="Grupo Sermesa" TextAlignment="BottomLeft" SizeF="342.708374,34.375" LocationFloat="0,0" Font="Arial, 9.75pt, style=Bold, charSet=0" Padding="2,2,0,0,100">
          <StylePriority Ref="41" UseFont="false" UseTextAlignment="false" />
        </Item3>
      </Controls>
    </Item4>
  </Bands>
</XtraReportsLayoutSerializer>