//------------------------------------------------------------------------------
// <auto-generated>
//     Este código fue generado por una herramienta.
//     Versión de runtime:4.0.30319.42000
//
//     Los cambios en este archivo podrían causar un comportamiento incorrecto y se perderán si
//     se vuelve a generar el código.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Reporte.Reporte {
    
    public partial class MovimientoNotaCredito : DevExpress.XtraReports.UI.XtraReport {
        private void InitializeComponent() {
            DevExpress.XtraReports.ReportInitializer reportInitializer = new DevExpress.XtraReports.ReportInitializer(this, "Reporte.Reporte.MovimientoNotaCredito.repx");

            // Controls
            this.TopMargin = reportInitializer.GetControl<DevExpress.XtraReports.UI.TopMarginBand>("TopMargin");
            this.BottomMargin = reportInitializer.GetControl<DevExpress.XtraReports.UI.BottomMarginBand>("BottomMargin");
            this.Detail = reportInitializer.GetControl<DevExpress.XtraReports.UI.DetailBand>("Detail");
            this.PageHeader = reportInitializer.GetControl<DevExpress.XtraReports.UI.PageHeaderBand>("PageHeader");
            this.GroupFooter1 = reportInitializer.GetControl<DevExpress.XtraReports.UI.GroupFooterBand>("GroupFooter1");
            this.barCode1 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRBarCode>("barCode1");
            this.labelNitEmpresa = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelNitEmpresa");
            this.labelEmprsaSucursal = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelEmprsaSucursal");
            this.labelNombeMovimiento = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelNombeMovimiento");
            this.labelEmpresa = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelEmpresa");
            this.labelFecha = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelFecha");
            this.table1 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRTable>("table1");
            this.tableRow1 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRTableRow>("tableRow1");
            this.tableCell1 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRTableCell>("tableCell1");
            this.tableCell2 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRTableCell>("tableCell2");
            this.tableCell3 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRTableCell>("tableCell3");
            this.tableCell4 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRTableCell>("tableCell4");
            this.tableCell5 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRTableCell>("tableCell5");
            this.tableCell6 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRTableCell>("tableCell6");
            this.labelValidacion = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelValidacion");
            this.label17 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label17");
            this.labelMotivo = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelMotivo");
            this.label15 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label15");
            this.labelExento = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelExento");
            this.labelTextoExento = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelTextoExento");
            this.labelRebaja = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelRebaja");
            this.label13 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label13");
            this.labelValorNota = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelValorNota");
            this.label11 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label11");
            this.labelFactura = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelFactura");
            this.labelProveedor = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelProveedor");
            this.label5 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label5");
            this.label4 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label4");
            this.labelBodega = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelBodega");
            this.labelFechaIngreso = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelFechaIngreso");
            this.label8 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label8");
            this.labelTextFechaIngreso = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelTextFechaIngreso");
            this.labelNFace = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelNFace");
            this.labelFechaRegistro = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelFechaRegistro");
            this.labelTextNface = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelTextNface");
            this.label2 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label2");
            this.labelNumeroNota = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelNumeroNota");
            this.labelTextNota = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelTextNota");
            this.labelCorrelativo = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelCorrelativo");
            this.label7 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label7");
            this.SubBand1 = reportInitializer.GetControl<DevExpress.XtraReports.UI.SubBand>("SubBand1");
            this.table2 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRTable>("table2");
            this.tableRow2 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRTableRow>("tableRow2");
            this.tableCell7 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRTableCell>("tableCell7");
            this.tableCell8 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRTableCell>("tableCell8");
            this.tableCell9 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRTableCell>("tableCell9");
            this.tableCell10 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRTableCell>("tableCell10");
            this.tableCell11 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRTableCell>("tableCell11");
            this.tableCell12 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRTableCell>("tableCell12");
            this.labelNombreEmpleado = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("labelNombreEmpleado");
            this.label12 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label12");
            this.label10 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label10");
            this.label9 = reportInitializer.GetControl<DevExpress.XtraReports.UI.XRLabel>("label9");
        }
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.GroupFooterBand GroupFooter1;
        private DevExpress.XtraReports.UI.XRBarCode barCode1;
        private DevExpress.XtraReports.UI.XRLabel labelNitEmpresa;
        private DevExpress.XtraReports.UI.XRLabel labelEmprsaSucursal;
        private DevExpress.XtraReports.UI.XRLabel labelNombeMovimiento;
        private DevExpress.XtraReports.UI.XRLabel labelEmpresa;
        private DevExpress.XtraReports.UI.XRLabel labelFecha;
        private DevExpress.XtraReports.UI.XRTable table1;
        private DevExpress.XtraReports.UI.XRTableRow tableRow1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell2;
        private DevExpress.XtraReports.UI.XRTableCell tableCell3;
        private DevExpress.XtraReports.UI.XRTableCell tableCell4;
        private DevExpress.XtraReports.UI.XRTableCell tableCell5;
        private DevExpress.XtraReports.UI.XRTableCell tableCell6;
        private DevExpress.XtraReports.UI.XRLabel labelValidacion;
        private DevExpress.XtraReports.UI.XRLabel label17;
        private DevExpress.XtraReports.UI.XRLabel labelMotivo;
        private DevExpress.XtraReports.UI.XRLabel label15;
        private DevExpress.XtraReports.UI.XRLabel labelExento;
        private DevExpress.XtraReports.UI.XRLabel labelTextoExento;
        private DevExpress.XtraReports.UI.XRLabel labelRebaja;
        private DevExpress.XtraReports.UI.XRLabel label13;
        private DevExpress.XtraReports.UI.XRLabel labelValorNota;
        private DevExpress.XtraReports.UI.XRLabel label11;
        private DevExpress.XtraReports.UI.XRLabel labelFactura;
        private DevExpress.XtraReports.UI.XRLabel labelProveedor;
        private DevExpress.XtraReports.UI.XRLabel label5;
        private DevExpress.XtraReports.UI.XRLabel label4;
        private DevExpress.XtraReports.UI.XRLabel labelBodega;
        private DevExpress.XtraReports.UI.XRLabel labelFechaIngreso;
        private DevExpress.XtraReports.UI.XRLabel label8;
        private DevExpress.XtraReports.UI.XRLabel labelTextFechaIngreso;
        private DevExpress.XtraReports.UI.XRLabel labelNFace;
        private DevExpress.XtraReports.UI.XRLabel labelFechaRegistro;
        private DevExpress.XtraReports.UI.XRLabel labelTextNface;
        private DevExpress.XtraReports.UI.XRLabel label2;
        private DevExpress.XtraReports.UI.XRLabel labelNumeroNota;
        private DevExpress.XtraReports.UI.XRLabel labelTextNota;
        private DevExpress.XtraReports.UI.XRLabel labelCorrelativo;
        private DevExpress.XtraReports.UI.XRLabel label7;
        private DevExpress.XtraReports.UI.SubBand SubBand1;
        private DevExpress.XtraReports.UI.XRTable table2;
        private DevExpress.XtraReports.UI.XRTableRow tableRow2;
        private DevExpress.XtraReports.UI.XRTableCell tableCell7;
        private DevExpress.XtraReports.UI.XRTableCell tableCell8;
        private DevExpress.XtraReports.UI.XRTableCell tableCell9;
        private DevExpress.XtraReports.UI.XRTableCell tableCell10;
        private DevExpress.XtraReports.UI.XRTableCell tableCell11;
        private DevExpress.XtraReports.UI.XRTableCell tableCell12;
        private DevExpress.XtraReports.UI.XRLabel labelNombreEmpleado;
        private DevExpress.XtraReports.UI.XRLabel label12;
        private DevExpress.XtraReports.UI.XRLabel label10;
        private DevExpress.XtraReports.UI.XRLabel label9;
    }
}
