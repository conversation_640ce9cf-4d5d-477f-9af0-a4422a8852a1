<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp2.2</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="wwwroot\**" />
    <Content Remove="wwwroot\**" />
    <EmbeddedResource Remove="wwwroot\**" />
    <None Remove="wwwroot\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Models\ReporteKardexProductoBodega.repx" />
    <None Remove="Reporte\AuditoriaDescuentos.repx" />
    <None Remove="Reporte\BitacoraIngresosBodega.repx" />
    <None Remove="Reporte\BitacoraMontosLimite.repx" />
    <None Remove="Reporte\BitacoraMovimientoProducto.repx" />
    <None Remove="Reporte\CargosNoFacturadosPacientes.repx" />
    <None Remove="Reporte\ContrasenaInventario.repx" />
    <None Remove="Reporte\ControlIncidentes.repx" />
    <None Remove="Reporte\ConveniosInactivos.repx" />
    <None Remove="Reporte\ConveniosProveedores.repx" />
    <None Remove="Reporte\DetalleInventario.repx" />
    <None Remove="Reporte\DetalleLote.repx" />
    <None Remove="Reporte\DetalleMovimiento.repx" />
    <None Remove="Reporte\DetalleOrdenCompra.repx" />
    <None Remove="Reporte\DevolucionConsignacion.repx" />
    <None Remove="Reporte\DevolucionProveedorConsignacion.repx" />
    <None Remove="Reporte\EnvioConsignacion.repx" />
    <None Remove="Reporte\EnvioConsignacionOrtopediaPdf.repx" />
    <None Remove="Reporte\ExistenciaPorProducto.repx" />
    <None Remove="Reporte\Honorarios_No_Disponibles - Copia.repx" />
    <None Remove="Reporte\LiquidacionCostoUltimo.repx" />
    <None Remove="Reporte\LiquidacionDetalle.repx" />
    <None Remove="Reporte\LiquidacionDetalleExcel.repx" />
    <None Remove="Reporte\LiquidacionesExcel.repx" />
    <None Remove="Reporte\ListaPrecios.repx" />
    <None Remove="Reporte\MovimientoNotaCredito.repx" />
    <None Remove="Reporte\MovimientoNuevo.repx" />
    <None Remove="Reporte\MovimientosPorBodega.repx" />
    <None Remove="Reporte\MovimientosRequerimientos.repx" />
    <None Remove="Reporte\MovimientosRequerimientosExcel.repx" />
    <None Remove="Reporte\PedidosConsolidados.repx" />
    <None Remove="Reporte\PendienteAceptarExcel.repx" />
    <None Remove="Reporte\PendienteAceptarTexto.repx" />
    <None Remove="Reporte\ProductosSinConvenioProveedor.repx" />
    <None Remove="Reporte\PsicotropicoUnaFamiliaIntegrada.repx" />
    <None Remove="Reporte\PsicotropicoUnProducto.repx" />
    <None Remove="Reporte\PsicotropicoUnProductoIntegrado.repx" />
    <None Remove="Reporte\ReporteCierreExistenciasInventario.repx" />
    <None Remove="Reporte\ReporteConsumosPorDepartamento.repx" />
    <None Remove="Reporte\ReporteDespachosPorBodega.repx" />
    <None Remove="Reporte\ReporteKardexProductoBodega.repx" />
    <None Remove="Reporte\ReporteStatusTecnicosSoporte.repx" />
    <None Remove="Reporte\RequerimientosDespachados.repx" />
    <None Remove="Reporte\RptExistenciasProdutosConsignacion.repx" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DevExpress.AspNetCore.Reporting" Version="20.1.14" />
    <PackageReference Include="DevExpress.CrossPlatform.Printing.DrawingEngine" Version="1.0.12" />
    <PackageReference Include="DevExpress.Document.Processor" Version="20.1.14" />
    <PackageReference Include="DevExpress.Reporting.CodeCompletion" Version="20.1.13" />
    <PackageReference Include="DevExpress.Reporting.Core" Version="20.1.14" />
    <PackageReference Include="Microsoft.AspNetCore.All" Version="2.0.9" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="2.2.3" />
    <PackageReference Include="MySql.Data" Version="8.0.18" />
    <PackageReference Include="Newtonsoft.Json" Version="12.0.2" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="4.4.*" />
    <PackageReference Include="System.ServiceModel.Http" Version="4.5.0" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="4.4.*" />
    <PackageReference Include="System.ServiceModel.Security" Version="4.4.*" />
  </ItemGroup>   

  <ItemGroup>
    <DotNetCliToolReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Tools" Version="2.0.4" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Reporte\BitacoraIngresosBodega.repx" />
    <EmbeddedResource Include="Reporte\BitacoraMontosLimite.repx" />
    <EmbeddedResource Include="Reporte\BitacoraMovimientoProducto.repx" />
    <EmbeddedResource Include="Reporte\CargosNoFacturadosPacientes.repx" />
    <EmbeddedResource Include="Reporte\DetalleInventario.repx" />
    <EmbeddedResource Include="Reporte\EnvioConsignacionOrtopediaPdf.repx" />
    <EmbeddedResource Include="Reporte\ExistenciaPorProducto.repx" />
    <EmbeddedResource Include="Reporte\MovimientoNotaCredito.repx" />
    <EmbeddedResource Include="Reporte\MovimientosPorBodega.repx" />
    <EmbeddedResource Include="Reporte\MovimientosRequerimientosExcel.repx" />
    <EmbeddedResource Include="Reporte\PedidosConsolidados.repx" />
    <EmbeddedResource Include="Reporte\AuditoriaDescuentos.repx" />
    <EmbeddedResource Include="Reporte\CargosProductosConsignacionExcel.repx" />
    <EmbeddedResource Include="Reporte\CargosProductosConsignacionPDF.repx" />
    <EmbeddedResource Include="Reporte\ContrasenaInventario.repx" />
    <EmbeddedResource Include="Reporte\ControlIncidentes.repx" />
    <EmbeddedResource Include="Reporte\ConveniosInactivos.repx" />
    <EmbeddedResource Include="Reporte\ConveniosProveedores.repx" />
    <EmbeddedResource Include="Reporte\DetalleLote.repx" />
    <EmbeddedResource Include="Reporte\DetalleMovimiento.repx" />
    <EmbeddedResource Include="Reporte\DetalleOrdenCompra.repx" />
    <EmbeddedResource Include="Reporte\DevolucionConsignacion.repx" />
    <EmbeddedResource Include="Reporte\DevolucionProveedorConsignacion.repx" />
    <EmbeddedResource Include="Reporte\EnvioConsignacion.repx" />
    <EmbeddedResource Include="Reporte\LiquidacionCostoUltimo.repx" />
    <EmbeddedResource Include="Reporte\LiquidacionDetalle.repx" />
    <EmbeddedResource Include="Reporte\LiquidacionDetalleExcel.repx" />
    <EmbeddedResource Include="Reporte\LiquidacionesExcel.repx" />
    <EmbeddedResource Include="Reporte\ListaPrecios.repx" />
    <EmbeddedResource Include="Reporte\MovimientosRequerimientos.repx" />
    <EmbeddedResource Include="Reporte\MovimientoNuevo.repx" />
    <EmbeddedResource Include="Reporte\PedidoConsignacion.repx" />
    <EmbeddedResource Include="Reporte\PendienteAceptarExcel.repx" />
    <EmbeddedResource Include="Reporte\PendienteAceptarTexto.repx" />
    <EmbeddedResource Include="Reporte\ProductosSinConvenioProveedor.repx" />
    <EmbeddedResource Include="Reporte\PsicotropicoUnaFamiliaIntegrada.repx" />
    <EmbeddedResource Include="Reporte\PsicotropicoUnProducto.repx" />
    <EmbeddedResource Include="Reporte\PsicotropicoUnProductoIntegrado.repx" />
    <EmbeddedResource Include="Reporte\ReporteCierreExistenciasInventario.repx" />
    <EmbeddedResource Include="Reporte\ReporteConsumosPorDepartamento.repx" />
    <EmbeddedResource Include="Reporte\ReporteDespachosPorBodega.repx" />
    <EmbeddedResource Include="Reporte\ReporteKardexProductoBodega.repx" />
    <EmbeddedResource Include="Reporte\ReporteStatusTecnicosSoporte.repx" />
    <EmbeddedResource Include="Reporte\RequerimientosDespachados.repx" />
    <EmbeddedResource Include="Reporte\RptExistenciasProdutosConsignacion.repx" />
  </ItemGroup>

  <ItemGroup>
    <WCFMetadata Include="Connected Services" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Connected Services\" />
    <Folder Include="DLL\" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Conexion">
      <HintPath>Conexion.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Reporte\BitacoraIngresosBodega.cs">
      <DependentUpon>BitacoraIngresosBodega.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\BitacoraIngresosBodega.Designer.cs">
      <DependentUpon>BitacoraIngresosBodega.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\BitacoraMovimientoProducto.cs">
      <DependentUpon>BitacoraMovimientoProducto.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\BitacoraMovimientoProducto.Designer.cs">
      <DependentUpon>BitacoraMovimientoProducto.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\CargosProductosConsignacionExcel.cs">
      <DependentUpon>CargosProductosConsignacionExcel.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\CargosProductosConsignacionExcel.Designer.cs">
      <DependentUpon>CargosProductosConsignacionExcel.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\CargosProductosConsignacionPDF.cs">
      <DependentUpon>CargosProductosConsignacionPDF.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\CargosProductosConsignacionPDF.Designer.cs">
      <DependentUpon>CargosProductosConsignacionPDF.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\DetalleInventario.cs">
      <DependentUpon>DetalleInventario.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\DetalleInventario.Designer.cs">
      <DependentUpon>DetalleInventario.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\EnvioConsignacionOrtopediaPdf.cs">
      <DependentUpon>EnvioConsignacionOrtopediaPdf.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\EnvioConsignacionOrtopediaPdf.Designer.cs">
      <DependentUpon>EnvioConsignacionOrtopediaPdf.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ExistenciaPorProducto.cs">
      <DependentUpon>ExistenciaPorProducto.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ExistenciaPorProducto.Designer.cs">
      <DependentUpon>ExistenciaPorProducto.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\MovimientoNotaCredito.cs">
      <DependentUpon>MovimientoNotaCredito.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\MovimientoNotaCredito.Designer.cs">
      <DependentUpon>MovimientoNotaCredito.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\MovimientosPorBodega.cs">
      <DependentUpon>MovimientosPorBodega.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\MovimientosPorBodega.Designer.cs">
      <DependentUpon>MovimientosPorBodega.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\PedidoConsignacion.cs">
      <DependentUpon>PedidoConsignacion.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\PedidoConsignacion.Designer.cs">
      <DependentUpon>PedidoConsignacion.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\PsicotropicoUnaFamiliaIntegrada.cs">
      <DependentUpon>PsicotropicoUnaFamiliaIntegrada.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\PsicotropicoUnaFamiliaIntegrada.Designer.cs">
      <DependentUpon>PsicotropicoUnaFamiliaIntegrada.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\PsicotropicoUnProducto.cs">
      <DependentUpon>PsicotropicoUnProducto.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\PsicotropicoUnProducto.Designer.cs">
      <DependentUpon>PsicotropicoUnProducto.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\PsicotropicoUnProductoIntegrado.cs">
      <DependentUpon>PsicotropicoUnProductoIntegrado.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\PsicotropicoUnProductoIntegrado.Designer.cs">
      <DependentUpon>PsicotropicoUnProductoIntegrado.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ReporteKardexProductoBodega.cs">
      <DependentUpon>ReporteKardexProductoBodega.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ReporteKardexProductoBodega.Designer.cs">
      <DependentUpon>ReporteKardexProductoBodega.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\AuditoriaDescuentos.cs">
      <DependentUpon>AuditoriaDescuentos.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\AuditoriaDescuentos.Designer.cs">
      <DependentUpon>AuditoriaDescuentos.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\BitacoraMontosLimite.cs">
      <DependentUpon>BitacoraMontosLimite.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\BitacoraMontosLimite.Designer.cs">
      <DependentUpon>BitacoraMontosLimite.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\CargosNoFacturadosPacientes.cs">
      <DependentUpon>CargosNoFacturadosPacientes.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\CargosNoFacturadosPacientes.Designer.cs">
      <DependentUpon>CargosNoFacturadosPacientes.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ContrasenaInventario.cs">
      <DependentUpon>ContrasenaInventario.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ContrasenaInventario.Designer.cs">
      <DependentUpon>ContrasenaInventario.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ControlIncidentes.cs">
      <DependentUpon>ControlIncidentes.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ControlIncidentes.Designer.cs">
      <DependentUpon>ControlIncidentes.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ConveniosInactivos.cs">
      <DependentUpon>ConveniosInactivos.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ConveniosInactivos.Designer.cs">
      <DependentUpon>ConveniosInactivos.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ConveniosProveedores.cs">
      <DependentUpon>ConveniosProveedores.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ConveniosProveedores.Designer.cs">
      <DependentUpon>ConveniosProveedores.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\DetalleLote.cs">
      <DependentUpon>DetalleLote.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\DetalleLote.Designer.cs">
      <DependentUpon>DetalleLote.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\DetalleMovimiento.cs">
      <DependentUpon>DetalleMovimiento.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\DetalleMovimiento.Designer.cs">
      <DependentUpon>DetalleMovimiento.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\DetalleOrdenCompra.cs">
      <DependentUpon>DetalleOrdenCompra.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\DetalleOrdenCompra.Designer.cs">
      <DependentUpon>DetalleOrdenCompra.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\Detalle_Pago - Copia.cs">
      <DependentUpon>Detalle_Pago.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\Detalle_Pago - Copia.Designer.cs">
      <DependentUpon>Detalle_Pago.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\DevolucionConsignacion.cs">
      <DependentUpon>DevolucionConsignacion.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\DevolucionConsignacion.Designer.cs">
      <DependentUpon>DevolucionConsignacion.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\DevolucionProveedorConsignacion.cs">
      <DependentUpon>DevolucionProveedorConsignacion.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\DevolucionProveedorConsignacion.Designer.cs">
      <DependentUpon>DevolucionProveedorConsignacion.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\EnvioConsignacion.cs">
      <DependentUpon>EnvioConsignacion.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\EnvioConsignacion.Designer.cs">
      <DependentUpon>EnvioConsignacion.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\LiquidacionCostoUltimo.cs">
      <DependentUpon>LiquidacionCostoUltimo.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\LiquidacionCostoUltimo.Designer.cs">
      <DependentUpon>LiquidacionCostoUltimo.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\LiquidacionDetalle.cs">
      <DependentUpon>LiquidacionDetalle.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\LiquidacionDetalle.Designer.cs">
      <DependentUpon>LiquidacionDetalle.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\LiquidacionDetalleExcel.cs">
      <DependentUpon>LiquidacionDetalleExcel.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\LiquidacionDetalleExcel.Designer.cs">
      <DependentUpon>LiquidacionDetalleExcel.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\LiquidacionesExcel.cs">
      <DependentUpon>LiquidacionesExcel.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\LiquidacionesExcel.Designer.cs">
      <DependentUpon>LiquidacionesExcel.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ListaPrecios.cs">
      <DependentUpon>ListaPrecios.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ListaPrecios.Designer.cs">
      <DependentUpon>ListaPrecios.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\Memo - Copia.cs">
      <DependentUpon>Memo.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\Memo - Copia.Designer.cs">
      <DependentUpon>Memo.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\Honorarios_No_Disponibles - Copia.cs">
      <DependentUpon>Honorarios_No_Disponibles.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\Honorarios_No_Disponibles - Copia.Designer.cs">
      <DependentUpon>Honorarios_No_Disponibles.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\Memo - Copia.cs">
      <DependentUpon>Memo.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\Memo - Copia.Designer.cs">
      <DependentUpon>Memo.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\MovimientoNuevo.cs">
      <DependentUpon>MovimientoNuevo.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\MovimientoNuevo.Designer.cs">
      <DependentUpon>MovimientoNuevo.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\MovimientosRequerimientos.cs">
      <DependentUpon>MovimientosRequerimientos.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\MovimientosRequerimientos.Designer.cs">
      <DependentUpon>MovimientosRequerimientos.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\MovimientosRequerimientosExcel.cs">
      <DependentUpon>MovimientosRequerimientosExcel.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\MovimientosRequerimientosExcel.Designer.cs">
      <DependentUpon>MovimientosRequerimientosExcel.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\PedidosConsolidados.cs">
      <DependentUpon>PedidosConsolidados.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\PedidosConsolidados.Designer.cs">
      <DependentUpon>PedidosConsolidados.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\PendienteAceptarExcel.cs">
      <DependentUpon>PendienteAceptarExcel.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\PendienteAceptarExcel.Designer.cs">
      <DependentUpon>PendienteAceptarExcel.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\PendienteAceptarTexto.cs">
      <DependentUpon>PendienteAceptarTexto.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\PendienteAceptarTexto.Designer.cs">
      <DependentUpon>PendienteAceptarTexto.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ProductosSinConvenioProveedor.cs">
      <DependentUpon>ProductosSinConvenioProveedor.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ProductosSinConvenioProveedor.Designer.cs">
      <DependentUpon>ProductosSinConvenioProveedor.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ReporteCierreExistenciasInventario.cs">
      <DependentUpon>ReporteCierreExistenciasInventario.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ReporteCierreExistenciasInventario.Designer.cs">
      <DependentUpon>ReporteCierreExistenciasInventario.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ReporteConsumosPorDepartamento.cs">
      <DependentUpon>ReporteConsumosPorDepartamento.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ReporteConsumosPorDepartamento.Designer.cs">
      <DependentUpon>ReporteConsumosPorDepartamento.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ReporteDespachosPorBodega.cs">
      <DependentUpon>ReporteDespachosPorBodega.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ReporteDespachosPorBodega.Designer.cs">
      <DependentUpon>ReporteDespachosPorBodega.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ReporteStatusTecnicosSoporte.cs">
      <DependentUpon>ReporteStatusTecnicosSoporte.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\ReporteStatusTecnicosSoporte.Designer.cs">
      <DependentUpon>ReporteStatusTecnicosSoporte.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\RequerimientosDespachados.cs">
      <DependentUpon>RequerimientosDespachados.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\RequerimientosDespachados.Designer.cs">
      <DependentUpon>RequerimientosDespachados.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\RptExistenciasProdutosConsignacion.cs">
      <DependentUpon>RptExistenciasProdutosConsignacion.repx</DependentUpon>
    </Compile>
    <Compile Update="Reporte\RptExistenciasProdutosConsignacion.Designer.cs">
      <DependentUpon>RptExistenciasProdutosConsignacion.repx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ProjectExtensions><VisualStudio><UserProperties Properties_4launchSettings_1json__JSONSchema="" /></VisualStudio></ProjectExtensions>

</Project>
