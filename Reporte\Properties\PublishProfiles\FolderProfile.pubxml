<?xml version="1.0" encoding="utf-8"?>
<!--
Este archivo se usa en el proceso de publicación y empaquetado del proyecto web. Para personalizar el comportamiento de este proceso,
edite el archivo MSBuild. Visite https://go.microsoft.com/fwlink/?LinkID=208121 para obtener más información. 
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>FileSystem</WebPublishMethod>
    <PublishProvider>FileSystem</PublishProvider>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish />
    <LaunchSiteAfterPublish>True</LaunchSiteAfterPublish>
    <ExcludeApp_Data>False</ExcludeApp_Data>
    <ProjectGuid>45ff06c9-ea6a-403e-bab5-548a038901d5</ProjectGuid>
    <publishUrl>C:\Users\<USER>\Documents\Migración\Reporteria\v2-reporte-radiología\Release</publishUrl>
    <DeleteExistingFiles>True</DeleteExistingFiles>
    <TargetFramework>netcoreapp2.2</TargetFramework>
    <SelfContained>false</SelfContained>
    <_IsPortable>true</_IsPortable>
  </PropertyGroup>
</Project>